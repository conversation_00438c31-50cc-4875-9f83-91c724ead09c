{"name": "raeda-ai-mobile-app", "license": "0BSD", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@react-navigation/stack": "^7.2.10", "axios": "^1.8.4", "expo": "~52.0.46", "expo-status-bar": "~2.0.1", "i18next": "^25.2.0", "lucide-react-native": "^0.488.0", "react": "18.3.1", "react-i18next": "^15.5.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-localize": "^3.4.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-webview": "^13.13.5", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "@types/react-native": "^0.72.8", "typescript": "^5.8.3"}, "private": true}