import { StyleSheet } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { lightTheme, darkTheme } from '../theme/theme';

export const useThemedStyles = <T extends StyleSheet.NamedStyles<T>>(
  styleFactory: (theme: typeof lightTheme) => T
) => {
  const { isDark } = useTheme();
  const theme = isDark ? darkTheme : lightTheme;
  return StyleSheet.create(styleFactory(theme));
};