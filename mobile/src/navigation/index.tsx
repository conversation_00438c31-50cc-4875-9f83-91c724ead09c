import React, { useEffect } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import UserScreen from "../screens/UserScreen.native";
import LoginScreen from "../screens/LoginScreen.native";
import ForgotPasswordScreen from "../screens/ForgetPasswordScreen.native";
import ResetPasswordScreen from "../screens/ResetPasswordScreen.native";
import { useAuthStore } from "../contexts/AuthContext";
import { useTheme } from "../contexts/ThemeContext";
import { colors } from "../theme/colors";
import { useTranslation } from "react-i18next";
import { WorkoutProvider } from "../contexts/WorkoutContext";
export type RootStackParamList = {
  Home: undefined;
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { email: string };
  User: { userId: string };
};

type UserScreenNavigationProps = NativeStackScreenProps<
  RootStackParamList,
  "User"
>;

const UserScreenWrapper = ({
  route,
  navigation,
}: UserScreenNavigationProps) => {
  const { userId } = route.params;
  const logout = useAuthStore((state: any) => state.logout);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Error during logout:", error);
    }
  };

  return (
    <>
      <WorkoutProvider>
        <UserScreen userId={userId} onLogout={handleLogout} />
      </WorkoutProvider>
    </>
  );
};

const Stack = createNativeStackNavigator<RootStackParamList>();

const AppNavigator = () => {
  const { t } = useTranslation();
  const { isDark } = useTheme();

  const isLoading = useAuthStore((state) => state.isLoading);
  const userToken = useAuthStore((state) => state.userToken);
  const userId = useAuthStore((state) => state.userId);
  const initialize = useAuthStore((state) => state.initialize);

  useEffect(() => {
    initialize();
  }, [initialize]);

  if (isLoading) {
    return null;
  }

  return (
    <NavigationContainer>
      <Stack.Navigator>
        {userToken && userId ? (
          <Stack.Screen
            name="User"
            component={UserScreenWrapper}
            initialParams={{ userId }}
            options={{ headerShown: false }}
          />
        ) : (
          <>
            <Stack.Screen
              name="Login"
              component={LoginScreen}
              options={{ headerShown: false }}
            />
            {/* <Stack.Screen
              name="Register"
              component={RegisterScreen}
              options={{
                title: t("forgotPassword.backToLogin"),
                headerStyle: {
                  backgroundColor: colors[isDark ? "dark" : "light"].background,
                },
                headerTintColor:
                  colors[isDark ? "dark" : "light"].Buttonprimary,
              }}
            /> */}
            <Stack.Screen
              name="ForgotPassword"
              component={ForgotPasswordScreen}
              options={{
                title: t("forgotPassword.backToLogin"),
                headerStyle: {
                  backgroundColor: colors[isDark ? "dark" : "light"].background,
                },
                headerTintColor:
                  colors[isDark ? "dark" : "light"].Buttonprimary,
              }}
            />
            <Stack.Screen
              name="ResetPassword"
              component={ResetPasswordScreen}
              options={{
                title: t("forgotPassword.backToLogin"),
                headerStyle: {
                  backgroundColor: colors[isDark ? "dark" : "light"].background,
                },
                headerTintColor:
                  colors[isDark ? "dark" : "light"].Buttonprimary,
              }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
