import http from "../services/http";

export const trainingPlansApi = {
  create: async (data: any) => {
    return http.post(`/training-plans`, data).then((res) => res.data);
  },

  getById: async (id: string) => {
    return http.get(`/training-plans/user/${id}`).then((res) => res.data);
  },

  getByTrainingId: async (id: string | null) => {
    return http.get(`/training-plans/${id}`).then((res) => res.data);
  },

  delete: async (id: string) => {
    return http.delete(`/training-plans/${id}`).then((res) => res.data);
  },
};
