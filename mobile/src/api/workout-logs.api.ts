import http from "../services/http";
import { WorkoutLog } from "../types/workout";

function handleAxiosError(err: any): never {
  if (err.isAxiosError && err.response) {
    const msg = err.response.data?.message ?? "An unexpected error occurred";
    throw new Error(msg);
  }
  throw new Error("An error occurred, please try again.");
}

export const workoutLogsApi = {
  getAll: async (): Promise<WorkoutLog[]> => {
    try {
      const res = await http.get<WorkoutLog[]>("/workout-logs");
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  getByUser: async (userId: string): Promise<WorkoutLog[]> => {
    try {
      const res = await http.get<WorkoutLog[]>(`/workout-logs/user/${userId}`);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  create: async (data: any): Promise<WorkoutLog> => {
    try {
      const res = await http.post<WorkoutLog>("/workout-logs", data);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  update: async (id: string, data: any): Promise<WorkoutLog> => {
    try {
      const res = await http.put<WorkoutLog>(`/workout-logs/${id}`, data);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  complete: async (id: string): Promise<WorkoutLog> => {
    try {
      const res = await http.put<WorkoutLog>(`/workout-logs/${id}/complete`);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  remove: async (id: string): Promise<void> => {
    try {
      await http.delete(`/workout-logs/${id}`);
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  getLastWeekByUser: async (userId: string): Promise<WorkoutLog[]> => {
    try {
      const res = await http.get<WorkoutLog[]>(
        `/workout-logs/user/${userId}/last-week`
      );
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },
};
