import http from "../services/http";

export interface LoginData {
  email: string;
  password?: string;
}

export interface LoginResponse {
  message: string;
  jwtToken: string;
  user: {
    id: string;
    email: string;
    name: string;
    isActive: boolean;
    role: {
      id: number;
      name: string;
    };
  };
}

export interface RegisterData {
  name?: string;
  email: string;
  password?: string;
  role: "trainer" | "trainee";
}

export interface RegisterResponse {
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: "trainer" | "trainee";
  };
}

export const loginApi = {
  login: async (userData: LoginData): Promise<LoginResponse> => {
    try {
      const response = await http.post("/auth/login", userData);
      return response.data;
    } catch (error: any) {
      console.error("Login API error:", error.response?.data || error.message);
      throw error.response?.data || error;
    }
  },
};

export const registerApi = {
  register: async (userData: RegisterData): Promise<RegisterResponse> => {
    try {
      const response = await http.post("/auth/register", userData);
      return response.data;
    } catch (error: any) {
      console.error(
        "Register API error:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },
};
