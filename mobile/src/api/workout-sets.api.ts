import http from "../services/http";
import { WorkoutSet } from "../types/workout";

function handleAxiosError(err: any): never {
  if (err.isAxiosError && err.response) {
    const msg = err.response.data?.message ?? "An unexpected error occurred";
    throw new Error(msg);
  }
  throw new Error("An error occurred, please try again.");
}

export const workoutSetsApi = {
  getAll: async (): Promise<WorkoutSet[]> => {
    try {
      const res = await http.get<WorkoutSet[]>("/workout-sets");
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  getByUser: async (userId: string): Promise<WorkoutSet[]> => {
    try {
      const res = await http.get<WorkoutSet[]>(`/workout-sets/user/${userId}`);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  create: async (data: any): Promise<WorkoutSet> => {
    try {
      const res = await http.post<WorkoutSet>("/workout-sets", data);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  update: async (id: string, data: any): Promise<WorkoutSet> => {
    try {
      const res = await http.put<WorkoutSet>(`/workout-sets/${id}`, data);
      return res.data;
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },

  remove: async (id: string): Promise<void> => {
    try {
      await http.delete(`/workout-sets/${id}`);
    } catch (err: any) {
      console.error(err);
      handleAxiosError(err);
    }
  },
};
