import React from "react";
import { View, ActivityIndicator, StyleSheet, Modal } from "react-native";
import { useTheme } from "../../contexts/ThemeContext";

const Loader = ({ visible = false }) => {
  const { isDark } = useTheme();

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <ActivityIndicator size="large" color={isDark ? "#E9D5FF" : "#3B82F6"} />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
});

export default Loader;
