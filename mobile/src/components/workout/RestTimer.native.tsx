import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Pause, Play, SkipForward } from "lucide-react-native";
import { useTranslation } from "react-i18next";

interface RestTimerProps {
  time: number;
  onSkip: () => void;
  toggleTimerPause: (paused: boolean) => void;
  isPaused: boolean;
}

export const RestTimer: React.FC<RestTimerProps> = ({
  time,
  onSkip,
  toggleTimerPause,
  isPaused,
}) => {
  const { t } = useTranslation();

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <View style={styles.overlay}>
      <Text style={styles.timerText}>
        {t("workoutLogger.restTime")}{" "}
        {isPaused ? `(${t("workoutLogger.paused")})` : ""}
      </Text>
      <Text style={styles.timeDisplay}>{formatTime(time)}</Text>
      <View style={styles.buttons}>
        <TouchableOpacity style={styles.button} onPress={onSkip}>
          <SkipForward color="#FFFFFF" width={20} height={20} />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.button}
          onPress={() => toggleTimerPause(!isPaused)}
        >
          {isPaused ? (
            <Play color="#FFFFFF" width={20} height={20} />
          ) : (
            <Pause color="#FFFFFF" width={20} height={20} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  overlay: {
    position: "absolute",
    bottom: 10,
    left: "30%",
    right: 30,
    width: "50%",
    backgroundColor: "rgba(0,0,0,1)",
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOpacity: 0.2,
    shadowRadius: 4,
    shadowOffset: { width: 0, height: 2 },
  },
  timerText: {
    fontSize: 10,
    fontWeight: "600",
    color: "#FFFFFF",
    marginBottom: 2,
    letterSpacing: 0.5,
  },
  timeDisplay: {
    fontSize: 18,
    fontWeight: "700",
    color: "#FFFFFF",
    marginBottom: 8,
  },
  buttons: {
    flexDirection: "row",
  },
  button: {
    backgroundColor: "#3B82F6",
    padding: 6,
    borderRadius: 8,
    marginHorizontal: 4,
    alignItems: "center",
    justifyContent: "center",
  },
});
