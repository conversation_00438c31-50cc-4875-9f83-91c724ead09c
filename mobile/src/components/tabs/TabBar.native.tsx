import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  I18nManager,
} from "react-native";
import { useTheme } from "../../contexts/ThemeContext";
import {
  LayoutGrid as Layout,
  Calendar,
  Dumbbell,
  ClipboardList,
} from "lucide-react-native";
import { useTranslation } from "react-i18next";

interface TabBarProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export const TabBar: React.FC<TabBarProps> = ({ activeTab, onTabChange }) => {
  const { isDark } = useTheme();
  const { t } = useTranslation();
  const styles = createStyles(isDark ? "dark" : "light");

  const tabs = [
    { id: "overview", label: t("userScreen.tabs.overview"), Icon: Layout },
    { id: "meal-plan", label: t("userScreen.tabs.mealPlan"), Icon: Calendar },
    {
      id: "workout-plan",
      label: t("userScreen.tabs.workoutPlan"),
      Icon: Dumbbell,
    },
    {
      id: "workout-logger",
      label: t("userScreen.tabs.workoutLogger"),
      Icon: ClipboardList,
    },
  ];

  return (
    <View style={styles.outerContainer}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.container}
        style={styles.scrollView}
      >
        {tabs.map(({ id, label, Icon }) => {
          const isActive = activeTab === id;
          return (
            <TouchableOpacity
              key={id}
              style={[styles.tab, isActive && styles.activeTab]}
              onPress={() => onTabChange(id)}
            >
              <View
                style={[
                  styles.tabContent,
                  { flexDirection: I18nManager.isRTL ? "row-reverse" : "row" },
                ]}
              >
                <Icon
                  width={16}
                  height={16}
                  color={
                    isActive
                      ? isDark
                        ? "#C4B5FD"
                        : "#FFFFFF"
                      : isDark
                      ? "#9CA3AF"
                      : "#6B7280"
                  }
                  style={isActive ? styles.activeIcon : styles.icon}
                />
                <Text
                  style={[styles.tabText, isActive && styles.activeTabText]}
                >
                  {label}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </ScrollView>
    </View>
  );
};

const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    outerContainer: {
      backgroundColor:
        theme === "dark"
          ? "rgba(31, 41, 55, 1)" // from-gray-800 to-gray-900
          : "#F9FAFB",
      borderBottomWidth: 1,
      borderBottomColor: theme === "dark" ? "#374151" : "#E5E7EB",
    },
    scrollView: {
      flexGrow: 0,
      width: "100%",
    },
    container: {
      flexDirection: "row",
      paddingVertical: 4,
      paddingHorizontal: 8,
      gap: 4,
      flexWrap: "nowrap",
    },
    tab: {
      paddingVertical: 8,
      paddingHorizontal: 10,
      borderRadius: 8,
      backgroundColor: "transparent",
      marginVertical: 4,
    },
    tabContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    activeTab: {
      backgroundColor:
        theme === "dark"
          ? "rgba(109, 40, 217, 1)" // from-purple-700 to-purple-800
          : "rgba(59, 130, 246, 1)", // from-blue-500 to-blue-600
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 3,
    },
    tabText: {
      fontSize: 12,
      fontWeight: "600",
      marginHorizontal: 6,
      color: theme === "dark" ? "#9CA3AF" : "#6B7280",
    },
    activeTabText: {
      color: "#FFFFFF",
    },
    icon: {
      // No transform for inactive icons
    },
    activeIcon: {
      transform: [{ rotate: "12deg" }],
    },
  });
