import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { colors } from '../../theme/colors';

interface OverviewProps {
    engagementRate: number;
    userOverview: {
        profileImage?: any;
        userName: string;
        goal: string;
        achievements?: string[];
    };
}

export const Overview: React.FC<OverviewProps> = ({ engagementRate, userOverview }) => {
    const { isDark } = useTheme();
    const styles = createStyles(isDark ? "dark" : "light");

    return (
        <View style={styles.container}>
            <Text style={styles.title}>Welcome, {userOverview.userName}</Text>
            <Text style={styles.subtitle}>Goal: {userOverview.goal}</Text>
            <Text style={styles.engagement}>Engagement Rate: {engagementRate}%</Text>

            {userOverview.achievements && (
                <View style={styles.achievements}>
                    <Text style={styles.sectionTitle}>Achievements:</Text>
                    {userOverview.achievements.map((item, index) => (
                        <Text key={index} style={styles.achievementItem}>
                            • {item}
                        </Text>
                    ))}
                </View>
            )}
        </View>
    );
};

export const createStyles = (theme: "light" | "dark") =>
    StyleSheet.create({
        container: {
            paddingTop: 12,
            backgroundColor: colors[theme].cardBackground,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.05,
            shadowRadius: 4,
            elevation: 2,
        },
        title: {
            fontSize: 20,
            fontWeight: '700',
            color: theme === "dark" ? "#FFFFFF" : "#111827", // White for dark mode
            marginBottom: 8,
        },
        subtitle: {
            fontSize: 16,
            color: theme === "dark" ? "#D1D5DB" : "#374151", // Gray-300 for dark mode
            marginBottom: 8,
        },
        engagement: {
            fontSize: 14,
            color: theme === "dark" ? "#9CA3AF" : "#6B7280", // Gray-400 for dark mode
            marginBottom: 12,
        },
        achievements: {
            marginTop: 12,
        },
        sectionTitle: {
            fontSize: 16,
            fontWeight: '600',
            color: theme === "dark" ? "#FFFFFF" : "#111827", // White for dark mode
            marginBottom: 4,
        },
        achievementItem: {
            fontSize: 14,
            color: theme === "dark" ? "#D1D5DB" : "#374151", // Gray-300 for dark mode
            marginLeft: 8,
            marginBottom: 2,
        },
    });
