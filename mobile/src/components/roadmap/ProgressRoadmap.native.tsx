import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Map } from 'lucide-react-native';

interface Step {
  label: string;
  completed: boolean;
}

interface ProgressRoadmapProps {
  path: Step[];
}

export const ProgressRoadmap: React.FC<ProgressRoadmapProps> = ({ path }) => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Map color="#3B82F6" width={24} height={24} />
        <Text style={styles.headerText}>Progress Map</Text>
      </View>
      {path.map((step, index) => (
        <View key={index} style={styles.stepContainer}>
          <View style={[styles.statusDot, step.completed && styles.completedDot]} />
          <Text style={[styles.stepLabel, step.completed && styles.completedText]}>
            {step.label}
          </Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    marginLeft: 8,
    color: '#111827',
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#E5E7EB',
    marginRight: 12,
  },
  completedDot: {
    backgroundColor: '#3B82F6',
  },
  stepLabel: {
    fontSize: 14,
    color: '#374151',
  },
  completedText: {
    fontWeight: '600',
    color: '#10B981',
  },
});
