import React from "react";
import { View, Text } from "react-native";
// import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react-native";
import { MacroRange } from "../../types/meal";
import { useTheme } from "../../contexts/ThemeContext";
import { createStyles } from "../../style/UserMeal.style";
// import { colors as themeColors } from "../../theme/colors";
import { useTranslation } from "react-i18next";

export const MacroDisplay = ({
  macroRange,
  // title,
  // showTitle = true,
}: {
  macroRange: MacroRange;
  title?: string;
  showTitle?: boolean;
}) => {
  const { t } = useTranslation();

  const formatNumber = (num: number) => Math.round(num);
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  return (
    <View style={styles.macroDisplayContainer}>

      <View style={styles.macroGrid}>
        <View style={styles.macroColumn}>
          <View style={styles.macroItemProtein}>
            <Text style={styles.macroItemValue}>
              {t("mealPlan.protein").replace(
                "{{value}}",
                `${t("units.grams", {
                  value: formatNumber(macroRange.min.protein),
                })} - ${t("units.grams", {
                  value: formatNumber(macroRange.max.protein),
                })}`
              )}
            </Text>
          </View>

          <View style={styles.macroItemCarbs}>
            <Text style={styles.macroItemValue}>
              {t("mealPlan.carbs").replace(
                "{{value}}",
                `${t("units.grams", {
                  value: formatNumber(macroRange.min.carbs),
                })} - ${t("units.grams", {
                  value: formatNumber(macroRange.max.carbs),
                })}`
              )}
            </Text>
          </View>
        </View>

        <View style={styles.macroColumn}>
          <View style={styles.macroItemFats}>
            <Text style={styles.macroItemValue}>
              {t("mealPlan.fats").replace(
                "{{value}}",
                `${t("units.grams", {
                  value: formatNumber(macroRange.min.fats),
                })} - ${t("units.grams", {
                  value: formatNumber(macroRange.max.fats),
                })}`
              )}
            </Text>
          </View>

          <View style={styles.macroItemCalories}>
            <Text style={styles.macroItemValue}>
              {t("mealPlan.calories").replace(
                "{{value}}",
                `${formatNumber(macroRange.min.calories)} - ${formatNumber(macroRange.max.calories)}`
              )}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};
