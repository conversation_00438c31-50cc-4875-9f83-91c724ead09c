import React, { createContext, useContext } from 'react';

const dummyMealPlan = {
  meals: [
    {
      meal: 'Breakfast',
      categories: [
        {
          name: 'Protein',
          options: [{ foodId: 'chicken', amount: 100 }]
        },
        {
          name: 'Carb',
          options: [{ foodId: 'rice', amount: 150 }]
        }
      ]
    }
  ]
};

const MealPlanContext = createContext({
  getMealPlanForUser: (userId: string) => dummyMealPlan
});

export const MealPlanProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const getMealPlanForUser = (userId: string) => dummyMealPlan;

  return (
    <MealPlanContext.Provider value={{ getMealPlanForUser }}>
      {children}
    </MealPlanContext.Provider>
  );
};

export const useMealPlan = () => useContext(MealPlanContext);
