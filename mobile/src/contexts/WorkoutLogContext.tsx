import React, { createContext, useContext, useState } from 'react';
import { WorkoutLog } from '../types/workout';

interface WorkoutLogContextProps {
  addWorkoutLog: (userId: string, log: WorkoutLog) => void;
  getLogsByUserId: (userId: string) => WorkoutLog[];
}

const WorkoutLogContext = createContext<WorkoutLogContextProps>({
  addWorkoutLog: () => {},
  getLogsByUserId: () => []
});

export const WorkoutLogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [logs, setLogs] = useState<{ [userId: string]: WorkoutLog[] }>({});

  const addWorkoutLog = (userId: string, log: WorkoutLog) => {
    setLogs(prev => ({
      ...prev,
      [userId]: [...(prev[userId] || []), log]
    }));
  };

  const getLogsByUserId = (userId: string) => logs[userId] || [];

  return (
    <WorkoutLogContext.Provider value={{ addWorkoutLog, getLogsByUserId }}>
      {children}
    </WorkoutLogContext.Provider>
  );
};

export const useWorkoutLog = () => useContext(WorkoutLogContext);
