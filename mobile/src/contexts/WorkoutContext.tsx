import React, { createContext, useContext, useState } from 'react';

// Original context fields plus new ones
interface WorkoutContextType {
  isWorkoutStarted: boolean;
  setIsWorkoutStarted: React.Dispatch<React.SetStateAction<boolean>>;
  selectedDay: string;
  setSelectedDay: React.Dispatch<React.SetStateAction<string>>;
  currentExerciseIndex: number;
  setCurrentExerciseIndex: React.Dispatch<React.SetStateAction<number>>;
  activeExercise: any;
  setActiveExercise: React.Dispatch<React.SetStateAction<any>>;
  weight: string;
  setWeight: React.Dispatch<React.SetStateAction<string>>;
  reps: string;
  setReps: React.Dispatch<React.SetStateAction<string>>;
  // New fields for persistence
  appTrainingPlan: any[];
  setAppTrainingPlan: React.Dispatch<React.SetStateAction<any[]>>;
  selectedPlanId: string | null;
  setSelectedPlanId: React.Dispatch<React.SetStateAction<string | null>>;
  workoutLog: any;
  setWorkoutLog: React.Dispatch<React.SetStateAction<any>>;
}

const WorkoutContext = createContext<WorkoutContextType | undefined>(undefined);

export const WorkoutProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isWorkoutStarted, setIsWorkoutStarted] = useState(false);
  const [selectedDay, setSelectedDay] = useState('');
  const [currentExerciseIndex, setCurrentExerciseIndex] = useState(0);
  const [activeExercise, setActiveExercise] = useState<any>(null);
  const [weight, setWeight] = useState('');
  const [reps, setReps] = useState('');
  
  // New states for persistence
  const [appTrainingPlan, setAppTrainingPlan] = useState<any[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [workoutLog, setWorkoutLog] = useState<any>(null);

  return (
    <WorkoutContext.Provider
      value={{
        isWorkoutStarted,
        setIsWorkoutStarted,
        selectedDay,
        setSelectedDay,
        currentExerciseIndex,
        setCurrentExerciseIndex,
        activeExercise,
        setActiveExercise,
        weight,
        setWeight,
        reps,
        setReps,
        // Add new fields to provider
        appTrainingPlan,
        setAppTrainingPlan,
        selectedPlanId,
        setSelectedPlanId,
        workoutLog,
        setWorkoutLog
      }}
    >
      {children}
    </WorkoutContext.Provider>
  );
};

export const useWorkout = () => {
  const context = useContext(WorkoutContext);
  if (!context) {
    throw new Error('useWorkout must be used within a WorkoutProvider');
  }
  return context;
};