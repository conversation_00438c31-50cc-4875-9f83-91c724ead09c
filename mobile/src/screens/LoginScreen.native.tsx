import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  StyleSheet,
  Platform,
  KeyboardAvoidingView,
  ScrollView,
  Animated,
  SafeAreaView,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Mail, Lock, LogIn, Eye, EyeOff } from "lucide-react-native";
import { loginApi } from "../api/auth";
import { StackNavigationProp } from "@react-navigation/stack";
import { RootStackParamList } from "../navigation/index";
import { useAuthStore } from "../contexts/AuthContext";
import { createStyles } from "../style/Login.style";
import { useTheme } from "../contexts/ThemeContext";
import { prettyLog } from "../utils";
import { useTranslation } from "react-i18next";
type LoginScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  "Login"
>;

interface Role {
  id: number;
  name: string;
}

interface User {
  id: string;
  email: string;
  name: string;
  isActive: boolean;
  role: Role;
}

interface LoginResponse {
  jwtToken: string;
  message: string;
  user: User;
}

const LoginScreen: React.FC = () => {
  const { t } = useTranslation();

  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const fadeAnim = useState(new Animated.Value(0))[0];
  const login = useAuthStore((state: any) => state.login);
  const { theme, isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");

  useEffect(() => {
    if (error) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();

      const timeoutId = setTimeout(() => {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => setError(null));
      }, 3000);

      return () => clearTimeout(timeoutId);
    }
  }, [error, fadeAnim]);

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return t("login.validation.passwordRequired");
    }
    if (password.length < 8) {
      return t("login.validation.passwordLength");
    }
    if (!/[A-Z]/.test(password)) {
      return t("login.validation.passwordUppercase");
    }
    if (!/[a-z]/.test(password)) {
      return t("login.validation.passwordLowercase");
    }
    if (!/[0-9]/.test(password)) {
      return t("login.validation.passwordNumber");
    }
    if (!/[!@#$%^&*]/.test(password)) {
      return t("login.validation.passwordSpecialChar");
    }
    return null;
  };

  const validateEmail = (email: string): string | null => {
    if (!email?.trim()) {
      return t("login.validation.emailRequired");
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email.trim())) {
      return t("login.validation.emailInvalid");
    }
    return null;
  };

  const backendToFrontendErrorMap: Record<string, string> = {
    "Please provide a valid email": t("login.errors.invalidEmail"),
    "email should not be empty": t("login.errors.emailRequired"),
    "password should not be empty": t("login.errors.passwordRequired"),
    "password must match the required pattern": t(
      "login.errors.invalidPasswordPattern"
    ),
    "Invalid email or password.": t("login.errors.invalidCredentials"),
    "User not found": t("login.errors.userNotFound"),
    "Role not found": t("login.errors.roleNotFound"),
    "JWT_SECRET is not defined in the environment variables": t(
      "login.errors.serverConfiguration"
    ),
  };

  const getFrontendError = (backendMessage: string) => {
    return (
      backendToFrontendErrorMap[backendMessage] ||
      t("login.errors.unexpectedError")
    );
  };

  const handleSubmit = async (): Promise<void> => {
    const emailError = validateEmail(email);

    if (emailError) {
      setError(emailError);
      return;
    }

    const passwordError = validatePassword(password);

    if (passwordError) {
      setError(passwordError);
      return;
    }

    setError(null);
    setLoading(true);

    try {
      const result: any = await loginApi.login({ email, password });

      // Check if the result is a valid response
      if (result && result.jwtToken) {
        try {
          // Try to save the login data
          await login(result.jwtToken, result.user);
        } catch (error) {
          console.error("Failed to save login data:", error);
          setError(getFrontendError((error as any)?.message));
        }
      } else if (result && result.message) {
        setError(getFrontendError(result.message));
      } else {
        setError(getFrontendError(""));
      }
    } catch (error) {
      const backendMessage =
        (error as any)?.message || (error as any)?.error || "";
      setError(getFrontendError(backendMessage));
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = (): void => {
    setShowPassword((prevState) => !prevState);
  };

  const navigateToForgotPassword = (): void => {
    navigation.navigate("ForgotPassword");
  };

  const navigateToRegister = (): void => {
    navigation.navigate("Register");
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Rest of your component remains unchanged */}
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text style={styles.title}>{t("App.name")}</Text>
            <Text style={styles.subtitle}>{t("login.title")}</Text>
          </View>

          {loading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color={isDark ? "#E9D5FF" : "#3B82F6"} />
            </View>
          )}

          {error && (
            <Animated.View
              style={[styles.errorContainer, { opacity: fadeAnim }]}
              accessible={true}
              accessibilityLabel={`Error: ${error}`}
            >
              <Text style={styles.errorText}>{error}</Text>
            </Animated.View>
          )}

          <View style={styles.inputContainer}>
            <View style={styles.inputWrapper}>
              <View style={styles.iconContainer}>
                <Mail size={20} color="#9ca3af" />
              </View>
              <TextInput
                style={styles.input}
                placeholder={t("login.email")}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
                accessibilityLabel="Email address input"
                testID="email-input"
              />
            </View>

            <View style={styles.inputWrapper}>
              <View style={styles.iconContainer}>
                <Lock size={20} color="#9ca3af" />
              </View>
              <TextInput
                style={styles.input}
                placeholder={t("login.password")}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoComplete="password"
                accessibilityLabel="Password input"
                testID="password-input"
              />
              <TouchableOpacity
                onPress={togglePasswordVisibility}
                style={styles.eyeIcon}
                accessibilityLabel={
                  showPassword ? "Hide password" : "Show password"
                }
                accessibilityRole="button"
              >
                {showPassword ? (
                  <EyeOff size={20} color="#9ca3af" />
                ) : (
                  <Eye size={20} color="#9ca3af" />
                )}
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              onPress={navigateToForgotPassword}
              style={styles.forgotPasswordContainer}
              accessibilityLabel="Forgot password"
              accessibilityRole="button"
            >
              <Text style={styles.forgotPasswordText}>
                {t("login.forgotPassword")}
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={[styles.loginButton, loading && styles.loginButtonDisabled]}
            onPress={handleSubmit}
            disabled={loading}
            accessibilityLabel="Sign in button"
            accessibilityRole="button"
            testID="login-button"
          >
            <LogIn size={20} color="#ffffff" />
            <Text style={styles.loginButtonText}>{t("login.signIn")}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={navigateToRegister}
            style={styles.registerContainer}
            accessibilityLabel="Sign up"
            accessibilityRole="button"
          >
            {/* <Text style={styles.registerText}>
              {t("login.needAnAccount")}?{" "}
              <Text style={styles.registerLink}>{t("login.signUp")}</Text>
            </Text> */}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default LoginScreen;
