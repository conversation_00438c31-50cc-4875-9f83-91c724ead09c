import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { RouteProp, useNavigation, useRoute } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { Shield, Lock, Eye, EyeOff } from "lucide-react-native";
import { otpApi } from "../api/forget-password.api";
import { RootStackParamList } from "../navigation/index";
import axios from "axios";
import { useTheme } from "../contexts/ThemeContext";
import { createStyles } from "../style/ResetPassword.style";
import { useTranslation } from "react-i18next";

type ResetPasswordScreenRouteProp = RouteProp<
  RootStackParamList,
  "ResetPassword"
>;
type ResetPasswordScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "ResetPassword"
>;

const ResetPasswordScreen: React.FC = () => {
  const { t } = useTranslation();
  const [otp, setOtp] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(30);

  const navigation = useNavigation<ResetPasswordScreenNavigationProp>();
  const route = useRoute<ResetPasswordScreenRouteProp>();
  const { isDark } = useTheme();
  const styles = createStyles(isDark ? "dark" : "light");
  // Get email from navigation params
  const email = route.params?.email || "";

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (error || success) {
      timeoutId = setTimeout(() => {
        setError(null);
        if (success && !success.includes(t("success.passwordReset"))) {
          setSuccess(null);
        }
      }, 3000);
    }
    return () => timeoutId && clearTimeout(timeoutId);
  }, [error, success, t]);

  useEffect(() => {
    if (resendDisabled) {
      const interval = setInterval(() => {
        setCountdown((prev) => {
          if (prev === 1) {
            clearInterval(interval);
            setResendDisabled(false);
            return 30;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [resendDisabled]);

  const backendToFrontendErrorMap: Record<string, string> = {
    "email should not be empty": t("resetPassword.errors.emailRequired"),
    "email must be an email": t("resetPassword.errors.invalidEmail"),
    "otp should not be empty": t("resetPassword.errors.otpRequired"),
    "newPassword should not be empty": t(
      "resetPassword.errors.passwordRequired"
    ),
    "Password must be at least 8 characters long": t(
      "resetPassword.errors.passwordLength"
    ),
    "Invalid OTP": t("resetPassword.errors.invalidOtp"),
    "OTP has expired": t("resetPassword.errors.otpExpired"),
    "User not found": t("resetPassword.errors.userNotFound"),
    "Failed to update password in Firebase": t(
      "resetPassword.errors.firebaseUpdateFailed"
    ),
  };

  const validatePassword = (password: string): string | null => {
    if (!password) {
      return t("login.validation.passwordRequired");
    }
    if (password.length < 8) {
      return t("login.validation.passwordLength");
    }
    if (!/[A-Z]/.test(password)) {
      return t("login.validation.passwordUppercase");
    }
    if (!/[a-z]/.test(password)) {
      return t("login.validation.passwordLowercase");
    }
    if (!/[0-9]/.test(password)) {
      return t("login.validation.passwordNumber");
    }
    if (!/[!@#$%^&*]/.test(password)) {
      return t("login.validation.passwordSpecialChar");
    }
    return null;
  };

  const getResetPasswordError = (backendMessage: string) =>
    backendToFrontendErrorMap[backendMessage] ||
    t("resetPassword.errors.unexpectedError");

  const handleSubmit = async () => {
    setError(null);
    setSuccess(null);
    setIsSubmitting(true);

    const passwordError = validatePassword(password);

    if (passwordError) {
      setError(passwordError);
      setIsSubmitting(false);

      return;
    }

    if (password !== confirmPassword) {
      setError(t("errors.passwordMismatch"));
      setIsSubmitting(false);
      return;
    }

    if (password.length < 8) {
      setError(t("errors.passwordTooShort"));
      setIsSubmitting(false);
      return;
    }

    try {
      await otpApi.resetPassword({
        email,
        otp,
        newPassword: password,
      });
      setSuccess(t("success.passwordReset"));
      setTimeout(() => navigation.navigate("Login"), 2000);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const backendMessage =
          error.response?.data?.message || error.response?.data?.error || "";
        setError(getResetPasswordError(backendMessage));
      } else {
        setError(t("resetPassword.errors.unexpectedError"));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOtp = async () => {
    setResendDisabled(true);
    try {
      await otpApi.forgotPassword({ email });
      setSuccess(t("success.otpResent"));
    } catch (error) {
      setError(t("errors.resendOtpFailed"));
      setResendDisabled(false);
    }
  };

  const togglePasswordVisibility = (field: "password" | "confirmPassword") => {
    if (field === "password") {
      setShowPassword(!showPassword);
    } else {
      setShowConfirmPassword(!showConfirmPassword);
    }
  };

  const renderMessage = () => {
    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      );
    }

    if (success) {
      return (
        <View style={styles.successContainer}>
          <Text style={styles.successText}>{success}</Text>
        </View>
      );
    }
    return null;
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardAvoidContainer}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <View style={styles.card}>
            <View style={styles.header}>
              <Text style={styles.title}>{t("resetPassword.title")}</Text>
              <Text style={styles.subtitle}>{t("resetPassword.subtitle")}</Text>
            </View>

            {renderMessage()}

            <View style={styles.formContainer}>
              <View style={styles.inputContainer}>
                <View style={styles.iconContainer}>
                  <Shield size={20} color="#7D7D7D" />
                </View>
                <TextInput
                  style={styles.input}
                  placeholder={t("resetPassword.otpPlaceholder")}
                  value={otp}
                  onChangeText={setOtp}
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.inputContainer}>
                <View style={styles.iconContainer}>
                  <Lock size={20} color="#7D7D7D" />
                </View>
                <TextInput
                  style={styles.input}
                  placeholder={t("resetPassword.newPasswordPlaceholder")}
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                />
                <TouchableOpacity
                  onPress={() => togglePasswordVisibility("password")}
                  style={styles.eyeIcon}
                >
                  {showPassword ? (
                    <EyeOff size={20} color="#7D7D7D" />
                  ) : (
                    <Eye size={20} color="#7D7D7D" />
                  )}
                </TouchableOpacity>
              </View>

              <View style={styles.inputContainer}>
                <View style={styles.iconContainer}>
                  <Lock size={20} color="#7D7D7D" />
                </View>
                <TextInput
                  style={styles.input}
                  placeholder={t("resetPassword.confirmPasswordPlaceholder")}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                />
                <TouchableOpacity
                  onPress={() => togglePasswordVisibility("confirmPassword")}
                  style={styles.eyeIcon}
                >
                  {showConfirmPassword ? (
                    <EyeOff size={20} color="#7D7D7D" />
                  ) : (
                    <Eye size={20} color="#7D7D7D" />
                  )}
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={styles.resetButton}
                onPress={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color={isDark ? "#E9D5FF" : "#3B82F6"} />
                ) : (
                  <Text style={styles.buttonText}>
                    {t("resetPassword.resetButton")}
                  </Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.resendButton,
                  resendDisabled && styles.disabledButton,
                ]}
                onPress={handleResendOtp}
                disabled={resendDisabled}
              >
                <Text style={styles.buttonText}>
                  {resendDisabled
                    ? t("resetPassword.resendCountdown", { seconds: countdown })
                    : t("resetPassword.resendButton")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ResetPasswordScreen;
