export const lightTheme = {
  colors: {
    background: "#FFFFFF",
    surface: "#F5F5F5",
    primary: "#3B82F6",
    secondary: "#6B7280",
    text: "#FFFFFF",
    textSecondary: "#4B5563",
    border: "#E5E7EB",
    error: "#EF4444",
    success: "#10B981",
    warning: "#F59E0B",
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  roundness: {
    sm: 4,
    md: 8,
    lg: 16,
    full: 9999,
  },
};

export const darkTheme = {
  colors: {
    background: "#1F2937",
    surface: "#374151",
    primary: "#60A5FA",
    secondary: "#9CA3AF",
    text: "#2D3748",
    textSecondary: "#D1D5DB",
    border: "#4B5563",
    error: "#F87171",
    success: "#34D399",
    warning: "#FBBF24",
  },
  spacing: lightTheme.spacing,
  roundness: lightTheme.roundness,
};

export type Theme = typeof lightTheme;
