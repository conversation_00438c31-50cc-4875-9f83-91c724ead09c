import { FoodItem, MealWithFood } from '../types/meal';

const DUMMY_FOOD_ITEMS: FoodItem[] = [
    {
      id: '1',
      name: 'Chicken Breast',
      category: 'protein',
      defaultServing: 100,
      macrosPer100g: { protein: 31, carbs: 0, fats: 3.6, calories: 165 }
    },
    {
      id: '2',
      name: 'Salmon',
      category: 'protein',
      defaultServing: 100,
      macrosPer100g: { protein: 20, carbs: 0, fats: 13, calories: 208 }
    },
    {
      id: '3',
      name: 'Rice',
      category: 'carb',
      defaultServing: 100,
      macrosPer100g: { protein: 2.7, carbs: 28, fats: 0.3, calories: 130 }
    },
    {
      id: '4',
      name: 'Sweet Potato',
      category: 'carb',
      defaultServing: 100,
      macrosPer100g: { protein: 1.6, carbs: 20, fats: 0.1, calories: 86 }
    },
    {
      id: '5',
      name: 'Eggs',
      category: 'protein',
      defaultServing: 50,
      macrosPer100g: { protein: 13, carbs: 1.1, fats: 11, calories: 155 }
    },
    {
      id: '6',
      name: 'Oatmeal',
      category: 'carb',
      defaultServing: 40,
      macrosPer100g: { protein: 13, carbs: 67, fats: 6.9, calories: 389 }
    }
  ];
  

  const DUMMY_MEALS: MealWithFood[] = [
    {
      id: '1',
      name: 'Breakfast',
      categories: [
        {
          name: 'Protein',
          options: [
            { foodId: '2', amount: 100 },
            { foodId: '5', amount: 50 }
          ]
        },
        {
          name: 'Carb',
          options: [
            { foodId: '6', amount: 40 }
          ]
        }
      ],
      macroRange: {
        min: { protein: 25, carbs: 30, fats: 15, calories: 350 },
        max: { protein: 35, carbs: 40, fats: 20, calories: 450 }
      },
      foodItems: DUMMY_FOOD_ITEMS.filter(item => ['2', '5', '6'].includes(item.id))
    },
    {
      id: '2',
      name: 'Lunch',
      categories: [
        {
          name: 'Protein',
          options: [
            { foodId: '1', amount: 150 }
          ]
        },
        {
          name: 'Carb',
          options: [
            { foodId: '3', amount: 100 },
            { foodId: '4', amount: 100 }
          ]
        }
      ],
      macroRange: {
        min: { protein: 40, carbs: 40, fats: 10, calories: 450 },
        max: { protein: 50, carbs: 60, fats: 15, calories: 550 }
      },
      foodItems: DUMMY_FOOD_ITEMS.filter(item => ['1', '3', '4'].includes(item.id))
    },
    {
      id: '3',
      name: 'Dinner',
      categories: [
        {
          name: 'Protein',
          options: [
            { foodId: '1', amount: 100 }
          ]
        },
        {
          name: 'Carb',
          options: [
            { foodId: '3', amount: 80 }
          ]
        }
      ],
      macroRange: {
        min: { protein: 25, carbs: 20, fats: 5, calories: 250 },
        max: { protein: 30, carbs: 25, fats: 8, calories: 300 }
      },
      foodItems: DUMMY_FOOD_ITEMS.filter(item => ['1', '3'].includes(item.id))
    }
  ];

  export { DUMMY_FOOD_ITEMS, DUMMY_MEALS }