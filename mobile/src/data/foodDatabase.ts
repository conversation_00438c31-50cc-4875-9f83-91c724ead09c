type FoodItem = {
    id: string;
    name: string;
    macrosPer100g: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
  };
  
  const database: Record<string, FoodItem> = {
    'chicken-breast': {
      id: 'chicken-breast',
      name: 'Chicken Breast',
      macrosPer100g: {
        protein: 31,
        carbs: 0,
        fats: 3.6,
        calories: 165
      }
    },
    'brown-rice': {
      id: 'brown-rice',
      name: 'Brown Rice',
      macrosPer100g: {
        protein: 2.6,
        carbs: 23,
        fats: 0.9,
        calories: 111
      }
    },
    'broccoli': {
      id: 'broccoli',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      macrosPer100g: {
        protein: 2.8,
        carbs: 6.6,
        fats: 0.3,
        calories: 34
      }
    }
  };
  
  export const getFoodById = (id: string): FoodItem | undefined => {
    return database[id];
  };
  