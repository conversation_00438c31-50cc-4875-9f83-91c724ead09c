import { Day } from '../types/excercise';

  const dummy<PERSON>orkoutData: Day[] = [
    {
      id: '1',
      day: 'יום ראשון',
      focus: 'Chest and Triceps',
      exercises: [
        {
          id: '101',
          name: 'Bench Press',
          sets: '4',
          reps: '8-12',
          rest: '90 seconds',
          instructions: 'Lower bar slowly, push explosively'
        },
        {
          id: '102',
          name: 'Incline Dumbbell Press',
          sets: '3',
          reps: '10-12',
          rest: '60 seconds',
          instructions: 'Focus on upper chest contraction'
        },
        {
          id: '103',
          name: '<PERSON><PERSON><PERSON> Pushdowns',
          sets: '3',
          reps: '12-15',
          rest: '45 seconds'
        }
      ]
    },
    {
      id: '2',
      day: 'יום שלישי',
      focus: 'Back and Biceps',
      exercises: [
        {
          id: '201',
          name: 'Pull-ups',
          sets: '4',
          reps: '6-8',
          rest: '120 seconds',
          instructions: 'Use full range of motion'
        },
        {
          id: '202',
          name: '<PERSON><PERSON> Over <PERSON>s',
          sets: '3',
          reps: '10-12',
          rest: '90 seconds'
        },
        {
          id: '203',
          name: '<PERSON><PERSON>',
          sets: '3',
          reps: '10-12',
          rest: '60 seconds',
          instructions: 'Keep elbows stable throughout the movement'
        }
      ]
    },
    {
      id: '3',
      day: 'יום חמישי',
      focus: 'Legs and Cardio',
      exercises: [
        {
          id: '301',
          name: 'Squats',
          sets: '4',
          reps: '8-10',
          rest: '120 seconds',
          instructions: 'Keep chest up, go below parallel'
        },
        {
          id: '302',
          name: 'Romanian Deadlifts',
          sets: '3',
          reps: '10-12',
          rest: '90 seconds'
        },
        {
          id: '303',
          name: 'Treadmill',
          sets: '1',
          reps: '20 minutes',
          rest: 'None',
          instructions: 'Interval: 1 min sprint, 2 min walk'
        }
      ]
    }
  ];

  export { dummyWorkoutData }