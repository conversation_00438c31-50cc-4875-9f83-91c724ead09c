import { StyleSheet, Platform } from "react-native";
import { colors } from "../theme/colors";

export const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme === "dark" ? "#1F2937" : "#FFFFFF",
      //   padding: 12,
    },
    headerContainer: {
      marginTop: "5%",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 16,
    },
    headerLeft: {
      flexDirection: "row",
      alignItems: "center",
    },
    headerRight: {
      flexDirection: "row",
      alignItems: "center",
    },
    appTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: colors[theme].text,
    },
    menuButton: {
      padding: 8,
      borderRadius: 8,
      backgroundColor: colors[theme].background,
      borderColor: "white",
      borderWidth: 1,
      marginRight: 8,
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        },
        android: { elevation: 2 },
      }),
    },
    // Updated logout button style
    logoutButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors[theme].Buttonprimary,
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        },
        android: { elevation: 2 },
      }),
    },
    logoutButtonText: {
      color: colors[theme].ButtonText,
      fontWeight: "600",
      fontSize: 14,
      marginLeft: 8,
    },
    // New dropdown menu styles
    menuContainer: {
      backgroundColor: colors[theme].cardBackground,
      borderRadius: 12,
      padding: 8,
      marginBottom: 16,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#4C1D95" : "#DBEAFE", // Purple-900 for dark mode
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 6,
        },
        android: { elevation: 6 },
      }),
      zIndex: 10,
    },
    menuItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginVertical: 4,
    },
    activeMenuItem: {
      backgroundColor: theme === "dark" ? "#6D28D9" : "#3B82F6", // Purple-700 for dark mode
    },
    menuItemText: {
      marginLeft: 12,
      fontSize: 16,
      fontWeight: "500",
      color: colors[theme].MeterTitle,
    },
    activeMenuItemText: {
      color: "#FFFFFF",
    },
    // Current tab title container
    currentTabTitleContainer: {
      backgroundColor: colors[theme].cardBackground,
      borderRadius: 12,
      padding: 16,
      marginBottom: 16,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#4C1D95" : "#DBEAFE", // Purple-900 for dark mode
      alignItems: "center",
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: { elevation: 3 },
      }),
    },
    currentTabTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: colors[theme].Heading,
    },
    profileCard: {
      flex: 1,
      backgroundColor: theme === "dark" ? "rgba(17,24, 39, 1)" : "#FFFFFF",
      //   borderRadius: 16,
      padding: 16,
      paddingTop: 24,
      //   borderWidth: 2,
      //   borderColor: theme === "dark" ? colors[theme].mealPlanBorder : "#BEE3F8",
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
      }),
    },
    profileHeader: {
      flexDirection: "column",
      marginBottom: 12,
      paddingBottom: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme === "dark" ? "#374151" : "#E5E7EB",
    },
    profileInfo: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 16,
    },
    avatarSection: {
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 16,
    },
    userNameContainer: {
      alignItems: "center",
      marginTop: 8,
    },
    avatarContainer: {
      width: 60,
      height: 60,
      borderRadius: 30,
      borderWidth: 4,
      borderColor: theme === "dark" ? "#6D28D9" : "#BFDBFE",
      overflow: "hidden",
      //   marginRight: 12,
    },
    avatar: {
      width: "100%",
      height: "100%",
    },
    avatarPlaceholder: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: theme === "dark" ? "rgba(147, 51, 234, 1)" : "#3B82F6", // Purple-700 for dark mode
      justifyContent: "center",
      alignItems: "center",
      borderWidth: 4,
      borderColor: theme === "dark" ? "rgba(216, 180,254, 1)" : "#BFDBFE", // Purple-500 for dark mode
      //   marginRight: 12,
    },
    avatarText: {
      fontSize: 24,
      fontWeight: "bold",
      color: "#FFFFFF",
    },
    userDetails: {
      flex: 1,
    },
    userName: {
      fontSize: 18,
      fontWeight: "bold",
      color: colors[theme].userName,
      marginBottom: 4,
    },
    userSubInfo: {
      flexDirection: "row",
      alignItems: "center",
      marginTop: 2,
    },
    userSubText: {
      fontSize: 12,
      color: colors[theme].userName,

      marginLeft: 4,
    },
    userStats: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 6,
      borderTopWidth: 1,
      borderTopColor: theme === "dark" ? "#4B5563" : "#E5E7EB",
      marginTop: 4,
    },
    statItem: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: 4,
    },
    rankBadge: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme === "dark" ? "rgba(146, 64, 14, 0.2)" : "#FEF3C7",
      paddingVertical: 6,
      paddingHorizontal: 10,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#F59E0B" : "#FCD34D",
      minHeight: 32,
    },
    rankIconContainer: {
      marginRight: 4,
    },
    rankText: {
      fontSize: 12,
      fontWeight: "600",
      color: theme === "dark" ? "#F59E0B" : "#92400E",
    },
    pointsBadge: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme === "dark" ? "rgba(30, 58, 138, 0.2)" : "#DBEAFE",
      paddingVertical: 6,
      paddingHorizontal: 10,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#3B82F6" : "#93C5FD",
      minHeight: 32,
    },
    pointsText: {
      fontSize: 12,
      fontWeight: "600",
      color: theme === "dark" ? "#60A5FA" : "#1D4ED8",
      marginLeft: 4,
    },
    membershipStatus: {
      alignItems: "center",
    },
    membershipLabel: {
      fontSize: 10,
      color: "#6B7280",
      marginBottom: 2,
    },
    membershipBadge: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#D1FAE5",
      paddingVertical: 6,
      paddingHorizontal: 10,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(126, 34, 206, 1)" : "#A7F3D0",
      minHeight: 20,
    },
    membershipText: {
      fontSize: 12,
      fontWeight: "600",
      color: theme === "dark" ? "rgba(233, 213, 255, 1)" : "#047857",
      marginLeft: 4,
    },
    progressOverviewCard: {
      backgroundColor: colors[theme].cardBackground,
      borderRadius: 12,
      padding: 12,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#6D28D9" : "#DBEAFE",
    },
    progressText: {
      fontSize: 14,
      color: colors[theme].userName,
      lineHeight: 20,
    },
    progressCardsContainer: {
      flexDirection: "column",
      marginBottom: 16,
    },
    metricsGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      justifyContent: "space-between",
      marginBottom: 16,
      padding: 16,
      backgroundColor: theme === "dark" ? "#1F2937" : "#F9FAFB", // Gray-800 for dark mode
      borderRadius: 10,
    },
    metricCard: {
      width: "30%",
      alignItems: "center",
      padding: 8,
    },
    metricIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#EBF8FF", // Purple-900 for dark mode
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 8,
    },
    metricTitle: {
      fontSize: 14,
      fontWeight: "600",
      color: theme === "dark" ? "#D1D5DB" : "#4B5563", // Gray-300 for dark mode
      marginBottom: 4,
    },
    metricValue: {
      fontSize: 20,
      fontWeight: "bold",
      color: theme === "dark" ? "rgba(216, 180, 254, 1)" : "#4A90E2", // Purple-300 for dark mode
      marginBottom: 4,
    },
    metricUnit: {
      fontSize: 14,
      color: theme === "dark" ? "rgba(216, 180, 254, 1)" : "#6B7280", // Gray-400 for dark mode
    },
    changeIndicator: {
      marginTop: 4,
    },
    positiveChange: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme === "dark" ? "rgba(127, 29, 29, 1)" : "#D1FAE5", // bg-green-900 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(127, 29, 29, 1)" : "#A7F3D0", // Same as background for dark mode
    },
    positiveChangeText: {
      fontSize: 12,
      fontWeight: "500",
      color: theme === "dark" ? "rgba(254, 202, 202, 1)" : "#047857", // text-green-200 for dark mode
      marginLeft: 4,
    },
    negativeChange: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme === "dark" ? "#7F1D1D" : "#FEE2E2", // bg-red-900 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#7F1D1D" : "#FECACA", // Same as background for dark mode
    },
    negativeChangeText: {
      fontSize: 12,
      fontWeight: "500",
      color: theme === "dark" ? "#FECACA" : "#B91C1C", // text-red-200 for dark mode
      marginLeft: 4,
    },
    neutralChange: {
      backgroundColor: theme === "dark" ? "#374151" : "#F3F4F6", // bg-gray-700 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#374151" : "#E5E7EB", // Same as background for dark mode
    },
    neutralChangeText: {
      fontSize: 12,
      fontWeight: "500",
      color: theme === "dark" ? "#D1D5DB" : "#6B7280", // text-gray-300 for dark mode
    },
    // New styles for score card status indicators
    excellentChange: {
      backgroundColor: theme === "dark" ? "#065F46" : "#D1FAE5", // bg-green-900 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#065F46" : "#A7F3D0", // Same as background for dark mode
    },
    excellentChangeText: {
      fontSize: 12,
      fontWeight: "500",
      color: theme === "dark" ? "#A7F3D0" : "#047857", // text-green-200 for dark mode
    },
    goodChange: {
      backgroundColor: theme === "dark" ? "#1E3A8A" : "#DBEAFE", // bg-blue-900 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#1E3A8A" : "#93C5FD", // Same as background for dark mode
    },
    goodChangeText: {
      fontSize: 12,
      fontWeight: "500",
      color: theme === "dark" ? "#BFDBFE" : "#1D4ED8", // text-blue-200 for dark mode
    },
    onTrackChange: {
      backgroundColor: theme === "dark" ? "rgba(113, 63, 18, 1)" : "#FEF3C7", // bg-yellow-900 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(113, 63, 18, 1)" : "#FCD34D", // Same as background for dark mode
    },
    onTrackChangeText: {
      fontSize: 12,
      fontWeight: "500",
      color: theme === "dark" ? "rgba(254, 240, 138, 1)" : "#92400E", // text-yellow-200 for dark mode
    },
    weightCard: {
      backgroundColor: theme === "dark" ? "rgba(153, 27, 27, 0.2)" : "#FEE2E2", // Red-900 with opacity for dark mode
      borderRadius: 12,
      padding: 12,
      marginBottom: 12,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#EF4444" : "#FECACA", // Red-500 for dark mode
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        },
        android: { elevation: 2 },
      }),
    },
    bodyFatCard: {
      backgroundColor: theme === "dark" ? "rgba(153, 27, 27, 0.2)" : "#FEE2E2", // Red-900 with opacity for dark mode
      borderRadius: 12,
      padding: 12,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#EF4444" : "#FECACA", // Red-500 for dark mode
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.1,
          shadowRadius: 2,
        },
        android: { elevation: 2 },
      }),
    },
    cardHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 10,
    },
    cardIconContainer: {
      padding: 8,
      borderRadius: 8,
      marginRight: 8,
      backgroundColor:
        theme === "dark"
          ? "rgba(76, 29, 149, 0.3)"
          : "rgba(255, 255, 255, 0.5)", // Purple-900 with opacity for dark mode
    },
    cardTitle: {
      fontSize: 14,
      fontWeight: "600",
      color: theme === "dark" ? "#FFFFFF" : "#1F2937", // White for dark mode
    },
    weightDataContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    weightDataCard: {
      flex: 1,
      backgroundColor: theme === "dark" ? "#1F2937" : "#FFFFFF", // Gray-800 for dark mode
      borderRadius: 8,
      padding: 10,
      alignItems: "center",
      marginHorizontal: 4,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#EF4444" : "rgba(239, 68, 68, 0.5)", // Red-500 for dark mode
    },
    dataLabel: {
      fontSize: 10,
      color: theme === "dark" ? "#9CA3AF" : "#6B7280", // Gray-400 for dark mode
      marginBottom: 4,
    },
    weightValueContainer: {
      flexDirection: "row",
      alignItems: "flex-end",
    },
    weightValue: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme === "dark" ? "#F87171" : "#B91C1C", // Red-400 for dark mode
    },
    weightUnit: {
      fontSize: 12,
      color: theme === "dark" ? "#9CA3AF" : "#6B7280", // Gray-400 for dark mode
      marginLeft: 2,
      marginBottom: 2,
    },
    bodyFatValue: {
      fontSize: 18,
      fontWeight: "bold",
      color: theme === "dark" ? "#F87171" : "#B91C1C", // Red-400 for dark mode
    },
    achievementMeters: {
      marginBottom: 16,
    },
    meterCard: {
      backgroundColor: colors[theme].cardBackground,
      borderRadius: 12,
      padding: 12,
      marginBottom: 8,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#6D28D9" : "#E5E7EB", // Purple-700 for dark mode
    },
    meterHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 8,
    },
    meterHeaderLeft: {
      flexDirection: "row",
      alignItems: "center",
    },
    meterTitle: {
      fontSize: 13,
      fontWeight: "500",
      color: colors[theme].MeterTitle,
      marginLeft: 6,
    },
    meterValue: {
      fontSize: 14,
      fontWeight: "bold",
      color: theme === "dark" ? "#FBBF24" : "#F59E0B", // Amber-400 for dark mode
    },
    progressBar: {
      height: 8,
      backgroundColor: theme === "dark" ? "#374151" : "#F3F4F6", // Gray-700 for dark mode
      borderRadius: 4,
      overflow: "hidden",
    },
    progressFill: {
      height: "100%",
      borderRadius: 4,
    },
    updatesSection: {
      marginTop: 16,
      padding: 16,
      backgroundColor: theme === "dark" ? "#1F2937" : "#F9FAFB", // Gray-800 for dark mode
      borderRadius: 10,
      direction: "rtl", // Add RTL direction for the entire updates section
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: "700",
      color: theme === "dark" ? "#FFFFFF" : "#111827", // White for dark mode
      marginBottom: 12,
      writingDirection: "rtl", // Add RTL writing direction for Hebrew text
    },
    updateCards: {
      flexDirection: "column",
      gap: 12,
    },
    updateCard: {
      backgroundColor: theme === "dark" ? "rgba(31, 41, 55, 1)" : "#FFFFFF", // Gray-900 for dark mode
      borderRadius: 10,
      padding: 12,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(107, 33, 168, 1)" : "#4A90E2", // Gray-700 for dark mode
    },
    updateHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    updateTitle: {
      fontSize: 14,
      fontWeight: "600",
      color: theme === "dark" ? "rgba(229, 231, 235, 1)" : "black", // Purple-300 for dark mode
      marginHorizontal: 4,
    },
    updateText: {
      fontSize: 14,
      color: theme === "dark" ? "#D1D5DB" : "#4B5563", // Gray-300 for dark mode
      lineHeight: 20,
      writingDirection: "rtl", // Add RTL writing direction for Hebrew text
    },
    content: {
      flex: 1,
    },
    contentContainer: {
      flex: 1,

      //   paddingHorizontal: 8,
      //   paddingTop: 12,
      //   paddingBottom: 8,
      //   padding: 5,
    },
    floatingTimerContainer: {
      position: "absolute",
      bottom: 16,
      left: 16,
      right: 16,
      backgroundColor:
        theme === "dark"
          ? "rgba(31, 41, 55, 0.95)"
          : "rgba(255, 255, 255, 0.95)", // Gray-800 with opacity for dark mode
      borderRadius: 12,
      padding: 16,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#6D28D9" : "#BFDBFE", // Purple-700 for dark mode
      ...Platform.select({
        ios: {
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 6,
        },
        android: { elevation: 6 },
      }),
    },
  });
