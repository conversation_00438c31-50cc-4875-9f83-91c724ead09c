import {
        StyleSheet,
        Platform, 
      } from 'react-native';
import { colors } from '../theme/colors';

export const createStyles = (theme: 'light' | 'dark') =>
  StyleSheet.create({
        container: {
          flex: 1,
          backgroundColor: colors[theme].background,
        },
        keyboardAvoidingView: {
          flex: 1,
        },
        scrollContainer: {
          flexGrow: 1,
          paddingHorizontal: 20,
          paddingTop: 40,
          paddingBottom: 20,
        },
        header: {
          marginTop: '10%',
          alignItems: 'center',
          marginBottom: 32,
        },
        title: {
          fontSize: 32,
          fontWeight: 'bold',
          color: colors[theme].MeterTitle,
          marginBottom: 8,
        },
        subtitle: {
          fontSize: 18,
          color: colors[theme].MeterTitle,
        },
        loadingOverlay: {
          position: 'absolute',
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          zIndex: 1,
        },
        errorContainer: {
          backgroundColor: '#fee2e2',
          borderWidth: 1,
          borderColor: '#f87171',
          borderRadius: 8,
          padding: 12,
          marginBottom: 20,
        },
        errorText: {
          color: '#b91c1c',
          fontSize: 15,
        },
        successContainer: {
          backgroundColor: '#dcfce7',
          borderWidth: 1,
          borderColor: '#34d399',
          borderRadius: 8,
          padding: 12,
          marginBottom: 20,
        },
        successText: {
          color: '#047857',
          fontSize: 15,
        },
        inputContainer: {
          marginBottom: 24,
        },
        inputWrapper: {
          flexDirection: 'row',
          alignItems: 'center',
          borderWidth: 1,
          borderColor: '#e5e7eb',
          borderRadius: 10,
          marginBottom: 16,
          backgroundColor: '#f9fafb',
        },
        inputIcon: {
          paddingHorizontal: 14,
        },
        input: {
          flex: 1,
          paddingVertical: 14,
          fontSize: 16,
        },
        eyeIcon: {
          padding: 14,
        },
        roleDisplay: {
          marginTop: 8,
          marginBottom: 16,
          flexDirection: 'row',
          alignItems: 'center',
        },
        roleLabel: {
          fontSize: 16,
          fontWeight: '500',
          color: '#4b5563',
          marginRight: 12,
        },
        roleValueContainer: {
          backgroundColor: '#eef2ff',
          paddingVertical: 8,
          paddingHorizontal: 16,
          borderRadius: 8,
          borderWidth: 1,
          borderColor: '#c7d2fe',
        },
        roleValue: {
          fontSize: 16,
          color: '#4f46e5',
          fontWeight: '500',
        },
        registerButton: {
          backgroundColor: colors[theme].Buttonprimary,
          borderRadius: 10,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
          paddingVertical: 16,
          marginBottom: 20,
        },
        disabledButton: {
          backgroundColor: '#93c5fd',
        },
        registerButtonText: {
          color: colors[theme].ButtonText,
          fontWeight: '600',
          fontSize: 17,
          marginLeft: 8,
        },
        loginContainer: {
          alignItems: 'center',
          marginTop: 10,
        },
        loginText: {
          fontSize: 16,
          color: colors[theme].MeterTitle,
        },
        loginLink: {
          color: colors[theme].Buttonprimary,
          fontWeight: '500',
        },
      });