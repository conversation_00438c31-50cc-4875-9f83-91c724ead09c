import { StyleSheet, Platform } from "react-native";
import { colors } from "../theme/colors";

export const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    container: {
      flexGrow: 1,
      backgroundColor: colors[theme].background,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20
    },
    header: {
      alignItems: 'center',
      marginBottom: 32
    },
    title: {
      fontSize: 32,
      fontWeight: 'bold',
      color: colors[theme].MeterTitle,
      marginBottom: 8
    },
    subtitle: {
      fontSize: 20,
      color: '#4B5563',
      fontWeight: '500'
    },
    errorContainer: {
      backgroundColor: '#FEE2E2',
      borderWidth: 1,
      borderColor: '#F87171',
      padding: 12,
      borderRadius: 8,
      marginBottom: 16,
      width: '100%',
      maxWidth: 400
    },
    errorText: {
      color: '#B91C1C',
      textAlign: 'center'
    },
    successContainer: {
      backgroundColor: colors[theme].WeightCard,
      borderWidth: 1,
      borderColor: '#34D399',
      padding: 12,
      borderRadius: 8,
      marginBottom: 16,
      width: '100%',
      maxWidth: 400
    },
    successText: {
      color: theme === "dark" ? "green" : colors[theme].MeterTitle,
      textAlign: "right",
    },
    form: {
      width: '100%',
      maxWidth: 400,
      marginTop: 16
    },
    inputContainer: {
      flexDirection: 'row-reverse',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: '#E5E7EB',
      borderRadius: 12,
      marginBottom: 24,
      backgroundColor: 'white'
    },
    iconContainer: {
      paddingHorizontal: 12
    },
    input: {
      flex: 1,
      height: 56,
      paddingVertical: 12,
      paddingRight: 12,
      color: '#1F2937',
      fontSize: 16,
      textAlign: 'right'
    },
    button: {
      backgroundColor: colors[theme].Buttonprimary,
      borderRadius: 12,
      padding: 16,
      alignItems: 'center',
      marginBottom: 20,
      elevation: 2
    },
    buttonDisabled: {
      opacity: 0.7
    },
    buttonContent: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center'
    },
    buttonText: {
      color: colors[theme].ButtonText,
      fontWeight: '600',
      fontSize: 18,
      marginLeft: 8
    },
    loader: {
      marginLeft: 8
    },
    backButton: {
      alignItems: 'center',
      padding: 12
    },
    backButtonText: {
      color: colors[theme].Buttonprimary,
      fontWeight: '500',
      fontSize: 16
    }
  });
