import { StyleSheet, Platform } from "react-native";
import { colors } from "../theme/colors";

export const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors[theme].background,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: "center",
      paddingHorizontal: 20,
      paddingBottom: 40,
    },
    header: {
      alignItems: "center",
      marginBottom: 36,
      paddingTop: 50,
    },
    title: {
      fontSize: 32,
      fontWeight: "bold",
      color: colors[theme].MeterTitle,
      marginBottom: 12,
    },
    subtitle: {
      fontSize: 17,
      color: colors[theme].MeterTitle,
    },
    loadingOverlay: {
      position: "absolute",
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "rgba(255, 255, 255, 0.7)",
      zIndex: 1,
    },
    errorContainer: {
      backgroundColor: "#fee2e2",
      borderWidth: 1,
      borderColor: "#f87171",
      borderRadius: 10,
      padding: 12,
      marginBottom: 20,
    },
    errorText: {
      color: "#b91c1c",
      fontSize: 14,
      textAlign: "right",
    },
    inputContainer: {
      marginBottom: 24,
    },
    inputWrapper: {
      flexDirection: "row-reverse",
      alignItems: "center",
      borderWidth: 1,
      borderColor: "#e5e7eb",
      backgroundColor: "#ffffff",
      borderRadius: 12,
      marginBottom: 16,
      height: 54,
    },
    iconContainer: {
      paddingHorizontal: 12,
    },
    inputIcon: {
      paddingHorizontal: 16,
    },
    input: {
      flex: 1,
      paddingVertical: 12,
      fontSize: 16,
      color: "#1f2937",
      textAlign: "right",
    },
    eyeIcon: {
      padding: 14,
    },
    forgotPasswordContainer: {
      alignItems: "flex-end",
      marginTop: 4,
      paddingRight: 4,
    },
    forgotPasswordText: {
      color: colors[theme].Buttonprimary,
      fontWeight: "500",
      fontSize: 15,
    },
    loginButton: {
      backgroundColor: colors[theme].Buttonprimary,
      borderRadius: 12,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 16,
      marginBottom: 24,
      height: 54,
    },
    loginButtonDisabled: {
      backgroundColor: "#93c5fd",
    },
    loginButtonText: {
      color: colors[theme].ButtonText,
      fontWeight: "600",
      fontSize: 16,
      marginLeft: 10,
    },
    registerContainer: {
      alignItems: "center",
      marginTop: 12,
    },
    registerText: {
      fontSize: 16,
      color: colors[theme].MeterTitle,
    },
    registerLink: {
      color: colors[theme].Buttonprimary,
      fontWeight: "500",
    },
  });
