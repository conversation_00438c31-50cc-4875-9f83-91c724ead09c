import {
        StyleSheet,
        Platform, 
      } from 'react-native';
import { colors } from '../theme/colors';

export const createStyles = (theme: 'light' | 'dark') =>
  StyleSheet.create({
        container: {
          flex: 1,
          backgroundColor: '#F5F7FA',
        },
        contentContainer: {
          padding: 16,
        },
        loadingContainer: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 24,
        },
        loadingText: {
          marginTop: 12,
          fontSize: 16,
          color: '#4A5568',
          textAlign: 'center',
        },
        errorContainer: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: 24,
        },
        errorText: {
          fontSize: 16,
          color: '#E53E3E',
          textAlign: 'center',
        },
        emptyContainer: {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          padding: 24,
        },
        emptyCard: {
          backgroundColor: 'white',
          borderRadius: 12,
          padding: 24,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
          borderWidth: 2,
          borderColor: '#BEE3F8',
          alignItems: 'center',
          width: '100%',
        },
        emptyText: {
          fontSize: 16,
          color: '#718096',
          textAlign: 'center',
        },
        mealPlanCard: {
          backgroundColor: 'white',
          borderRadius: 12,
          padding: 24,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 5,
          borderWidth: 2,
          borderColor: '#BEE3F8',
        },
        headerContainer: {
          flexDirection: 'row',
          alignItems: 'center',
          marginBottom: 24,
          gap: 12,
        },
        iconContainer: {
          padding: 12,
          backgroundColor: '#EBF8FF',
          borderRadius: 12,
        },
        headerTitle: {
          fontSize: 20,
          fontWeight: '600',
          color: '#2D3748',
        },
        totalMacrosContainer: {
          marginBottom: 24,
        },
        mealsContainer: {
          marginTop: 8,
        },
        mealCard: {
          backgroundColor: 'white',
          borderRadius: 8,
          padding: 16,
          marginBottom: 20,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.05,
          shadowRadius: 5,
          elevation: 2,
          borderWidth: 1,
          borderColor: '#E2E8F0',
        },
        mealTitle: {
          fontSize: 18,
          fontWeight: '600',
          color: '#2D3748',
          marginBottom: 16,
          textAlign: 'right',
        },
        categoryContainer: {
          marginBottom: 16,
        },
        categoryHeader: {
          flexDirection: 'row-reverse', // RTL support
          alignItems: 'center',
          marginBottom: 12,
          gap: 8,
        },
        categoryTitle: {
          fontSize: 16,
          fontWeight: '500',
          color: '#4A5568',
        },
        optionsContainer: {
          marginBottom: 8,
        },
        proteinOptionItem: {
          backgroundColor: '#EBF8FF',
          borderRadius: 8,
          padding: 12,
          marginBottom: 8,
          flexDirection: 'row-reverse', // RTL support
          justifyContent: 'space-between',
          alignItems: 'center',
        },
        proteinOptionName: {
          fontSize: 15,
          color: '#2B6CB0',
        },
        carbOptionItem: {
          backgroundColor: '#FEEBC8',
          borderRadius: 8,
          padding: 12,
          marginBottom: 8,
          flexDirection: 'row-reverse', // RTL support
          justifyContent: 'space-between',
          alignItems: 'center',
        },
        carbOptionName: {
          fontSize: 15,
          color: '#C05621',
        },
        proteinMacroValue: {
          fontSize: 14,
          color: '#2B6CB0',
        },
        carbMacroValue: {
          fontSize: 14,
          color: '#C05621',
        },
        macroDisplayContainer: {
          backgroundColor: 'white',
          borderRadius: 8,
          padding: 16,
          borderWidth: 1,
          borderColor: '#E2E8F0',
          marginTop: 8,
        },
        macroTitle: {
          fontSize: 16,
          fontWeight: '500',
          color: '#4A5568',
          marginBottom: 12,
          textAlign: 'right',
        },
        macroGrid: {
          flexDirection: 'row',
          justifyContent: 'space-between',
        },
        macroColumn: {
          width: '48%',
          gap: 8,
        },
        macroItemProtein: {
          padding: 12,
          backgroundColor: 'rgba(239, 246, 255, 0.7)',
          borderRadius: 8,
          marginBottom: 8,
        },
        macroItemCarbs: {
          padding: 12,
          backgroundColor: 'rgba(255, 251, 235, 0.7)',
          borderRadius: 8,
          marginBottom: 8,
        },
        macroItemFats: {
          padding: 12,
          backgroundColor: 'rgba(240, 253, 244, 0.7)',
          borderRadius: 8,
          marginBottom: 8,
        },
        macroItemCalories: {
          padding: 12, 
          backgroundColor: 'rgba(254, 242, 242, 0.7)',
          borderRadius: 8,
          marginBottom: 8,
        },
        macroItemHeader: {
          flexDirection: 'row-reverse',
          alignItems: 'center',
          gap: 6,
          marginBottom: 4,
        },
        macroItemLabel: {
          fontSize: 14,
          color: '#4A5568',
          fontWeight: '500',
        },
        macroItemValue: {
          fontSize: 15,
          fontWeight: '600',
          color: '#2D3748',
          textAlign: 'right',
        }
      });