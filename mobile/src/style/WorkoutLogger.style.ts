import { StyleSheet, Platform } from "react-native";
import { colors } from "../theme/colors";

export const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    loadingContainer: {
      justifyContent: "center",
      alignItems: "center",
    },
    loadingText: {
      marginTop: 10,
      fontSize: 16,
      color: theme === "dark" ? "#C4B5FD" : "#3B82F6", // Purple-300 for dark mode
    },
    container: {
      flex: 1,
      //   paddingHorizontal: 8,
      //   paddingTop: 12,
      //   paddingBottom: 8,
    },
    workoutLogContainer: {
      flex: 1,
      backgroundColor:
        theme === "dark" ? "#181825" : colors[theme].cardBackground, // bg-gray-800 for dark mode
      //   borderRadius: 12,

      padding: 17,

      //   shadowColor: "#000",
      //   shadowOffset: { width: 0, height: 4 },
      //   shadowOpacity: 0.1,
      //   shadowRadius: 8,
      //   borderWidth: 2,
      //   borderColor: theme === "dark" ? colors[theme].mealPlanBorder : "#BEE3F8",
    },

    noWorkoutStartedHeader: {
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
      marginBottom: 16,
    },

    header: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 16,
      width: "100%",
    },
    headerIcon: {
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#E0F2FE", // Purple-800 for dark mode
      padding: 8,
      borderRadius: 10,
      marginRight: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
      elevation: 2,
    },

    titleContainer: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
    },

    headerText: {
      fontSize: 18,
      fontWeight: "600",
      color: theme === "dark" ? "white" : colors[theme].MeterTitle, // Purple-300 for dark mode
      //   flex: 1,
      marginRight: 8,
    },
    selectDayContainer: {
      marginBottom: 24,
      marginTop: 16,
    },
    selectDayLabel: {
      fontSize: 14,
      fontWeight: "500",
      color: theme === "dark" ? "gray" : colors[theme].MeterTitle, // Purple-300 for dark mode
      marginBottom: 8,
      textAlign: "right",
      marginRight: 5,
    },
    dayPicker: {
      flexDirection: "row",
      marginBottom: 16,
    },
    dayButton: {
      backgroundColor: theme === "dark" ? "#374151" : "#F3F4F6", // Gray-700 for dark mode
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
      marginRight: 8,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#6B7280" : "transparent", // Gray-500 for dark mode
    },
    selectedDayButton: {
      backgroundColor: theme === "dark" ? "#4C1D95" : "#BFDBFE", // Purple-900 for dark mode
      borderColor: theme === "dark" ? "#8B5CF6" : "transparent", // Purple-500 for dark mode
    },
    dayButtonText: {
      fontSize: 14,
      color: theme === "dark" ? "#D1D5DB" : "#4B5563", // Gray-300 for dark mode
    },
    selectedDayButtonText: {
      color: theme === "dark" ? "#C4B5FD" : "#1E40AF", // Purple-300 for dark mode
      fontWeight: "500",
    },
    selectDayButton: {
      backgroundColor:
        theme === "dark" ? "rgba(88, 28, 135, 1)" : colors[theme].Buttonprimary, // Purple-700 for dark mode
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 3,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#8B5CF6" : "transparent", // Purple-500 for dark mode
    },
    selectDayButtonText: {
      fontSize: 16,
      fontWeight: "600",
      color: theme === "dark" ? "#C4B5FD" : colors[theme].ButtonText, // Purple-300 for dark mode
    },
    restTimerBlock: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "center",
      alignContent: "center",
      paddingHorizontal: 5,
    },
    restTimerContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme === "dark" ? "#92400E" : "#FEF3C7", // Amber-800 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 8,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#B45309" : "transparent", // Amber-700 for dark mode
    },
    restTimerText: {
      fontSize: 14,
      fontWeight: "500",
      color: theme === "dark" ? "#FCD34D" : "#B45309", // Amber-300 for dark mode
      marginLeft: 4,
      marginRight: 8,
    },
    skipRestButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme === "dark" ? "#92400E" : "#FEF3C7", // Amber-800 for dark mode
      paddingVertical: 4,
      paddingHorizontal: 8,
      borderRadius: 8,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#B45309" : "transparent", // Amber-700 for dark mode

      marginRight: 8,
    },
    skipRestText: {
      fontSize: 12,
      fontWeight: "500",
      color: theme === "dark" ? "#FCD34D" : "#B45309", // Amber-300 for dark mode
      marginLeft: 4,
    },
    controlsHelpContainer: {
      marginBottom: 16,
    },
    controlsHelpButton: {
      position: "absolute",
      top: 0,
      right: 0,
      padding: 8,
    },
    controlsHelpTextContainer: {
      backgroundColor: theme === "dark" ? "#1F2937" : "#E0F2FE", // Gray-800 for dark mode
      padding: 12,
      borderRadius: 8,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#4B5563" : "transparent", // Gray-600 for dark mode
    },
    controlsHelpText: {
      fontSize: 12,
      color: theme === "dark" ? "#9CA3AF" : "#3B82F6", // Gray-400 for dark mode
    },
    exerciseContainer: {
      backgroundColor:
        theme === "dark" ? "#1F2937" : colors[theme].cardBackground, // Gray-800 for dark mode
      borderWidth: 1,
      borderColor: theme === "dark" ? "#4B5563" : "#E5E7EB", // Neutral gray colors
      overflow: "hidden",
      borderRadius: 10,
      padding: 16,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
      elevation: 3,
      marginBottom: 8,
    },
    exerciseHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: 16,
    },
    exerciseName: {
      fontSize: 16,
      fontWeight: "500",
      color: theme === "dark" ? "#C4B5FD" : colors[theme].MeterTitle, // Purple-300 for dark mode
      flex: 1,
      textAlign: "right",
    },
    exerciseButtons: {
      flexDirection: "row",
    },
    exerciseButton: {
      backgroundColor:
        theme === "dark" ? "#7E22CE" : colors[theme].Buttonprimary, // Purple-800 for dark mode
      padding: 8,
      borderRadius: 8,
      marginLeft: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 3,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#6D28D9" : "transparent", // Purple-700 for dark mode
    },
    disabledButtonCommon: {
      opacity: 0.5, // Makes the button look inactive
    },
    pauseButton: {
      backgroundColor:
        theme === "dark" ? "#92400E" : colors[theme].Buttonprimary, // Amber-800 for dark mode
      padding: 8,
      borderRadius: 10,
      marginLeft: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 3,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#B45309" : "transparent", // Amber-700 for dark mode
    },
    playButton: {
      backgroundColor:
        theme === "dark" ? "#15803D" : colors[theme].Buttonprimary, // Green-800 for dark mode
      padding: 8,
      borderRadius: 10,
      marginLeft: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 3,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#16A34A" : "transparent", // Green-700 for dark mode
    },
    finishButton: {
      backgroundColor:
        theme === "dark" ? "#991B1B" : colors[theme].Buttonprimary, // Red-800 for dark mode
      padding: 8,
      borderRadius: 10,
      //   marginLeft: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 3,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#B91C1C" : "transparent", // Red-700 for dark mode
    },
    instructionsContainer: {
      backgroundColor:
        theme === "dark" ? "rgba(146, 64, 14, 0.4)" : colors[theme].ServingItem,
      padding: 12,
      borderRadius: 10,
      marginBottom: 16,
      borderWidth: theme === "dark" ? 1 : 1,
      borderColor: theme === "dark" ? "#B45309" : "#4A90E2", // Amber-700 for dark mode
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.15,
      shadowRadius: 2,
      //   elevation: 2,
    },
    instructionsText: {
      fontSize: 14,
      color: theme === "dark" ? "#FCD34D" : colors[theme].MeterTitle, // Amber-300 for dark mode
    },
    inputContainer: {
      marginBottom: 16,
    },
    inputLabel: {
      fontSize: 14,
      fontWeight: "500",
      color: theme === "dark" ? "#C4B5FD" : colors[theme].MeterTitle, // Purple-300 for dark mode
      marginBottom: 4,
      textAlign: "right",
      width: "100%",
    },
    input: {
      backgroundColor: theme === "dark" ? "#374151" : "#FFFFFF", // Gray-700 for dark mode
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 10,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#8B5CF6" : "#E5E7EB", // Purple-500 for dark mode
      fontSize: 14,
      color: theme === "dark" ? "#C4B5FD" : "#374151", // Purple-300 for dark mode
      marginBottom: 12,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 1,
      elevation: 1,
      textAlign: "right",
    },
    completeSetButton: {
      backgroundColor:
        theme === "dark" ? "#6D28D9" : colors[theme].Buttonprimary, // Purple-700 for dark mode
      paddingVertical: 12,
      borderRadius: 10,
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 3,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#8B5CF6" : "transparent", // Purple-500 for dark mode
    },
    completeSetButtonText: {
      fontSize: 16,
      fontWeight: "600",
      color: theme === "dark" ? "#C4B5FD" : colors[theme].ButtonText, // Purple-300 for dark mode
    },
    statsContainer: {
      marginTop: 20,
      borderRadius: 12,
      padding: 0,
    },
    remainingContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      paddingBottom: 12,
      gap: 8,
    },
    remainingItem: {
      flex: 1,
      flexDirection: "column",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 12,
      borderRadius: 12,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3,
      elevation: 2,
    },
    remainingLabel: {
      fontSize: 11,
      fontWeight: "600",
      marginBottom: 6,
      textAlign: "center",
      textAlignVertical: "center", // optional, helps center inside parent vertically
    },

    setsRemainingItem: {
      backgroundColor: theme === "dark" ? "#7E22CE" : "rgba(250, 245, 255, 1)", // Purple-800 for dark mode
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#6D28D9" : "transparent", // Purple-700 for dark mode
    },
    exercisesRemainingItem: {
      backgroundColor: theme === "dark" ? "#15803D" : "rgba(240, 253, 244, 1)", // Green-800 for dark mode
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#16A34A" : "transparent", // Green-700 for dark mode
    },
    durationItem: {
      backgroundColor: theme === "dark" ? "#1E40AF" : "rgba(239, 246, 255, 1)", // Blue-800 for dark mode
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#2563EB" : "transparent", // Blue-600 for dark mode
    },

    setsRemainingLabel: {
      color: theme === "dark" ? "#C4B5FD" : "rgba(107, 33, 168, 1)", // Purple-300 for dark mode
      fontWeight: 400,
    },
    exercisesRemainingLabel: {
      color: theme === "dark" ? "#86EFAC" : "rgba(22, 101, 52, 1)", // Green-300 for dark mode
      fontWeight: 400,
    },
    durationLabel: {
      color: theme === "dark" ? "#93C5FD" : "rgba(30, 64, 175, 1)", // Blue-300 for dark mode
      fontWeight: 400,
    },

    remainingValue: {
      fontSize: 22,
      fontWeight: "700",
      textAlign: "center",
    },
    setsRemainingValue: {
      color: theme === "dark" ? "#C4B5FD" : "rgba(88, 28, 135, 1)", // Purple-300 for dark mode
      fontWeight: 600,
    },
    exercisesRemainingValue: {
      color: theme === "dark" ? "#86EFAC" : "rgba(20, 83, 45, 1)", // Green-300 for dark mode
      fontWeight: 600,
    },
    durationValue: {
      color: theme === "dark" ? "#93C5FD" : "rgba(30, 58, 138, 1)", // Blue-300 for dark mode
      fontWeight: 600,
    },
    finishConfirmationContainer: {
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0,0,0,0.4)",
      justifyContent: "center",
      alignItems: "center",
      zIndex: 99,
      elevation: 10,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent background
      justifyContent: "center",
      alignItems: "center",
      padding: 20,
    },
    finishConfirmationBox: {
      backgroundColor: theme === "dark" ? "#1F2937" : "#FFFFFF", // Gray-800 for dark mode
      borderRadius: 14,
      padding: 24,
      width: "85%",
      maxWidth: 320,
      borderWidth: theme === "dark" ? 1 : 1,
      borderColor: theme === "dark" ? "#6D28D9" : colors[theme].primary, // Purple-700 for dark mode
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 5,
      //   elevation: 5,
    },
    finishConfirmationTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: theme === "dark" ? "#C4B5FD" : colors[theme].MeterTitle, // Purple-300 for dark mode
      marginBottom: 8,
    },
    finishConfirmationText: {
      fontSize: 14,
      color: theme === "dark" ? "#9CA3AF" : "#4B5563", // Gray-400 for dark mode
      marginBottom: 24,
    },
    finishConfirmationButtons: {
      flexDirection: "column",
      width: "100%",
      gap: 12,
    },
    finishConfirmationCancelButton: {
      backgroundColor:
        theme === "dark" ? "#374151" : colors[theme].Buttonsecondary, // Gray-700 for dark mode
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 10,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 2,
      elevation: 2,
      alignItems: "center",
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#4B5563" : "transparent", // Gray-600 for dark mode
    },
    finishConfirmationCancelText: {
      fontSize: 16,
      fontWeight: "500",
      color: theme === "dark" ? "#9CA3AF" : colors[theme].MeterTitle, // Gray-400 for dark mode
    },
    finishConfirmationFinishButton: {
      backgroundColor:
        theme === "dark" ? "#DC2626" : colors[theme].FinsihButton, // Red-600 for dark mode
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 10,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 3,
      elevation: 3,
      alignItems: "center",
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#EF4444" : "transparent", // Red-500 for dark mode
    },
    finishConfirmationFinishText: {
      fontSize: 16,
      fontWeight: "600",
      color: "#FFFFFF",
    },
    nextExerciseContainer: {
      marginTop: 16,
      backgroundColor: theme === "dark" ? "#1F2937" : "rgba(255, 259, 262, 1)", // Gray-800 for dark mode
      borderRadius: 10,
      padding: 12,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#4B5563" : "transparent", // Gray-600 for dark mode
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.15,
      shadowRadius: 2,
      elevation: 2,
    },
    nextExerciseLabel: {
      textAlign: "right",
      fontSize: 12,
      color: theme === "dark" ? "#9CA3AF" : "#6B7280", // Gray-400 for dark mode
      fontWeight: "500",
      marginBottom: 4,
    },
    nextExerciseName: {
      textAlign: "right",
      fontSize: 14,
      fontWeight: "600",
      color: theme === "dark" ? "#C4B5FD" : "rgba(31, 41, 55, 1)", // Purple-300 for dark mode
    },
    // at bottom of StyleSheet.create({...})
    errorBanner: {
      backgroundColor: theme === "dark" ? "rgba(153, 27, 27, 0.2)" : "#FEE2E2", // Red-900 with opacity for dark mode
      borderWidth: 1,
      borderColor: theme === "dark" ? "#EF4444" : "#F87171", // Red-500 for dark mode
      borderStyle: "solid",
      padding: 8,
      borderRadius: 8,
      marginBottom: 16,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 1,
    },
    errorText: {
      color: theme === "dark" ? "#FCA5A5" : "#B91C1C", // Red-300 for dark mode
      textAlign: "right",
      flex: 1,
      fontSize: 14,
    },
    errorDismiss: {
      color: theme === "dark" ? "#FCA5A5" : "#B91C1C", // Red-300 for dark mode
      fontWeight: "600",
      marginLeft: 12,
    },
    workoutInfoContainer: {
      paddingTop: 20,
      paddingBottom: 10,
      paddingHorizontal: 20,
      marginBottom: 20,
      borderRadius: 10,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#4B5563" : "#C5C9CE",
      backgroundColor: theme === "dark" ? "#1a1a2e" : "#F9FAFB",
    },
    workoutInfoHeaderContainer: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      width: "100%",
      marginBottom: 20,
    },
    workoutInfoTitle: {
      fontSize: 18,
      fontWeight: "bold",
      textAlign: "right",
      color: theme === "dark" ? "#ffffff" : "#111827",
      marginHorizontal: 5,
    },
    workoutInfoSettingsContainer: {
      //   flex: 1,
    },
    workoutInfoSettingItem: {
      marginBottom: 15,
      //   backgroundColor: theme === "dark" ? "#16213e" : "#FFFFFF",
      borderColor: theme === "dark" ? "#4B5563" : "#E5E7EB", // Neutral gray colors
    },
    workoutInfoSettingContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    workoutInfoSettingText: {
      fontSize: 12,
      textAlign: "right",
      flex: 1,
      marginRight: 5,
      lineHeight: 20,
      color: theme === "dark" ? "#ffffff" : "#374151",
    },
    workoutInfoOpenButton: {
      marginLeft: 8,
    },

    workoutCompleteMessageContainer: {
      backgroundColor:
        theme === "dark" ? "rgba(146, 64, 14, 0.4)" : colors[theme].ServingItem,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#B45309" : "#4A90E2",
      borderRadius: 8,
      padding: 10,
      display: "flex",
      flexDirection: "row", // RTL layout
      alignItems: "flex-end",
      marginBottom: 8,
    },

    workoutCompleteMessageNoteWrapper: {
      flexDirection: "column",
      alignItems: "center",
      maxWidth: 60, // constrain width so note text wraps under icon
      marginLeft: 8, // space between icon/note and message
    },

    workoutCompleteMessageNote: {
      color: theme === "dark" ? "#f09d1d" : "#4A90E2",
      fontSize: 14,
      fontWeight: "600",
      textAlign: "right",
    },

    workoutCompleteMessage: {
      width: "100%",
      fontSize: 14,
      color: theme === "dark" ? "#FCD34D" : colors[theme].MeterTitle, // Amber-300 for dark mode
      textAlign: "right",
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
    },
  });
