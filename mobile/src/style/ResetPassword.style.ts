import { StyleSheet, Platform } from "react-native";
import { colors } from "../theme/colors";

export const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors[theme].background,
    },
    card: {
      elevation: 3,
    },
    keyboardAvoidContainer: {
      flex: 1,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: 20,
      paddingTop: 40,
      paddingBottom: 20,
    },
    header: {
      marginTop: "10%",
      alignItems: "center",
      marginBottom: 32,
    },
    title: {
      fontSize: 28,
      fontWeight: "bold",
      color: colors[theme].MeterTitle,
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: colors[theme].MeterTitle,
    },
    formContainer: {
      width: "100%",
      maxWidth: 400,
      marginTop: 16,
    },
    inputContainer: {
      flexDirection: "row-reverse",
      alignItems: "center",
      borderWidth: 2,
      borderColor: "#E5E7EB",
      borderRadius: 12,
      marginBottom: 24,
      backgroundColor: "white",
    },
    iconContainer: {
      paddingHorizontal: 12,
    },
    input: {
      flex: 1,
      height: 56,
      paddingVertical: 12,
      paddingRight: 12,
      color: "#1F2937",
      fontSize: 16,
      textAlign: "right",
    },
    eyeIcon: {
      padding: 14,
    },
    resetButton: {
      backgroundColor: colors[theme].Buttonprimary,
      borderRadius: 12,
      height: 56,
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 12,
    },
    resendButton: {
      backgroundColor: colors[theme].Buttonprimary,
      borderRadius: 12,
      height: 48,
      justifyContent: "center",
      alignItems: "center",
    },
    disabledButton: {
      opacity: 0.7,
    },
    buttonText: {
      color: colors[theme].ButtonText,
      fontWeight: "600",
      fontSize: 16,
    },
    errorContainer: {
      backgroundColor: "#FEF2F2",
      borderWidth: 1,
      borderColor: "#F87171",
      borderRadius: 8,
      padding: 12,
      marginBottom: 16,
    },
    errorText: {
      color: "#B91C1C",
      fontSize: 14,
      textAlign: "right",
    },
    successContainer: {
      backgroundColor: "#F0FDF4",
      borderWidth: 1,
      borderColor: "#86EFAC",
      borderRadius: 8,
      padding: 12,
      marginBottom: 16,
    },
    successText: {
      color: "#166534",
      fontSize: 14,
      textAlign: "right",
    },
  });
