import { StyleSheet, Platform } from "react-native";
import { colors } from "../theme/colors";

export const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme === "dark" ? "#181825" : "#f5f6fa",
      width: "100%",
    },
    contentContainer: {
      //   flex: 1,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 24,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: theme === "dark" ? "#C4B5FD" : "#3B82F6",
      textAlign: "center",
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 24,
    },
    errorText: {
      fontSize: 16,
      color: "#E53E3E",
      textAlign: "center",
    },
    retryText: {
      marginTop: 16,
      fontSize: 16,
      color: "#4A90E2",
      fontWeight: "500",
      textAlign: "center",
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      //   padding: 24,
    },
    emptyCard: {
      backgroundColor: colors[theme].cardBackground,
      borderRadius: 12,
      padding: 24,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      borderWidth: 2,
      borderColor: theme === "dark" ? colors[theme].mealPlanBorder : "#BEE3F8",
      alignItems: "center",
      width: "100%",
    },
    emptyText: {
      fontSize: 16,
      color: theme === "dark" ? "#D1D5DB" : "#718096",
      textAlign: "center",
    },
    mealPlanCard: {
      backgroundColor:
        theme === "dark" ? "rgba(17, 24, 39, 1)" : colors[theme].cardBackground,
      //   borderRadius: 12,
      paddingHorizontal: 8,
      paddingTop: 12,
      paddingBottom: 8,
      flex: 1,
    },
    headerContainer: {
      flexDirection: "row-reverse",
      alignItems: "center",
      marginBottom: 15,
      gap: 12,
    },
    iconContainer: {
      padding: 12,
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#EBF8FF",
      borderRadius: 12,
    },
    headerTitle: {
      fontSize: 20,
      fontWeight: "600",
      color: colors[theme].MeterTitle,
    },
    totalMacrosContainer: {
      marginBottom: 24,
    },
    mealsContainer: {
      marginTop: 8,
    },
    mealCard: {
      backgroundColor:
        theme === "dark"
          ? colors[theme].mealCardBg
          : colors[theme].cardBackground,
      borderRadius: 10,
      marginBottom: 18,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.05,
      shadowRadius: 5,
      elevation: 2,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#4B5563" : "#E5E7EB", // Neutral gray colors
      overflow: "hidden",
    },
    mealHeader: {
      flexDirection: "row-reverse",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme === "dark" ? "#4B5563" : "#E5E7EB", // Neutral gray colors
    },
    mealHeaderContent: {
      flexDirection: "row-reverse",
      alignItems: "center",
      gap: 12,
    },
    mealIconContainer: {
      padding: 8,
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#EBF8FF",
      borderRadius: 8,
    },
    expandButtonContainer: {
      padding: 6,
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#EBF8FF",
      borderRadius: 8,
    },
    mealInfoContainer: {
      flexDirection: "row-reverse",
      alignItems: "center",
      gap: 4,
      marginTop: 4,
    },
    mealInfoText: {
      fontSize: 12,
      color: theme === "dark" ? "#9CA3AF" : "#6B7280",
    },
    expandedContent: {
      padding: 16,
      backgroundColor:
        theme === "dark"
          ? "rgba(17, 24, 39, 0.8)"
          : colors[theme].cardBackground,
    },
    mealTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: colors[theme].MeterTitle,
      textAlign: "right",
    },
    categoryContainer: {
      marginBottom: 24,
    },

    foodCategoryHeader: {
      paddingVertical: 3,
      paddingHorizontal: 5,
      marginBottom: 2,
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#EBF8FF",
      flexDirection: "row-reverse",
      alignItems: "center",
      //   marginBottom: 16,
      gap: 10,
      overflow: "hidden",
      borderRadius: 10,
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0,
    },
    carbsCategoryHeader: {
      paddingVertical: 3,
      paddingHorizontal: 5,
      marginBottom: 2,
      backgroundColor: theme === "dark" ? "rgba(146, 64, 14, 1)" : "#FEF3C7",
      flexDirection: "row-reverse",
      alignItems: "center",
      //   marginBottom: 16,
      gap: 10,
      overflow: "hidden",
      borderRadius: 10,
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0,
    },
    otherCategoryHeader: {
      paddingVertical: 3,
      paddingHorizontal: 5,
      marginBottom: 2,
      backgroundColor: theme === "dark" ? "rgba(20, 83, 45, 1)" : "#ECFDF5",
      flexDirection: "row-reverse",
      alignItems: "center",
      //   marginBottom: 16,
      gap: 10,
      overflow: "hidden",
      borderRadius: 10,
      borderBottomRightRadius: 0,
      borderBottomLeftRadius: 0,
    },

    categoryTitle: {
      fontSize: 14,
      fontWeight: "500",
      color: colors[theme].MeterTitle,
    },
    optionsContainer: {
      //   marginBottom: 8,
      //   gap: 12,
    },
    proteinOptionItem: {
      backgroundColor:
        theme === "dark" ? "rgba(31, 41, 55, 1)" : colors[theme].ServingItem,
      borderRadius: 5,
      padding: 8,
      marginBottom: 4,
      flexDirection: "row-reverse",
      alignItems: "center",
      justifyContent: "space-between",
    },
    carbOptionItem: {
      backgroundColor:
        theme === "dark" ? "rgba(31, 41, 55, 1)" : colors[theme].ServingItem,
      borderRadius: 5,
      padding: 8,
      marginBottom: 4,
      flexDirection: "row-reverse",
      alignItems: "center",
      justifyContent: "space-between",
    },
    proteinMacroValue: {
      fontSize: 14,
      fontWeight: "600",
      color: "#2B6CB0",
      backgroundColor: "rgba(235, 245, 255, 0.9)",
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
    },
    carbMacroValue: {
      fontSize: 14,
      fontWeight: "600",
      color: "#C05621",
      backgroundColor: "rgba(255, 248, 230, 0.9)",
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
    },
    othersMacroValue: {
      fontSize: 14,
      fontWeight: "600",
      color: "#29542d",
      backgroundColor: "rgba(41, 84, 45, 0.1)",
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 6,
    },
    foodItemContent: {
      flexDirection: "row-reverse",
      alignItems: "center",
      gap: 4,
      flex: 1,
    },
    foodIconContainer: {
      padding: 4,
      borderRadius: 999,
      backgroundColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#63B3ED",
    },
    carbsIconContainer: {
      padding: 4,
      borderRadius: 999,
      backgroundColor: theme === "dark" ? "rgba(146, 64, 14, 1)" : "#F6AD55",
    },
    othersIconContainer: {
      padding: 4,
      borderRadius: 999,
      backgroundColor: theme === "dark" ? "rgba(20, 83, 45, 1)" : "#34D399",
    },
    foodItemName: {
      fontSize: 14,
      fontWeight: "600",
      color: theme === "dark" ? "#E5E7EB" : "#374151",
      marginLeft: 4,
    },
    foodItemDetails: {
      flexDirection: "row-reverse",
      alignItems: "center",
    },
    servingText: {
      fontSize: 12,
      color: theme === "dark" ? "#FFFFFF" : "#374151",
      fontWeight: "500",
      textAlign: "center",
      backgroundColor: theme === "dark" ? "rgba(17, 24, 39, 1)" : "white",
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(55, 65, 81, 1)" : "#E5E7EB",
      overflow: "hidden",
    },
    macroDisplayContainer: {
      backgroundColor:
        theme === "dark"
          ? colors[theme].mealCardBg
          : colors[theme].cardBackground,
      borderRadius: 14,
      padding: 16,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#1F2937" : "#E2E8F0",
      marginTop: 0,
    },
    macroTitle: {
      fontSize: 16,
      fontWeight: "500",
      color: colors[theme].MeterTitle,
      marginBottom: 12,
      textAlign: "right",
    },
    macroGrid: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    macroColumn: {
      width: "48%",
      gap: 8,
    },
    macroItemProtein: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      height: 30,
      padding: 5,
      backgroundColor:
        theme === "dark" ? "rgba(88, 28, 135, 1)" : "rgba(239, 246, 255, 0.7)",
      borderRadius: 100,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(88, 28, 135, 1)" : "#63B3ED",
    },
    macroItemCarbs: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      height: 30,
      padding: 5,
      backgroundColor:
        theme === "dark" ? "rgba(146, 64, 14, 1)" : "rgba(255, 251, 235, 0.7)",
      borderRadius: 100,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(146, 64, 14, 1)" : "#F6AD55",
    },
    macroItemFats: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      height: 30,
      padding: 5,
      backgroundColor:
        theme === "dark" ? "rgba(20, 83, 45, 1)" : "rgba(240, 253, 244, 0.7)",
      borderRadius: 100,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(20, 83, 45, 1)" : "#34D399",
    },
    macroItemCalories: {
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      height: 30,
      padding: 5,
      backgroundColor:
        theme === "dark" ? "rgba(127, 29,29, 1)" : "rgba(254, 242, 242, 0.7)",
      borderRadius: 100,
      marginBottom: 8,
      borderWidth: 1,
      borderColor: theme === "dark" ? "rgba(127, 29,29, 1)" : "#FCA5A5",
    },
    macroItemHeader: {
      flexDirection: "row-reverse",
      alignItems: "center",
      gap: 6,
      marginBottom: 4,
    },
    macroItemLabel: {
      fontSize: 14,
      color: theme === "dark" ? "#ffffff" : "#4A5568",
      fontWeight: "500",
    },
    macroItemValue: {
      fontSize: 12,
      fontWeight: "600",
      color: theme === "dark" ? "#F9FAFB" : "#2D3748",
      textAlign: "right",
      flexShrink: 1,
    },
  });
