import { StyleSheet, Platform } from "react-native";
import { colors } from "../theme/colors";

export const createStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    // container: {
    //   flex: 1,
    //   backgroundColor: colors[theme].cardBackground,
    // },
    // contentContainer: {
    //   padding: 16,
    // },
    card: {
      backgroundColor: colors[theme].cardBackground,
      borderRadius: 12,
      padding: 16,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
      borderWidth: 2,
      borderColor: theme === "dark" ? "#4B5563" : "#E5E7EB", // Neutral gray colors
    },
    headerContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 16,
      gap: 12,
    },
    iconContainer: {
      padding: 12,
      backgroundColor: theme === "dark" ? "#4C1D95" : "#dbeafe", // Purple-900 for dark mode
      borderRadius: 12,
    },
    headerText: {
      fontSize: 18,
      fontWeight: "600",
      color: colors[theme].MeterTitle,
    },
    daysContainer: {
      gap: 16,
    },
    // dayCard: {
    //   backgroundColor: colors[theme].cardBackground,
    //   borderRadius: 8,
    //   padding: 16,
    //   marginBottom: 16,
    //   shadowColor: "#000",
    //   shadowOffset: { width: 0, height: 1 },
    //   shadowOpacity: 0.05,
    //   shadowRadius: 2,
    //   elevation: 2,
    //   borderWidth: 1,
    //   borderColor: theme === "dark" ? "#6D28D9" : "#e5e7eb", // Purple-700 for dark mode
    // },
    // dayTitle: {
    //   fontSize: 16,
    //   fontWeight: "600",
    //   color: colors[theme].MeterTitle,
    //   marginBottom: 12,
    //   textAlign: "right",
    // },
    // exercisesContainer: {
    //   gap: 12,
    // },
    // exerciseCard: {
    //   backgroundColor: colors[theme].cardBackground,
    //   borderRadius: 8,
    //   padding: 12,
    //   borderWidth: theme === "dark" ? 1 : 0,
    //   borderColor: theme === "dark" ? "#6D28D9" : "transparent", // Purple-700 for dark mode
    // },
    exerciseName: {
      fontWeight: "500",
      color: colors[theme].MeterTitle,
      marginBottom: 8,
      textAlign: "right",
    },
    exerciseStatsContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 8,
    },
    statCard: {
      flex: 1,
      padding: 8,
      borderRadius: 8,
      alignItems: "center",
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#4B5563" : "transparent", // Neutral gray colors
    },
    setsValue: {
      color: theme === "dark" ? "#C4B5FD" : "rgba(30, 64, 175,1)", // Purple-300 for dark mode
    },
    setsCard: {
      backgroundColor:
        theme === "dark" ? "rgba(76, 29, 149, 0.3)" : "rgba(239, 246, 255, 1)", // Purple-900 with opacity for dark mode
    },
    repsValue: {
      color: theme === "dark" ? "#86EFAC" : "rgba(22, 101, 52, 1)", // Green-300 for dark mode
    },
    repsCard: {
      backgroundColor:
        theme === "dark" ? "rgba(6, 78, 59, 0.3)" : "rgba(240, 253, 244, 1)", // Dark green with opacity for dark mode
    },
    restsValue: {
      color: theme === "dark" ? "#93C5FD" : "rgba(107, 33, 168, 1)", // Blue-300 for dark mode
    },
    restCard: {
      backgroundColor:
        theme === "dark" ? "rgba(30, 58, 138, 0.3)" : "rgba(250, 245, 255, 1)", // Dark blue with opacity for dark mode
    },
    statValue: {
      fontSize: 14,
      fontWeight: "500",
    },
    statLabel: {
      fontSize: 12,
    },
    instructionsCard: {
      marginTop: 8,
      backgroundColor: theme === "dark" ? "rgba(146, 64, 14, 0.2)" : "#fefce8", // Amber-900 with opacity for dark mode
      padding: 8,
      borderRadius: 8,
      borderWidth: theme === "dark" ? 1 : 0,
      borderColor: theme === "dark" ? "#4B5563" : "transparent", // Neutral gray colors
    },

    exerciseNameContainer: {
      width: "100%",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 8,
      paddingHorizontal: 4,
    },

    modalContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(0,0,0,0.5)",
    },
    modalContent: {
      width: "90%",
      backgroundColor: colors[theme].cardBackground,
      borderRadius: 14,
      paddingVertical: 4,
      paddingHorizontal: 10,
      borderWidth: theme === "dark" ? 2 : 0,
      borderColor: theme === "dark" ? "#4B5563" : "transparent", // Neutral gray colors
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      alignItems: "center",
    },
    modalHeader: {
      width: "100%",
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 3,
    },
    modalExerciseName: {
      color: colors[theme].MeterTitle,
      fontWeight: "700",
      paddingHorizontal: 7,
      fontSize: 15,
    },
    closeButton: {
      alignSelf: "flex-end",
      paddingVertical: 12,
      paddingRight: 2,
    },
    closeButtonText: {
      fontSize: 16,
      color: colors[theme].MeterTitle,
      fontWeight: "600",
    },
    videoPlayer: {
      minHeight: 220,
      width: "100%",
      borderRadius: 14,
      overflow: "hidden",
      marginBottom: 10,
      backgroundColor: "black",
    },
    videoButton: {
      backgroundColor: theme === "dark" ? "#4C1D95" : "#3b82f6", // Purple-900 for dark mode
      borderRadius: 5,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 3,
      paddingVertical: 5,
    },
    videoButtonIcon: {
      color: theme === "dark" ? "#C4B5FD" : "rgba(250, 245, 255, 1)", // Purple-300 for dark mode
      marginTop: 8,
      marginRight: 10,
      fontWeight: "500",
    },
    videoButtonText: {
      color: theme === "dark" ? "#C4B5FD" : "rgba(250, 245, 255, 1)", // Purple-300 for dark mode
      fontWeight: "500",
      fontSize: 10,
    },

    container: {
      flex: 1,
      backgroundColor: theme === "dark" ? "#181825" : "#f5f6fa",

      //   borderWidth: 2,
      //   borderColor: theme === "dark" ? colors[theme].mealPlanBorder : "#BEE3F8",
      //   borderRadius: 10,
    },

    contentContainer: {
      paddingHorizontal: 8,
      paddingTop: 12,
      paddingBottom: 8,
    },

    outerCard: {
      backgroundColor: colors[theme].themePrimaryBackground,
      borderRadius: 10,
      padding: 16,
      marginBottom: 24,
      shadowColor: "#000",
      shadowOpacity: 0.13,
      shadowRadius: 12,
      elevation: 6,
    },
    trainingHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "flex-end",
      marginBottom: 4,
    },
    trainingHeaderTextContainer: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "center",
      alignItems: "flex-end",
      marginRight: 8,
    },
    trainingTitle: {
      fontSize: 20,
      fontWeight: "bold",
      color: colors[theme].text,
      letterSpacing: 0.5,
    },
    editButton: {
      backgroundColor:
        theme === "dark" ? "rgba(88, 28, 135, 1)" : colors[theme].ServingItem,
      borderRadius: 8,
      padding: 7,
    },
    trainingSubtitle: {
      color: colors[theme].themeSecondary,
      fontSize: 14,
      //   marginBottom: 8,
      textAlign: "right",
    },
    tipBox: {
      backgroundColor: colors[theme].tipBoxBackground,
      borderWidth: 2,
      borderColor: colors[theme].tipBoxBorder,
      borderRadius: 10,
      padding: 10,
      marginTop: 4,
      width: "100%",
      display: "flex",
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
    },
    tipText: {
      color: colors[theme].tipBoxText,
      fontSize: 11,
      fontWeight: "600",
      textAlign: "right",
    },
    tipIcon: {
      color: colors[theme].tipBoxIcon,
    },
    dayCard: {
      backgroundColor: theme === "dark" ? "#1f2937" : "#fff",
      borderRadius: 10,
      marginBottom: 18,
      borderWidth: 1,
      borderColor: theme === "dark" ? "#4B5563" : "#E5E7EB", // Neutral gray colors
      shadowColor: "#000",
      shadowOpacity: 0.09,
      shadowRadius: 8,
      elevation: 3,
      overflow: "hidden",
    },

    dayHeader: {
      flexDirection: "row",
      alignItems: "center",
      padding: 16,
      borderBottomWidth: 1,
      borderBottomColor: "#a78bfa33",
    },
    calendarIcon: {
      marginLeft: 4,
    },
    dayTitle: {
      fontSize: 18,
      fontWeight: "bold",
      color: colors[theme].dayTitle,
      flex: 1,
      textAlign: "right",
      marginRight: 5,
    },
    dayTip: {
      color: "#a78bfa",
      fontSize: 13,
      marginBottom: 12,
      textAlign: "center",
    },

    dayCardActive: {
      //   borderColor: theme === "dark" ? "#c4b5fd" : "#84BDFD",
      shadowOpacity: 0.18,
      elevation: 8,
    },
    CollapsedDayCard: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 16,
      backgroundColor: colors[theme].CollapsedDayCardBackground,

      //   paddingVertical: 8,
      //   paddingHorizontal: 6,
    },
    CollapsedDayUpperContainer: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
      width: "100%",
    },
    chevronButtonStyles: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: colors[theme].themePrimary,
      borderRadius: 8,
      padding: 6,
    },

    CollapsedDayLowerContainer: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      width: "100%",
      marginTop: 7,
    },
    CollapsedDayLowerInfo: {
      color: colors[theme].CollapsedDayCardLowerText,
      fontSize: 11,
    },
    exerciseCount: {
      color: colors[theme].exerciseCount,
      fontWeight: "600",
      fontSize: 11,
      marginRight: 3,
    },

    exerciseCard: {
      shadowColor: "#000",
      shadowOpacity: 0.04,
      shadowRadius: 4,
      elevation: 2,
      borderTopWidth: 2,

      borderColor: theme === "dark" ? "#4B5563" : "#E5E7EB", // Neutral gray colors
    },
    exerciseRow: {
      backgroundColor: colors[theme].exerciseRow,
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-around",
      alignItems: "center",
      width: "100%",
      padding: 10,
    },
    exerciseNameBtn: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
      width: "100%",
      marginBottom: 8,
    },
    exerciseNameText: {
      color: colors[theme].dayTitle,
      fontWeight: "bold",
      fontSize: 15,
      marginRight: 3,
    },
    statsRow: {
      flexDirection: "row",
      justifyContent: "flex-end",
      gap: 8,
      width: "100%",
    },

    statPillRest: {
      backgroundColor: colors[theme].statPillRest,
      paddingHorizontal: 10,
      paddingVertical: 2,
    },
    statPillReps: {
      backgroundColor: colors[theme].statPillReps,
      paddingHorizontal: 10,
      paddingVertical: 2,
    },
    statPillSets: {
      backgroundColor: colors[theme].statPillSets,
      paddingHorizontal: 10,
      paddingVertical: 2,
    },

    statPill: {
      alignItems: "center",
      padding: 8,
      borderRadius: 8,
      minWidth: 60,
    },

    statRestText: {
      color: colors[theme].statRestText,
      fontSize: 12,
      fontWeight: "500",
    },
    statRepsText: {
      color: colors[theme].statRepsText,
      fontSize: 12,
      fontWeight: "500",
    },
    statSetsText: {
      color: colors[theme].statSetsText,
      fontSize: 12,
      fontWeight: "500",
    },

    exerciseDetails: {
      backgroundColor: colors[theme].exerciseDetails,
      padding: 10,
    },

    instructionsContainer: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      alignItems: "flex-end",
      width: "100%",
      backgroundColor: colors[theme].instructionsContainerBg,
      padding: 10,
      borderRadius: 10,
      marginTop: 8,
    },

    exerciseDetailsInnerContainer: {
      backgroundColor: colors[theme].exerciseDetailsInnerContainerBg,
      padding: 10,
      borderRadius: 10,
    },

    instructionsTextHeaderContainer: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
    },

    instructionsTextHeader: {
      color: colors[theme].instructionsTextHeader,
      width: "100%",
      textAlign: "right",
      fontSize: 12,
      marginRight: 3,
    },

    instructionsText: {
      color: colors[theme].instructionsText,
      width: "100%",
      textAlign: "right",
      fontSize: 12,
    },

    exercisStatsExpandedContainer: {
      paddingTop: 10,
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      gap: 8,
      width: "100%",
    },
    expandedStatPill: {
      display: "flex",
      flexDirection: "column",
      justifyContent: "space-between",
      alignItems: "center",
      padding: 8,
      borderRadius: 10,
      minWidth: 85,
      borderWidth: 1,
    },

    expandedStatPillRest: {
      color: colors[theme].expandedStatPillRestText,
      borderColor:
        theme === "dark" ? "#346634" : colors[theme].expandedStatPillRestBorder,
      backgroundColor: colors[theme].expandedStatPillRestBg,
      fontSize: 10,
    },

    expandedStatPillReps: {
      color: colors[theme].expandedStatPillRepsText,
      borderColor:
        theme === "dark" ? "#924011" : colors[theme].expandedStatPillRepsBorder,
      backgroundColor: colors[theme].expandedStatPillRepsBg,
      fontSize: 10,
    },

    expandedStatPillSets: {
      color: theme === "dark" ? "#C4B5FD" : colors[theme].statSetsText,
      borderColor:
        theme === "dark" ? "#704ca8" : colors[theme].expandedStatPillSetsBorder,
      backgroundColor: colors[theme].expandedStatPillSetsBg,
      fontSize: 10,
    },

    expandedExerciseDetailsName: {
      color: colors[theme].expandedExerciseDetailsName,
      marginRight: 3,
      fontSize: 12,
    },

    expandedExerciseHeaderContainer: {
      display: "flex",
      flexDirection: "row",
      alignItems: "center",
      width: "100%",
    },

    expandedExerciseNameContainer: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "center",
      textAlign: "right",
    },

    exerciseVideoButtton: {
      backgroundColor: colors[theme].exerciseVideoButttonBg,
      borderWidth: theme === "dark" ? 0 : 0.5,
      borderColor:
        theme === "dark" ? "#4B5563" : colors[theme].exerciseVideoButttonText,
      borderRadius: 10,
      paddingHorizontal: 8,
      paddingVertical: 2,
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },

    exerciseVideoButttonText: {
      fontSize: 10,
      paddingRight: 2,
      color: colors[theme].exerciseVideoButttonText,
    },

    noTrainingPlanText: {
      color: colors[theme].noTrainingPlanText,
      textAlign: "center",
      fontSize: 12,
    },

    loadingContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 24,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: theme === "dark" ? "#C4B5FD" : "#3B82F6",
      textAlign: "center",
    },
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 24,
    },
    errorText: {
      fontSize: 16,
      color: "#E53E3E",
      textAlign: "center",
    },
    retryText: {
      marginTop: 16,
      fontSize: 16,
      color: "#4A90E2",
      fontWeight: "500",
      textAlign: "center",
    },
  });
