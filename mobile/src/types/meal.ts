interface MacroNutrients {
    protein: number;
    carbs: number;
    fats: number;
    calories: number;
  }
  
  interface MacroRange {
    min: MacroNutrients;
    max: MacroNutrients;
  }
  
  interface FoodItem {
    id: string;
    name: string;
    category: string;
    defaultServing: number;
    macrosPer100g: MacroNutrients;
  }
  
  interface MealCategory {
    name: string;
    options: {
      foodId: string;
      amount: number;
    }[];
  }
  
  interface MealWithFood {
    id: string;
    name: string;
    categories: MealCategory[];
    macroRange: MacroRange;
    foodItems?: FoodItem[];
  }
  
  interface UserMealPlanProps {
    userId: string;
  }

  export { MacroNutrients, MacroRange, FoodItem, MealCategory, MealWithFood, UserMealPlanProps }