import React, { useState, useEffect, useCallback } from 'react';
import { Layout, Calendar, <PERSON><PERSON><PERSON>, ClipboardList, Map } from 'lucide-react';
import { Overview } from '../components/tabs/Overview';
import { UserMealPlan } from '../components/user/UserMealPlan';
import { UserWorkoutPlan } from '../components/user/UserWorkoutPlan';
import { WorkoutLogger } from '../components/user/WorkoutLogger';
import { RestTimer } from '../components/workout/RestTimer';
import { ProgressRoadmap } from '../components/roadmap/ProgressRoadmap';
import { getOverviewById } from '../data/overview';
import { WorkoutProvider } from '../contexts/WorkoutContext';
import { fatLossPath, muscleGainPath } from '../types/roadmap';
import { useAuth } from '../contexts/AuthContext';
import { authApi } from '../service/logout.api';
import defaultProfile from '../default-profile-image.png';

interface UserViewProps {
  userId: string;
  onBackToSelect: () => void;
}

export const UserView: React.FC<UserViewProps> = ({ userId }) => {
  const { logout } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [isWorkoutInProgress, setIsWorkoutInProgress] = useState(false);
  const [restTimer, setRestTimer] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const userOverview = getOverviewById(userId);

  const userDataString = localStorage.getItem('user');


  const handleLogout = useCallback(async () => {
    try {
      const token = localStorage.getItem('access_token')

      if (token)
        await authApi.logout({ token })
      logout()
    } catch (error) {
      logout();
    }
  }, [logout]);

  if (!userDataString) {
    return;
  }

  const userData = JSON.parse(userDataString);

  const tabs = [
    { id: "overview", label: "Overview", icon: Layout },
    { id: "meal-plan", label: "Meal Plan", icon: Calendar },
    { id: "workout-plan", label: "Training Plan", icon: Dumbbell },
    { id: "workout-logger", label: "Workout Log", icon: ClipboardList },
    { id: "roadmap", label: "Progress Map", icon: Map }
  ];

  useEffect(() => {
    let interval: any;
    if (restTimer > 0 && !isPaused) {
      interval = setInterval(() => {
        setRestTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [restTimer, isPaused]);

  useEffect(() => {
    if (restTimer === 0 && isWorkoutInProgress && activeTab !== "workout-logger") {
      if (Notification.permission === "granted") {
        const notification = new Notification("זמן המנוחה הסתיים!", {
          body: "חזור לאימון כדי להמשיך",
          icon: "/vite.svg"
        });

        notification.onclick = () => {
          setActiveTab("workout-logger");
          window.focus();
        };
      }
    }
  }, [restTimer, isWorkoutInProgress, activeTab]);

  useEffect(() => {
    if (isWorkoutInProgress && Notification.permission === "default") {
      Notification.requestPermission();
    }
  }, [isWorkoutInProgress]);

  const handleWorkoutStart = () => {
    setIsWorkoutInProgress(true);
  };

  const handleWorkoutEnd = () => {
    setIsWorkoutInProgress(false);
    setRestTimer(0);
    setIsPaused(false);
  };

  const handleRestStart = (duration: number) => {
    setRestTimer(duration);
  };

  const handleRestSkip = () => {
    setRestTimer(0);
  };

  const handleRestComplete = () => {
    if (activeTab !== "workout-logger") {
      if (Notification.permission === "granted") {
        new Notification("זמן המנוחה הסתיים!", {
          body: "חזור לאימון כדי להמשיך",
          icon: "/vite.svg"
        });
      }
    }
  };

  const handlePauseToggle = (paused: boolean) => {
    setIsPaused(paused);
  };

  return (
    <WorkoutProvider>
      <div className="min-h-screen bg-gray-100 relative">
        <div className="bg-white border-b border-gray-200 px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={handleLogout}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-800"
            >
              Logout
            </button>
            <div className="flex items-center gap-3">
              <img
                src={defaultProfile}
                alt=""
                className="h-8 w-8 rounded-full"
              />
              <span className="font-medium text-gray-900">{userData.name}</span>
            </div>
          </div>
        </div>

        <div className="p-4 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200 flex gap-1" dir="rtl">
          {tabs.map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id)}
              className={`
                p-3 text-sm font-semibold uppercase transition-all duration-200 rounded-lg
                flex items-center gap-2 group
                ${activeTab === id
                  ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md"
                  : "text-gray-500 hover:bg-gray-100"
                }
              `}
            >
              <Icon className={`w-4 h-4 ${activeTab === id
                ? "text-white transform group-hover:rotate-12 transition-transform duration-300"
                : "text-gray-400 group-hover:text-blue-500 transform group-hover:rotate-12 transition-all duration-300"
                }`} />
              {label}
            </button>
          ))}
        </div>

        <div className="p-6">
          {activeTab === "overview" && (
            <Overview userOverview={userOverview} />
          )}
          {activeTab === "meal-plan" && (
            <UserMealPlan userId={userId} />
          )}
          {activeTab === "workout-plan" && (
            <UserWorkoutPlan userId={userId} />
          )}
          {activeTab === "workout-logger" && (
            <WorkoutLogger
              userId={userId}
              onWorkoutStart={handleWorkoutStart}
              onWorkoutEnd={handleWorkoutEnd}
              onRestStart={handleRestStart}
              onPauseToggle={handlePauseToggle}
              restTimer={restTimer}
              isPaused={isPaused}
            />
          )}
          {activeTab === "roadmap" && (
            <ProgressRoadmap path={userOverview.goal === 'fat_loss' ? fatLossPath : muscleGainPath} />
          )}
        </div>

        {/* Floating Rest Timer */}
        {isWorkoutInProgress && restTimer > 0 && activeTab !== "workout-logger" && (
          <RestTimer
            time={restTimer}
            onSkip={handleRestSkip}
            onComplete={handleRestComplete}
            isPaused={isPaused}
          />
        )}
      </div>
    </WorkoutProvider>
  );
};