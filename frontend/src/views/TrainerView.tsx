import React, { useCallback } from 'react';
import App from '../components/App';
import { useAuth } from '../contexts/AuthContext';
import { authApi } from '../service/logout.api';

interface TrainerViewProps {
  onBackToSelect: () => void;
}

export const TrainerView: React.FC<TrainerViewProps> = () => {
  const { logout } = useAuth();

  const handleLogout = useCallback(async () => {
    try {
      const token = localStorage.getItem('access_token')

      if(token)
      await authApi.logout({token})
      logout()
    } catch (error) {
      logout();
    }
  }, [logout]);

  return (
    <div>
      <div className="bg-white border-b border-gray-200 px-4 py-4 flex justify-between items-center">
        <button
          onClick={handleLogout}
          className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-800 ml-auto"
        >
          Logout
        </button>
      </div>
      <App />
    </div>
  );
};
