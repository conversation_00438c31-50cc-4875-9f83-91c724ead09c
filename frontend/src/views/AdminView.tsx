import React, { useState, useCallback } from "react";
import {
  Users, Activity, TrendingUp, Target, UserPlus, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
} from "lucide-react";
import { useAdmin } from "../contexts/AdminContext";
import { calculateSystemAnalytics } from "../utils/adminAnalytics";
import { AdminAnalytics } from "../components/admin/AdminAnalytics";
import { AdminSettings } from "../components/admin/AdminSettings";
import { AdminTrainers } from '../components/admin/AdminTrainers';
import { useAuth } from '../contexts/AuthContext';
import { authApi } from '../service/logout.api';


// Define proper TypeScript interfaces for our data
interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  trainerId: string;
  status: 'active' | 'inactive';
  goal: 'fat_loss' | 'muscle_gain' | 'general_fitness';
  startDate: string;
  lastActive: string;
}

interface Trainer {
  id: string;
  // Add other trainer properties as needed
}

interface WorkoutLog {
  id: string;
  userId: string;
  trainerId: string;
  date: string;
  type: string;
  completed: boolean;
  metrics: {
    duration: number;
    calories: number;
    intensity: number;
  }
}

interface Analytics {
  activeUsers: number;
  userGrowth: number;
  goalAchievement: number;
  userActivity: Activity[];
  trainerPerformance: TrainerPerformance[];
  categoryAchievement: CategoryAchievement[];
}

interface Activity {
  type: string;
  count: number;
  time: string;
}

interface TrainerPerformance {
  name: string;
  trainees: number;
  success: number;
}

interface CategoryAchievement {
  category: string;
  rate: number;
}

// Props interfaces for our components
interface OverviewCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  bgColor: string;
  borderColor: string;
  subtext?: string;
}

interface RecentActivityProps {
  activities: Activity[];
}

interface PerformanceMetricsProps {
  title: string;
  data: TrainerPerformance[] | CategoryAchievement[];
}

// Tab interface
interface TabItem {
  id: string;
  label: string;
  icon: React.ReactNode;
}

// Reusable Overview Card Component
const OverviewCard: React.FC<OverviewCardProps> = ({
  title, value, icon, bgColor, borderColor, subtext
}) => (
  <div className={`bg-white rounded-xl shadow-lg p-6 border-2 ${borderColor}`}>
    <div className="flex items-center gap-3 mb-4">
      <div className={`p-3 rounded-xl ${bgColor}`}>{icon}</div>
      <div>
        <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        <p className="text-3xl font-bold text-gray-800">{value}</p>
      </div>
    </div>
    {subtext && <div className="text-sm text-green-600 flex items-center gap-2">
      <TrendingUp className="h-4 w-4" />
      <span>{subtext}</span>
    </div>}
  </div>
);

// Recent Activity Component
const RecentActivity: React.FC<RecentActivityProps> = ({ activities }) => (
  <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
    <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
    <div className="space-y-4">
      {activities.map((activity, index) => (
        <div key={index} className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="p-2 bg-blue-100 rounded-lg">
            {activity.type === "Workouts Logged" ? <Activity className="h-5 w-5 text-blue-600" />
              : activity.type === "Meal Plans Updated" ? <UserPlus className="h-5 w-5 text-blue-600" />
                : <Target className="h-5 w-5 text-blue-600" />}
          </div>
          <div>
            <p className="font-medium text-gray-800">{activity.type}</p>
            <p className="text-sm text-gray-500">{activity.count} activities recorded</p>
          </div>
          <span className="ml-auto text-sm text-gray-500">{activity.time}</span>
        </div>
      ))}
    </div>
  </div>
);

// Performance Metrics Component with type guard
const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({ title, data }) => {
  // Type guard function to check if item is TrainerPerformance
  const isTrainerPerformance = (item: TrainerPerformance | CategoryAchievement): item is TrainerPerformance => {
    return 'trainees' in item && 'success' in item;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">{title}</h3>
      <div className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-gray-700">
                {isTrainerPerformance(item) ? item.name : item.category}
              </span>
              <span className="text-sm font-medium text-gray-600">
                {isTrainerPerformance(item)
                  ? `${item.trainees} trainees`
                  : `${item.rate}%`}
              </span>
            </div>
            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full ${isTrainerPerformance(item) ? "bg-blue-600" : "bg-green-600"} rounded-full`}
                style={{ width: `${isTrainerPerformance(item) ? item.success : item.rate}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Main Dashboard Content Component
const DashboardContent: React.FC<{
  users: User[];
  trainers: Trainer[];
  workoutLogs: WorkoutLog[];
}> = ({ users, trainers, workoutLogs }) => {
  const analytics: Analytics = calculateSystemAnalytics(users, workoutLogs);

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <OverviewCard
          title="Total Trainers"
          value={trainers.length}
          icon={<Users className="h-6 w-6 text-blue-600" />}
          bgColor="bg-blue-100"
          borderColor="border-blue-200"
          subtext={`Managing ${users.length} trainees`}
        />
        <OverviewCard
          title="Active Trainees"
          value={analytics.activeUsers}
          icon={<Activity className="h-6 w-6 text-purple-600" />}
          bgColor="bg-purple-100"
          borderColor="border-purple-200"
          subtext={`${analytics.userGrowth.toFixed(1)}% growth rate`}
        />
        <OverviewCard
          title="Goal Progress"
          value={`${analytics.goalAchievement.toFixed(1)}%`}
          icon={<Target className="h-6 w-6 text-emerald-600" />}
          bgColor="bg-emerald-100"
          borderColor="border-emerald-200"
          subtext="Average trainee milestone completion"
        />
      </div>

      {/* Recent Activity */}
      <RecentActivity activities={analytics.userActivity} />

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <PerformanceMetrics title="Trainer Performance" data={analytics.trainerPerformance} />
        <PerformanceMetrics title="Goal Achievement by Category" data={analytics.categoryAchievement} />
      </div>
    </div>
  );
};


// Main AdminDashboard Component with Tabs
export const AdminDashboard: React.FC = () => {
  const [trainerTabClicked, setTrainerTabClicked] = useState(false);
  const [selectedTrainerId, setSelectedTrainerId] = useState<string | null>(null);
  const { logout } = useAuth();

  const { users, trainers, workoutLogs } = useAdmin() as {
    users: User[];
    trainers: Trainer[];
    workoutLogs: WorkoutLog[];
  };

  const handleLogout = useCallback(async () => {
    try {
      const token = localStorage.getItem('access_token')

      if(token)
      await authApi.logout({token})
      logout()
    } catch (error) {
      logout();
    }
  }, [logout]);

  // Define tabs
  const tabs: TabItem[] = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: <Activity className="h-5 w-5" />
    },
    {
      id: "analytics",
      label: "Analytics",
      icon: <BarChart className="h-5 w-5" />
    },
    {
      id: "trainers",
      label: "Trainers",
      icon: <Users className="h-5 w-5" />
    },
    {
      id: "settings",
      label: "Settings",
      icon: <Settings className="h-5 w-5" />
    }
  ];

  // State to track active tab
  const [activeTab, setActiveTab] = useState<string>("dashboard");

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    
    // Set trainerTabClicked to true only when trainers tab is selected
    if (tabId === "trainers") {
      setTrainerTabClicked(true);
    }
  };

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case "dashboard":
        return <DashboardContent users={users} trainers={trainers} workoutLogs={workoutLogs} />;
      case "analytics":
        return <AdminAnalytics />;
      case "trainers":
        return <AdminTrainers 
                 trainerTabClicked={trainerTabClicked} 
                 onTrainerSelect={setSelectedTrainerId}
                />;
      case "settings":
        return <AdminSettings />;
      default:
        return <DashboardContent users={users} trainers={trainers} workoutLogs={workoutLogs} />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <Dumbbell className="w-6 h-6 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-800">Raeda-AI</span>

          </div>
          <h1 className="text-xl font-bold text-gray-800">תצוגת מנהל</h1>
          <div className="bg-white border-b border-gray-200 px-4 py-4 flex justify-between items-center">

            <button
              onClick={handleLogout}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-800 ml-auto"
            >
              Logout
            </button>
          </div>
        </div>

      </div>
      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-md mb-6">
        <div className="flex overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={`flex items-center px-6 py-4 text-sm font-medium transition-colors border-b-2 whitespace-nowrap ${activeTab === tab.id
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-300"
                }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-xl shadow-md p-6">
        {renderContent()}
      </div>
    </div>
  );
};

export default AdminDashboard;