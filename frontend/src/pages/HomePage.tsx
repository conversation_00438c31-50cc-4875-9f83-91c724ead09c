import { useState, useEffect } from "react";
import { TrainerView } from "../views/TrainerView";
import { UserView } from "../views/UserView";
import { MealPlanProvider } from "../contexts/MealPlanContext";
import { WorkoutLogProvider } from "../contexts/WorkoutLogContext";
import { CustomLoader } from "../components/loader/CustomLoader";
import { useAuth } from "../contexts/AuthContext";
import { Navigate } from "react-router-dom";

export const HomePage = () => {
  const { isAuthenticated, user } = useAuth();
  const [view, setView] = useState<'select' | 'trainer' | 'user'>('select');
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [availableUsers, setAvailableUsers] = useState<Array<{ id: string; name: string }>>([]);

  useEffect(() => {
    if (!user) return;
    const storedUser = JSON.parse(localStorage.getItem("user") || "{}");
    const role = storedUser?.role?.name;
    
    if (role === "ROLE_TRAINEE") {
      setView("user");
      setSelectedUserId(storedUser.id);
    } else if (role === "ROLE_TRAINER") {
      setView("trainer");
      setAvailableUsers([]); // Fetch available users if needed
    }
  }, [user]);

  if (!isAuthenticated) return <Navigate to="/login" />;

  const renderView = () => {
    switch (view) {
      case "trainer":
        return <TrainerView onBackToSelect={() => setView("select")} />;
      case "user":
        return selectedUserId ? <UserView userId={selectedUserId} onBackToSelect={() => setView("select")} /> : <CustomLoader />;
      default:
        return <CustomLoader />;
    }
  };

  return (
    <MealPlanProvider>
      <WorkoutLogProvider>{renderView()}</WorkoutLogProvider>
    </MealPlanProvider>
  );
};
