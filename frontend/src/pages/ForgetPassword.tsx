import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Mail, Send } from "lucide-react";
import axios from "axios";
import { otpApi } from "../service/forget-password.api";

interface ForgetPasswordProps {}

export const ForgetPassword: React.FC<ForgetPasswordProps> = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  // Effect to clear messages after 3 seconds
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (error || success) {
      timeoutId = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 3000);
    }

    // Cleanup function to clear timeout
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [error, success]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // Clear previous messages
    setError(null);
    setSuccess(null);
    setIsSubmitting(true);

    try {
      // Replace with your actual API call
      const response = await otpApi.forgotPassword({ email });

      setSuccess("Password reset instructions sent to your email.");

      // Navigate to reset password page after short delay
      setTimeout(() => {
        navigate(`/reset-password?email=${email}`, { state: { email } });
      }, 2000);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        // Handle specific error responses
        if (error.response) {
          setError(
            error.response.data.message ||
              "Failed to send reset instructions. Please try again."
          );
        } else if (error.request) {
          setError(
            "No response from server. Please check your internet connection."
          );
        } else {
          setError("An unexpected error occurred. Please try again.");
        }
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
      console.error("Error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-lg border-2 border-blue-200">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Raeda-AI</h1>
          <p className="text-lg text-gray-600">Forgot Password</p>
        </div>

        {/* Error Message */}
        {error && (
          <div
            className="bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded relative transition-all duration-300 ease-in-out"
            role="alert"
          >
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div
            className="bg-green-50 border border-green-400 text-green-700 px-4 py-3 rounded relative transition-all duration-300 ease-in-out"
            role="alert"
          >
            <span className="block sm:inline">{success}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div className="space-y-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                placeholder="Enter your email address"
                autoComplete="email"
              />
            </div>
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full flex justify-center items-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-semibold ${
              isSubmitting ? "opacity-70 cursor-not-allowed" : ""
            }`}
          >
            <Send className="h-5 w-5" />
            {isSubmitting ? "Sending..." : "Send Otp"}
          </button>

          <div className="text-center mt-4 space-y-2">
            <button
              type="button"
              onClick={() => navigate("/login")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Back to Login
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
