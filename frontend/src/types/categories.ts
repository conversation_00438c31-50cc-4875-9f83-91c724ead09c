
interface Categories {
  id: string;
  name: string;
  category: string;
}


export const Categories: Categories[] = [
  {
    id: 'chest',
    name: 'Chest',
    category: 'chest'
  },
  // Back
  {
    id: 'back',
    name: 'Back',
    category: 'back',
  },
  // Legs
  {
    id: 'legs',
    name: 'Legs',
    category: 'legs',
  },

  // Shoulders
  {
    id: 'shoulders',
    name: 'Shoulders',
    category: 'shoulders',
  },

  // Arms
  {
    id: 'Arms',
    name: 'Arms',
    category: 'arms',
  },

  // Abs
  {
    id: 'abs',
    name: 'Abs',
    category: 'abs',
  },

  // Cardio
  {
    id: 'cardio',
    name: 'Card<PERSON>',
    category: 'cardio',
  },
];
