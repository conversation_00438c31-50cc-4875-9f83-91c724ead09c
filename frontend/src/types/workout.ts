export interface WorkoutSet {
  weight: number;
  reps: number;
  notes?: string;
}

export interface WorkoutExercise {
  id?: string;
  name: string;
  sets: WorkoutSet[];
  category?: string;
}

export interface WorkoutLog {
  id: string | null;
  date: string;
  type: string | null;
  duration: string;
  exercises: WorkoutExercise[];
  notes?: string;
  trainingPlanId?: string | null;
  userId?: string;
  trainingPlan?: {
    day: string,
    focus: string
  }
}