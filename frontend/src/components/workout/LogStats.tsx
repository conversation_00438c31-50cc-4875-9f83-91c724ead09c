import React from 'react';
import { WorkoutLog } from '../../types/workout';
import { Activity, Weight, Trophy, TrendingUp } from 'lucide-react';

interface LogStatsProps {
  logs: WorkoutLog[];
}

export const LogStats: React.FC<LogStatsProps> = ({ logs }) => {

  const completedLogs = (logs || []).filter(log => 
    log?.exercises?.some(exercise => exercise.sets && exercise.sets.length > 0)
  );
  // Calculate total completed workouts
  const totalWorkouts = completedLogs.length;
  
  // Calculate total volume (weight × reps for all sets)
  const totalVolume = completedLogs.reduce((total, log) => {
    return total + log.exercises.reduce((exerciseTotal, exercise) => {
      return exerciseTotal + (exercise.sets || []).reduce((setTotal, set) => {
        return setTotal + (set.weight * set.reps);
      }, 0);
    }, 0);
  }, 0);

  // Calculate average volume per workout
  const averageVolume = totalWorkouts > 0 ? Math.round(totalVolume / totalWorkouts) : 0;

  // Find the most common exercise
  const exerciseCounts: Record<string, number> = {};
  const exerciseVolumes: Record<string, number> = {};
  
  completedLogs.forEach(log => {
    log.exercises.forEach(exercise => {
      if (exercise.sets && exercise.sets.length > 0) {
        // Count exercise occurrence
        exerciseCounts[exercise.name] = (exerciseCounts[exercise.name] || 0) + 1;
        averageVolume
        // Sum up volume for this exercise
        const exerciseVolume = exercise.sets.reduce((total, set) => 
          total + (set.weight * set.reps), 0);
        exerciseVolumes[exercise.name] = (exerciseVolumes[exercise.name] || 0) + exerciseVolume;
      }
    });
  });
  
  // Find favorite exercise (most frequently performed)
  let favoriteExercise = "";
  let highestCount = 0;
  
  Object.entries(exerciseCounts).forEach(([name, count]) => {
    if (count > highestCount) {
      favoriteExercise = name;
      highestCount = count;
    }
  });
  
  // Find strongest exercise (highest total volume)
  let strongestExercise = "";
  let highestVolume = 0;
  
  Object.entries(exerciseVolumes).forEach(([name, volume]) => {
    if (volume > highestVolume) {
      strongestExercise = name;
      highestVolume = volume;
    }
  });

  // Scheduled vs. completed workouts
  const scheduledWorkouts = logs.length;
  const completionRate = scheduledWorkouts > 0 
    ? Math.round((totalWorkouts / scheduledWorkouts) * 100) 
    : 0;

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="bg-gradient-to-br from-blue-50 to-white rounded-lg shadow-sm p-4">
        <div className="flex items-center gap-3">
          <Activity className="h-5 w-5 text-blue-600" />
          <div>
            <div className="text-sm font-medium text-gray-500">אימונים שהושלמו</div>
            <div className="text-2xl font-bold text-gray-900">{totalWorkouts}</div>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-green-50 to-white rounded-lg shadow-sm p-4">
        <div className="flex items-center gap-3">
          <Weight className="h-5 w-5 text-green-600" />
          <div>
            <div className="text-sm font-medium text-gray-500">נפח אימון ממוצע</div>
            <div className="text-2xl font-bold text-gray-900">{averageVolume} ק״ג</div>
          </div>
        </div>
      </div>
      
      {favoriteExercise && (
        <div className="bg-gradient-to-br from-yellow-50 to-white rounded-lg shadow-sm p-4">
          <div className="flex items-center gap-3">
            <Trophy className="h-5 w-5 text-yellow-600" />
            <div>
              <div className="text-sm font-medium text-gray-500">תרגיל מועדף</div>
              <div className="text-2xl font-bold text-gray-900">{favoriteExercise}</div>
            </div>
          </div>
        </div>
      )}
      
      {completionRate > 0 && (
        <div className="bg-gradient-to-br from-purple-50 to-white rounded-lg shadow-sm p-4">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-5 w-5 text-purple-600" />
            <div>
              <div className="text-sm font-medium text-gray-500">אחוז השלמה</div>
              <div className="text-2xl font-bold text-gray-900">{completionRate}%</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};