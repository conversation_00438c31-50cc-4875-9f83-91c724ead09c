import React, { useEffect, useState, useRef } from 'react';
import { Timer, FastForward } from 'lucide-react';
import Draggable from 'react-draggable';

interface RestTimerProps {
  time: number;
  onSkip: () => void;
  onComplete: () => void;
  isPaused: boolean;
}

export const RestTimer: React.FC<RestTimerProps> = ({ time, onSkip, onComplete, isPaused }) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const nodeRef = useRef(null);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    if (time === 0) {
      onComplete();
    }
  }, [time, onComplete]);

  return (
    <Draggable
      nodeRef={nodeRef}
      position={position}
      onStop={(e, data) => setPosition({ x: data.x, y: data.y })}
      bounds="parent"
    >
      <div ref={nodeRef} className="fixed top-4 right-4 z-50 cursor-move">
        <div className="flex items-center gap-4 bg-white rounded-lg shadow-lg p-4 border-2 border-yellow-200">
          <button
            onClick={onSkip}
            className="flex items-center gap-2 bg-yellow-100 px-4 py-2 rounded-lg hover:bg-yellow-200 transition-colors duration-200"
          >
            <FastForward className="h-5 w-5 text-yellow-600" />
            <span className="font-medium text-yellow-800">דלג על מנוחה</span>
          </button>
          <div className="flex items-center gap-2 bg-yellow-100 px-4 py-2 rounded-lg">
            <Timer className="h-5 w-5 text-yellow-600" />
            <span className="font-medium text-yellow-800">
              מנוחה: {formatTime(time)}
            </span>
          </div>
        </div>
      </div>
    </Draggable>
  );
};