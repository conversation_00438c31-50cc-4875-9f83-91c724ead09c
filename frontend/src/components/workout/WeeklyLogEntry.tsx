import React, { useState } from 'react';
import { Calendar, Clock, Dumbbell, ChevronDown, ChevronUp } from 'lucide-react';

// Update LogEntryProps to make log optional
interface LogEntryProps {
  userId?: string;
  logs?: any[];
}

export const WeeklyLogEntry: React.FC<LogEntryProps> = ({ logs }) => {
  const [expandedExercises, setExpandedExercises] = useState<Record<string, boolean>>({});  

  const toggleExerciseExpand = (date: string | null, exerciseName: string) => {
    const key = `${date ?? 'no-log-id'}-${exerciseName}`;
    setExpandedExercises(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const isExerciseExpanded = (date: string | null, exerciseName: string) => {
    const key = `${date ?? 'no-log-id'}-${exerciseName}`;
    return expandedExercises[key] || false;
  };

  if (!logs?.length) {
    return <div className="text-center p-4 text-gray-500">אין היסטוריית אימונים.</div>;
  }

  return (
    <div className="space-y-6">
      {logs.map((log) => (
        <div key={`${log.trainingPlan.id}-${log.trainingPlan.date}`} className="bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
          {/* Log header */}
          <div className="bg-blue-50 p-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg text-gray-900">{log.trainingPlan.type || 'אימון'}</h3>
              <span className="text-sm text-blue-600 font-medium">
                {log.trainingPlanId ? log.trainingPlanId :  ''}
              </span>
            </div>
            
            <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
            <span>{log.trainingPlan.day}</span>
            <span>{log.trainingPlan.focus}</span>
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{new Date(log.trainingPlan.date).toLocaleDateString('he-IL')}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{typeof log.trainingPlan.duration === 'string' ? log.trainingPlan.duration : '---'}</span>
              </div>
            </div>
          </div>

          {/* Exercises list */}
          <div className="divide-y divide-gray-100">
            {log.trainingPlan.exercises?.map((exercise: any) => (
              <div key={`${log.trainingPlan.id}-${log.trainingPlan.date}-${exercise.id}`} className="p-4">
                <div 
                  className="flex items-center justify-between cursor-pointer"
                  onClick={() => toggleExerciseExpand(log.trainingPlan.date, exercise.name)}
                >
                  <div className="flex items-center gap-2">
                     <Dumbbell className="h-5 w-5 text-gray-400" />
                    <span className="font-medium">{exercise.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">{exercise.sets.length} סטים</span>
                    {isExerciseExpanded(log.trainingPlan.date, exercise.name) ? 
                      <ChevronUp className="h-4 w-4 text-gray-400" /> : 
                      <ChevronDown className="h-4 w-4 text-gray-400" />
                    }
                  </div>
                </div>

                {isExerciseExpanded(log.trainingPlan.date, exercise.name) && (
                  <div className="mt-3 pt-3 border-t border-gray-100">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="text-gray-500 text-center">
                          <th className="py-2 font-medium">סט</th>
                          <th className="py-2 font-medium">משקל (ק"ג)</th>
                          <th className="py-2 font-medium">חזרות</th>
                        </tr>
                      </thead>
                      <tbody>
                        {exercise.sets.length > 0 ? (
                          exercise.sets.map((set: any, index: any) => (
                            <tr key={`set-${index}`} className="text-center border-t border-gray-50">
                              <td className="py-2">{index + 1}</td>
                              <td className="py-2">{set.weight}</td>
                              <td className="py-2">{set.reps}</td>
                            </tr>
                          ))
                        ) : (
                          <tr className="text-center">
                            <td colSpan={3} className="py-3 text-gray-400">לא הושלמו סטים</td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
