import React, { useEffect, useState } from 'react';
import { WeeklyLogStats } from '../workout/WeeklyLogStats';
import { WeeklyLogEntry } from '../workout/WeeklyLogEntry';
import { workoutLogsApi } from '../../service/workout-logs.api';

interface FirstWeekWorkoutsProps {
  userId: string;
}

export const FirstWeekWorkouts: React.FC<FirstWeekWorkoutsProps> = ({ userId }) => {
  const [allLogs, setAllLogs] = useState<any[]>([]);

  async function getWorkoutLogsWeekly() {

    try {
      const res = await workoutLogsApi.getLastWeekByUser(userId);
      setAllLogs(res || []);
    } catch (error) {
      console.error("Error fetching workout logs:", error);
    } finally {
    }
  }

  useEffect(() => {
    getWorkoutLogsWeekly();
  }, [userId]);

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-bold text-gray-800 mb-4">שבוע ראשון</h2>
        <WeeklyLogStats logs={allLogs} />
        <div className="space-y-6">
          <WeeklyLogEntry logs={allLogs} />
        </div>
      </div>
    </div>
  );
};
