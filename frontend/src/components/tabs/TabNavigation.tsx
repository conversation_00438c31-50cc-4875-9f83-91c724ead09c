import React from "react";
import { Layout, Calendar, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock } from "lucide-react";

interface Tab {
  id: string;
  label: string;
  icon: React.ElementType;
}

interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const tabs: Tab[] = [
  { id: "overview", label: "Overview", icon: Layout },
  { id: "mealplan", label: "Meal Plan", icon: Calendar },
  { id: "training", label: "Training Plan", icon: Dumbbell },
  { id: "workoutlogs", label: "Workout Logs", icon: ClipboardList },
  { id: "firstweek", label: "First Week", icon: Clock },
];

export const TabNavigation: React.FC<TabNavigationProps> = ({ activeTab, onTabChange }) => {
  return (
    <div className="p-4 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200 flex gap-1" dir="rtl">
      {tabs.map(({ id, label, icon: Icon }) => {
        const isActive = activeTab === id;
        return (
          <button
            key={id}
            onClick={() => onTabChange(id)}
            className={`
              p-3 text-sm font-semibold uppercase transition-all duration-200 rounded-lg
              flex items-center gap-2 group
              ${isActive ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-md" : "text-gray-500 hover:bg-gray-100"}
            `}
          >
            <Icon
              className={`w-4 h-4 transition-transform duration-300 ${
                isActive
                  ? "text-white transform group-hover:rotate-12"
                  : "text-gray-400 group-hover:text-blue-500 transform group-hover:rotate-12"
              }`}
            />
            {label}
          </button>
        );
      })}
    </div>
  );
};
