import React, { useState, useEffect } from "react";
import { UserTable } from "../users/UserTable";
import { AlertSection } from "../users/AlertSection";
import { FilterBar } from "../users/FilterBar";
import { StatisticCards } from "../users/StatisticCards";
import { AdminPanel } from "../training/AddAMember";
import { userApi } from "../user/service/user.api";
import { User } from "../../types/user";
import { trainerAssignmentApi } from "../../service/trainer-assignment-service.api";
import defaultProfile from "../../default-profile-image.png";

// Define the expected props for the UsersOverview component
interface UsersOverviewProps {
  onUserSelect: (userId: string) => void;
}

export const UsersOverview: React.FC<UsersOverviewProps> = ({
  onUserSelect,
}) => {
  // State for users, loading status, error, and filters
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    status: "all",
    performance: "all",
    team: "all",
    dateRange: "all",
  });

  // Fetch users when component mounts
  useEffect(() => {
    fetchUsers();
  }, []);

  // Function to fetch users from the API
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const storedUser = JSON.parse(localStorage.getItem("user") || "{}");

      // Fetch user data from the API
      const apiData = await userApi.getAll(storedUser.id);
      const traineeData = await trainerAssignmentApi.getAssignmentsByTrainerId(
        storedUser.id
      );

      // Map API data to the User type expected by UserTable
      const mappedUsers = traineeData.map((apiUser: any) => ({
        id: apiUser.trainee.id,
        assignmentId: apiUser.id,
        name: apiUser.trainee.name,
        email: apiUser.trainee.email,
        avatar: apiUser.avatar || defaultProfile,
        status: apiUser.trainee.isActive
          ? ("active" as const)
          : ("inactive" as const),
        team:
          apiUser.team ||
          ("beginner" as "beginner" | "intermediate" | "advanced"),
        performance: apiUser.performance || 75,
        lastActive:
          apiUser.lastActive || new Date().toISOString().split("T")[0],
        overdueTasks: apiUser.overdueTasks || 0,
        attentionNote: apiUser.attentionNote || null,
      }));

      setUsers(mappedUsers);
      setError(null);
    } catch (err) {
      console.error("Error fetching users:", err);
      setError("Failed to load users. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handler for adding a new user from the AdminPanel
  const handleAddUser = async (userData: any) => {
    try {
      await fetchUsers();
    } catch (error) {
      console.error("Error adding user:", error);
      setError("Failed to add user. Please try again.");

      // Refresh all users in case of error
      await fetchUsers();
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading users...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
        <p className="text-red-700">{error}</p>
        <button
          className="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
          onClick={() => fetchUsers()}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Add Member Component */}
      <AdminPanel onAddUser={handleAddUser} />

      {/* Statistics Cards */}
      <StatisticCards users={users} />

      {/* Alert Section */}
      <AlertSection users={users} onUserSelect={onUserSelect} />

      {/* Filter Bar */}
      <FilterBar filters={filters} onFilterChange={setFilters} />

      {/* User Table */}
      <UserTable
        filters={filters}
        onUserSelect={onUserSelect}
        users={users}
        loading={loading}
        error={error}
      />
    </div>
  );
};
