import React, { useState, useEffect } from "react";
import MealEditor from "../meal/MealEditor";
import { MacroDisplay } from "../meal/MacroDisplay";
import { MacroRange, MealCategory, FoodItem } from "../../types/food";
import { Utensils, Plus, Trash2 } from "lucide-react";
import { mealsApi } from "../meal/services/meals.api";
import { useMealPlan } from "../../contexts/MealPlanContext";

interface MealPlanProps {
  selectedUserId: string;
}

interface MealWithFood {
  id: string;
  name: string;
  categories: MealCategory[];
  macroRange: MacroRange;
  foodItems?: FoodItem[];
  isNewMeal?: boolean; // Add this to track newly added meals
  defaultServing: string;
  maxServing: string;
  minServing: string;
}

export const MealPlan: React.FC<MealPlanProps> = ({ selectedUserId }) => {
  const { updateMealPlan } = useMealPlan();

  const [meals, setMeals] = useState<MealWithFood[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showOptions, setShowOptions] = useState<boolean>(false);

  const [totalMacroRange, setTotalMacroRange] = useState<MacroRange>({
    min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
    max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
  });

  const transformFetchedMeals = (fetchedMeals: any) => {
    return fetchedMeals.map((meal: any) => {
      const transformedCategories = [];

      // Check if meal has categories
      if (meal.categories && meal.categories.length > 0) {
        // Group food items by category
        const proteinItems = meal.categories.filter(
          (item: any) => item.name === "Protein"
        );
        const carbItems = meal.categories.filter(
          (item: any) => item.name === "Carb"
        );

        // Create Protein category if it has items
        if (proteinItems.length > 0) {
          transformedCategories.push({
            name: "Protein",
            options: proteinItems[0].options.map((item: any) => ({
              foodId: item.foodId,
              amount: item.amount || 0,
            })),
          });
        }

        // Create Carb category if it has items
        if (carbItems.length > 0) {
          transformedCategories.push({
            name: "Carb",
            options: carbItems[0].options.map((item: any) => ({
              foodId: item.foodId,
              amount: item.amount || 0,
            })),
          });
        }
      }

      return {
        ...meal,
        id: meal.id,
        name: meal.name,
        categories: transformedCategories,
        macroRange: meal.macroRange,
        foodItems: meal.foodItems || [],
        isNewMeal: false,
      };
    });
  };
  mealsApi.findByTrainee(selectedUserId);

  useEffect(() => {
    setLoading(true);
    mealsApi
      .findByTrainee(selectedUserId)
      .then((response) => {
        const transformedMeals = transformFetchedMeals(response);
        setMeals(transformedMeals);
        updateTotalMacros(transformedMeals);

        transformedMeals.forEach((meal: any) => {
          updateMealPlan(
            selectedUserId,
            meal.id,
            meal.categories,
            meal.macroRange
          );
        });

        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching meals:", error);
        setError("Failed to load meal plan");
        setLoading(false);
      });
  }, [selectedUserId]);

  const handleMealAdd = () => {
    setShowOptions(true);
    const newMeal = {
      id: `meal-${Date.now()}`,
      name: `Meal ${meals.length + 1}`,
      defaultServing: "",
      maxServing: "",
      minServing: "",
      categories: [],
      macroRange: {
        min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
        max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
      },
      foodItems: [],
      isNewMeal: true, // Mark this as a new meal
    };

    setMeals([...meals, newMeal]);
  };

  const handleMealUpdate = (
    id: string,
    categories: MealCategory[],
    macroRange: MacroRange,
    foodItems?: FoodItem[]
  ) => {
    const newMeals = meals.map((meal) =>
      meal.id === id
        ? {
            ...meal,
            categories,
            macroRange,
            foodItems: foodItems || meal.foodItems,
          }
        : meal
    );
    setMeals(newMeals);
    updateTotalMacros(newMeals);
  };

  const handleMealDelete = async (id: string) => {
    const newMeals = meals.filter((meal) => meal.id !== id);
    updateTotalMacros(newMeals);
    setMeals(newMeals);
    await mealsApi.remove(id);
  };

  const updateTotalMacros = (updatedMeals: MealWithFood[]) => {
    const newTotalMacroRange: MacroRange = {
      min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
      max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
    };

    updatedMeals.forEach((meal) => {
      if (meal.macroRange) {
        newTotalMacroRange.min.protein += meal.macroRange.min.protein;
        newTotalMacroRange.min.carbs += meal.macroRange.min.carbs;
        newTotalMacroRange.min.fats += meal.macroRange.min.fats;
        newTotalMacroRange.min.calories += meal.macroRange.min.calories;

        newTotalMacroRange.max.protein += meal.macroRange.max.protein;
        newTotalMacroRange.max.carbs += meal.macroRange.max.carbs;
        newTotalMacroRange.max.fats += meal.macroRange.max.fats;
        newTotalMacroRange.max.calories += meal.macroRange.max.calories;
      }
    });

    setTotalMacroRange(newTotalMacroRange);
  };

  if (loading) {
    return <div className="p-6 text-center">Loading meal plan...</div>;
  }

  if (error) {
    return <div className="p-6 text-center text-red-500">{error}</div>;
  }

  // Check if dark mode is preferred
  const [prefersDarkMode, setPrefersDarkMode] = useState(
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  // Listen for changes in color scheme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersDarkMode(e.matches);
    };

    // Add event listener
    mediaQuery.addEventListener('change', handleChange);

    // Clean up
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <div className={`p-6 ${prefersDarkMode ? 'bg-gray-900 border-purple-800' : 'bg-white border-blue-200'} border-2 shadow-lg rounded-xl`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className={`p-3 transition-transform duration-300 transform ${prefersDarkMode ? 'bg-purple-900' : 'bg-blue-100'} rounded-xl hover:rotate-12`}>
              <Utensils className={`w-6 h-6 ${prefersDarkMode ? 'text-purple-300' : 'text-blue-600'}`} />
            </div>
            <h2 className={`text-xl font-semibold ${prefersDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Daily Meal Plan
            </h2>
          </div>
          <button
            onClick={handleMealAdd}
            className={`flex items-center gap-2 px-4 py-2 text-white transition-colors duration-200 transform ${prefersDarkMode ? 'bg-purple-700 hover:bg-purple-600' : 'bg-blue-600 hover:bg-blue-700'} rounded-lg hover:scale-105`}
          >
            <Plus className="w-5 h-5" />
            <span>Add Meal</span>
          </button>
        </div>

        <div className="mb-6">
          <MacroDisplay
            macroRange={totalMacroRange}
            title="Total Daily Macros"
            isDarkMode={prefersDarkMode}
          />
        </div>

        <div className="space-y-6">
          {meals.map((meal) => (
            <div key={meal.id} className="relative group">
              <button
                onClick={() => handleMealDelete(meal.id)}
                className={`absolute z-10 p-2 transition-all duration-200 ${prefersDarkMode ? 'bg-red-700' : 'bg-red-100'} rounded-full opacity-0 -left-3 -top-3 group-hover:opacity-100 ${prefersDarkMode ? 'hover:bg-red-600' : 'hover:bg-red-200'}`}
                title="Delete Meal"
              >
                <Trash2 className={`w-4 h-4 ${prefersDarkMode ? 'text-white' : 'text-red-600'}`} />
              </button>

              <MealEditor
                traineeId={selectedUserId}
                key={meal.id}
                mealId={meal.id}
                defaultServing={meal.defaultServing}
                maxServing={meal.maxServing}
                minServing={meal.minServing}
                userId="1"
                mealName={meal.name}
                initialCategories={meal.categories}
                initialFoodItems={meal.foodItems}
                meals={meals}
                onUpdate={(categories, macroRange, foodItems) =>
                  handleMealUpdate(meal.id, categories, macroRange, foodItems)
                }
                onDelete={() => handleMealDelete(meal.id)}
                isNewMeal={meal.isNewMeal} // Pass the isNewMeal prop
                setShowOptions={setShowOptions}
                showOptions={showOptions}
                isDarkMode={prefersDarkMode}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
