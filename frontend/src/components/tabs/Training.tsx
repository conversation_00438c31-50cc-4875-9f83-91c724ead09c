import React, { useEffect, useState } from "react";
import { TrainingDay } from "../../types/training";
import { TrainingDayEditor } from "../training/TrainingDayEditor";
import { Dumbbell, Plus } from "lucide-react";
import { trainingPlansApi } from "../training/service/training-plans.api";
import { trainingExerciseApi } from "../training/service/training-exercise.api";

export const Training: React.FC = () => {
  const [trainingPlan, setTrainingPlan] = useState<TrainingDay[]>([]);

  // Check if dark mode is preferred
  const [prefersDarkMode, setPrefersDarkMode] = useState(
    window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  );

  // Listen for changes in color scheme preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersDarkMode(e.matches);
    };

    // Add event listener
    mediaQuery.addEventListener('change', handleChange);

    // Clean up
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  async function getTrainingPlansBy() {
    const trainee_id: string | null = localStorage.getItem("trainee_id");
    if (trainee_id) {
      const res = await trainingPlansApi.getById(trainee_id);
      setTrainingPlan(res);
    }
  }

  useEffect(() => {
    getTrainingPlansBy();
  }, []);

  const handleDayAdd = () => {
    const newDay: TrainingDay = {
      day: "New Day",
      focus: "New Focus",
      exercises: [],
    };
    setTrainingPlan([newDay, ...trainingPlan]);
  };

  const handleDayUpdate = async (index: number, updatedDay: TrainingDay) => {
    const newPlan = [...trainingPlan];
    const originalDay = trainingPlan[index];

    if (updatedDay.id) {
      if (
        updatedDay.day !== originalDay.day ||
        updatedDay.focus !== originalDay.focus
      ) {
        try {
          const dayChanges: any = {};
          if (updatedDay.day !== originalDay.day)
            dayChanges.day = updatedDay.day;
          if (updatedDay.focus !== originalDay.focus)
            dayChanges.focus = updatedDay.focus;

          if (Object.keys(dayChanges).length > 0) {
            await trainingExerciseApi.update(updatedDay.id, dayChanges);
          }
        } catch (error) {
          console.error(`Error updating training day ${updatedDay.id}:`, error);
        }
      }

      for (const updatedExercise of updatedDay.exercises) {
        if (!updatedExercise.id) continue;

        const originalExercise = originalDay.exercises.find(
          (e) => e.id === updatedExercise.id
        );

        if (originalExercise) {
          const changedFields: any = {};

          if (updatedExercise.name !== originalExercise.name)
            changedFields.name = updatedExercise.name;
          if (updatedExercise.sets !== originalExercise.sets)
            changedFields.sets = updatedExercise.sets;
          if (updatedExercise.reps !== originalExercise.reps)
            changedFields.reps = updatedExercise.reps;
          if (updatedExercise.rest !== originalExercise.rest)
            changedFields.rest = updatedExercise.rest;
          if (updatedExercise.instructions !== originalExercise.instructions)
            changedFields.instructions = updatedExercise.instructions;

          if (Object.keys(changedFields).length > 0) {
            try {
              await trainingExerciseApi.update(
                updatedExercise.id,
                changedFields
              );
            } catch (error) {
              console.error(
                `Error updating exercise ${updatedExercise.id}:`,
                error
              );
            }
          }
        }
      }
    }

    newPlan[index] = updatedDay;
    setTrainingPlan(newPlan);
  };

  const handleDayDelete = async (index: number) => {
    const dayToDelete = trainingPlan[index];

    if (dayToDelete.id) {
      try {
        await trainingPlansApi.delete(dayToDelete.id);
      } catch (error) {
        console.error("Error deleting training plan:", error);
      }
    }

    // Remove from state regardless of API success
    setTrainingPlan(trainingPlan.filter((_, i) => i !== index));
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6 p-6" dir="rtl">
      <div className={`p-6 ${prefersDarkMode ? 'bg-gray-900 border-purple-800' : 'bg-white border-blue-200'} border-2 shadow-lg rounded-xl`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className={`p-3 transition-transform duration-300 transform ${prefersDarkMode ? 'bg-purple-900' : 'bg-blue-100'} rounded-xl hover:rotate-12`}>
              <Dumbbell className={`w-6 h-6 ${prefersDarkMode ? 'text-purple-300' : 'text-blue-600'}`} />
            </div>
            <h2 className={`text-xl font-semibold ${prefersDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Weekly Training Plan
            </h2>
          </div>
          <button
            onClick={handleDayAdd}
            className={`flex items-center gap-2 px-4 py-2 ${prefersDarkMode ? 'bg-purple-800 hover:bg-purple-700' : 'bg-blue-600 hover:bg-blue-700'} text-white rounded-lg transition-colors duration-200 transform hover:scale-105`}
          >
            <Plus className="h-5 w-5" />
            <span>Add Training Day</span>
          </button>
        </div>

        <div className="space-y-6">
          {trainingPlan.map((day, index) => (
            <TrainingDayEditor
              key={index}
              day={day}
              onUpdate={(updatedDay) => handleDayUpdate(index, updatedDay)}
              onDelete={() => handleDayDelete(index)}
              prefersDarkMode={prefersDarkMode}
            />
          ))}
        </div>
      </div>
    </div>
  );
};
