import React, { useEffect, useState } from 'react';
import { events } from '../../data/events';
import { calculateTimeLeft } from '../../utils/dateUtils';
import { calculateWeeklyIntensity } from '../../utils/workoutUtils';
import { workoutLogs } from '../../data/workoutLogs';
import { Trophy, Star, Target, Dumbbell, Calendar, TrendingUp, Award, Clock, AlertCircle, Crown } from 'lucide-react';
import { users } from '../../data/users';
import { calculateUserPerformance } from '../../utils/userPerformance';
import { getOverviewById } from '../../data/overview';

// Define types for user data
interface User {
  id: string;
  name: string;
  email: string;
  isActive: boolean;
  role?: {
    id: number;
    name: string;
  };
  passwordHash?: string;
  roleId?: number;
  isVerified?: boolean;
  firstWorkoutDate?: string | null;
  avatarUrl?: string;
}

// Define types for assignment data
interface AssignmentData {
  id: string;
  isActive: boolean;
  startDate: string;
  endDate: string;
  createdAt: string;
  updatedAt: string;
  approved: boolean;
  trainee: User;
  trainer: User;
}

// Define types for user overview data
interface UserOverview {
  userId: string;
  userName: string;
  email?: string;
  avatarUrl?: string | null;
  startingWeight: number;
  currentWeight: number;
  startingFatPercentage: number;
  currentFatPercentage: number;
  engagementLevel: number;
  progressLevel: number;
  weeklyIntensity?: number;
  progressOverview: string;
  latestChanges: string;
  latestEvents: string;
  membershipEndDate: string;
}

// Define props interface
interface OverviewProps {
  userOverview?: Partial<UserOverview>;
}

export const TraineeOverview: React.FC<OverviewProps> = ({ userOverview: initialUserOverview = {} }) => {
  const [traineeData, setTraineeData] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch user data from localStorage
  const fetchUserData = (): void => {
    setIsLoading(true);
    
    try {
      // Attempt to get user data from localStorage
      const userDataString = localStorage.getItem('user');
      
      if (!userDataString) {
        setError('No user data found in local storage');
        setIsLoading(false);
        return;
      }
      
      const userData = JSON.parse(userDataString);
      setTraineeData(userData);
    } catch (error) {
      console.error("Error parsing user data:", error);
      setError('Failed to parse user data');
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchUserData();
  }, []);
  
  // Merge trainee data with userOverview
  const enhancedUserOverview = React.useMemo<UserOverview>(() => {
    // Default values for the user overview
    const defaultOverview: UserOverview = {
      userId: initialUserOverview.userId || (traineeData?.id || 'default-user-id'),
      userName: initialUserOverview.userName || (traineeData?.name || 'User'),
      email: initialUserOverview.email || (traineeData?.email || 'No email provided'),
      avatarUrl: initialUserOverview.avatarUrl || traineeData?.avatarUrl || null,
      startingWeight: initialUserOverview.startingWeight || 80,
      currentWeight: initialUserOverview.currentWeight || 78,
      startingFatPercentage: initialUserOverview.startingFatPercentage || 25,
      currentFatPercentage: initialUserOverview.currentFatPercentage || 22,
      engagementLevel: initialUserOverview.engagementLevel || 75,
      progressLevel: initialUserOverview.progressLevel || 60,
      weeklyIntensity: initialUserOverview.weeklyIntensity || 0,
      progressOverview: initialUserOverview.progressOverview || "Making steady progress towards fitness goals. Keep up the good work!",
      latestChanges: initialUserOverview.latestChanges || "Completed 4 consecutive workout sessions this week.",
      latestEvents: initialUserOverview.latestEvents || "Joined the evening yoga class on Thursday.",
      membershipEndDate: initialUserOverview.membershipEndDate || new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString()
    };
    
    return defaultOverview;
  }, [traineeData, initialUserOverview]);

  const weeklyIntensity = calculateWeeklyIntensity(workoutLogs);
  const timeLeft = calculateTimeLeft(enhancedUserOverview.membershipEndDate);

  // Calculate user's rank in leaderboard
  const sortedUsers = [...users].sort((a, b) => {
    const aOverview = getOverviewById(a.id);
    const bOverview = getOverviewById(b.id);
    const aPerformance = calculateUserPerformance(
      aOverview.engagementLevel,
      aOverview.progressLevel,
      0
    );
    const bPerformance = calculateUserPerformance(
      bOverview.engagementLevel,
      bOverview.progressLevel,
      0
    );
    return bPerformance - aPerformance;
  });

  const userRank = sortedUsers.findIndex(u => u.id === enhancedUserOverview.userId) + 1;
  const userPerformance = calculateUserPerformance(
    enhancedUserOverview.engagementLevel,
    enhancedUserOverview.progressLevel,
    enhancedUserOverview.weeklyIntensity || 0
  );

  // Get first letter for avatar if no avatar URL
  const userInitial = enhancedUserOverview.userName?.charAt(0).toUpperCase();

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>;
  }

  // Calculate membership dates based on current date
  const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toLocaleDateString(); // 30 days ago
  const endDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toLocaleDateString(); // 90 days from now

  return (
    <div className="max-w-5xl mx-auto space-y-6" dir="rtl">
      {/* Profile Card */}
      <div className="bg-white rounded-2xl shadow-lg p-8 border-2 border-blue-200">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            {enhancedUserOverview.avatarUrl ? (
              <img 
                src={enhancedUserOverview.avatarUrl} 
                alt={enhancedUserOverview.userName}
                className="h-20 w-20 rounded-full border-4 border-blue-200 object-cover transform hover:rotate-12 transition-all duration-300"
              />
            ) : (
              <div className="h-20 w-20 rounded-full bg-blue-500 flex items-center justify-center transform hover:rotate-12 transition-all duration-300 border-4 border-blue-200">
                <span className="text-3xl font-bold text-white">
                  {userInitial}
                </span>
              </div>
            )}
            <div>
              <h2 className="text-2xl font-bold text-gray-800">{enhancedUserOverview.userName}</h2>
              <div className="flex items-center gap-2 mt-1">
                <Trophy className="w-4 h-4 text-yellow-500" />
                <span className="text-sm text-gray-600">Advanced Member</span>
                <span className="text-xs text-gray-500 ml-2">
                  Membership: {startDate} - {endDate}
                </span>
              </div>
              {traineeData?.role?.id === 2 && (
                <div className="flex items-center gap-2 mt-1">
                  <Star className="w-4 h-4 text-blue-500" />
                  <span className="text-sm text-gray-600">Role: Trainee</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-6">
            {/* Points and Rank */}
            <div className="text-right">
              <div className="flex items-center gap-3 mb-2">
                <div className="bg-gradient-to-r from-yellow-100 to-amber-100 px-3 py-1.5 rounded-full border border-yellow-300">
                  <div className="flex items-center gap-2">
                    <Crown className="h-5 w-5 text-yellow-600" />
                    <span className="font-medium text-yellow-700">Rank {userRank}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2 bg-blue-50 px-3 py-1.5 rounded-full">
                <Star className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-700">{userPerformance} Points</span>
              </div>
            </div>
            {/* Membership Status */}
            <div className="text-left">
              <div className="text-sm text-gray-500 mb-1">Membership Status</div>
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-700 font-medium border-2 border-green-200">
                <Star className="w-4 h-4 ml-1" />
                {timeLeft}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border-2 border-blue-100">
          <p className="text-gray-700 text-lg">{enhancedUserOverview.progressOverview}</p>
        </div>
      </div>

      {/* Progress Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Weight Progress */}
        <div className="bg-emerald-100 rounded-xl shadow-lg p-6 transform hover:-translate-y-1 transition-all duration-300 border-2 border-emerald-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-emerald-200">
              <Target className="h-6 w-6 text-emerald-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">Weight Tracking</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4 text-center border-2 border-emerald-100">
              <div className="text-sm text-gray-500 mb-1">Starting</div>
              <div className="text-2xl font-bold text-emerald-600">
                {enhancedUserOverview.startingWeight}
                <span className="text-base font-medium text-gray-500 mr-1">kg</span>
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center border-2 border-emerald-100">
              <div className="text-sm text-gray-500 mb-1">Current</div>
              <div className="text-2xl font-bold text-emerald-600">
                {enhancedUserOverview.currentWeight}
                <span className="text-base font-medium text-gray-500 mr-1">kg</span>
              </div>
            </div>
          </div>
        </div>

        {/* Body Fat Progress */}
        <div className="bg-blue-100 rounded-xl shadow-lg p-6 transform hover:-translate-y-1 transition-all duration-300 border-2 border-blue-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-blue-200">
              <Dumbbell className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">Body Composition</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4 text-center border-2 border-blue-100">
              <div className="text-sm text-gray-500 mb-1">Starting Body Fat</div>
              <div className="text-2xl font-bold text-blue-600">
                {enhancedUserOverview.startingFatPercentage}%
              </div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center border-2 border-blue-100">
              <div className="text-sm text-gray-500 mb-1">Current Body Fat</div>
              <div className="text-2xl font-bold text-blue-600">
                {enhancedUserOverview.currentFatPercentage}%
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Achievement Meters */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-yellow-100 rounded-xl shadow-lg p-6 border-2 border-yellow-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              <span className="font-medium text-gray-700">Engagement Level</span>
            </div>
            <span className="text-lg font-bold text-yellow-500">{enhancedUserOverview.engagementLevel}%</span>
          </div>
          <div className="h-3 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full bg-yellow-500 rounded-full transition-all duration-500"
              style={{ width: `${enhancedUserOverview.engagementLevel}%` }}
            />
          </div>
        </div>

        <div className="bg-green-100 rounded-xl shadow-lg p-6 border-2 border-green-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <span className="font-medium text-gray-700">Progress Level</span>
            </div>
            <span className="text-lg font-bold text-green-500">{enhancedUserOverview.progressLevel}%</span>
          </div>
          <div className="h-3 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full bg-green-500 rounded-full transition-all duration-500"
              style={{ width: `${enhancedUserOverview.progressLevel}%` }}
            />
          </div>
        </div>

        <div className="bg-purple-100 rounded-xl shadow-lg p-6 border-2 border-purple-200">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-purple-500" />
              <span className="font-medium text-gray-700">Weekly Intensity</span>
            </div>
            <span className="text-lg font-bold text-purple-500">{Math.round(weeklyIntensity)}%</span>
          </div>
          <div className="h-3 bg-gray-100 rounded-full overflow-hidden">
            <div
              className="h-full bg-purple-500 rounded-full transition-all duration-500"
              style={{ width: `${weeklyIntensity}%` }}
            />
          </div>
        </div>
      </div>

      {/* Latest Updates and Events Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Latest Updates */}
        <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-blue-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Latest Updates</h3>

          <div className="bg-white rounded-lg p-4 backdrop-blur-sm mb-4 group hover:shadow-md transition-all duration-300 border-2 border-blue-100">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 rounded-lg bg-blue-200 transform group-hover:rotate-12 transition-transform duration-300">
                <Trophy className="h-5 w-5 text-blue-600" />
              </div>
              <h4 className="font-medium text-gray-800">Latest Achievement</h4>
            </div>
            <p className="text-gray-700">{enhancedUserOverview.latestChanges}</p>
          </div>

          <div className="bg-white rounded-lg p-4 backdrop-blur-sm group hover:shadow-md transition-all duration-300 border-2 border-blue-100">
            <div className="flex items-center gap-2 mb-2">
              <div className="p-2 rounded-lg bg-purple-200 transform group-hover:rotate-12 transition-transform duration-300">
                <Clock className="h-5 w-5 text-purple-600" />
              </div>
              <h4 className="font-medium text-gray-800">Recent Activity</h4>
            </div>
            <p className="text-gray-700">{enhancedUserOverview.latestEvents}</p>
          </div>
        </div>

        {/* Upcoming Events */}
        <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-indigo-200">
          <div className="flex items-center gap-2 mb-4">
            <div className="p-2 rounded-lg bg-indigo-200 transform group-hover:rotate-12 transition-transform duration-300">
              <Calendar className="h-5 w-5 text-indigo-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800">Upcoming Events</h3>
          </div>

          <div className="space-y-3">
            {events.slice(0, 3).map((event, idx) => (
              <div
                key={idx}
                className="bg-white rounded-lg p-4 transform hover:scale-102 transition-all duration-300 hover:shadow-md group border-2 border-indigo-100"
              >
                <div className="flex items-center gap-2 mb-1">
                  {event.type === 'social_event' ? (
                    <div className="p-1.5 rounded-lg bg-green-200 transform group-hover:rotate-12 transition-transform duration-300">
                      <Calendar className="h-4 w-4 text-green-600" />
                    </div>
                  ) : (
                    <div className="p-1.5 rounded-lg bg-amber-200 transform group-hover:rotate-12 transition-transform duration-300">
                      <AlertCircle className="h-4 w-4 text-amber-600" />
                    </div>
                  )}
                  <span className="font-medium text-gray-900">{event.date}</span>
                  <span className="text-sm text-gray-500 px-2 py-0.5 rounded-full bg-gray-100 border border-gray-200">
                    {event.type === 'social_event' ? 'Social' : 'Health'}
                  </span>
                </div>
                <p className="text-gray-700 mt-2 group-hover:text-gray-900 transition-colors duration-200">
                  {event.details}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};