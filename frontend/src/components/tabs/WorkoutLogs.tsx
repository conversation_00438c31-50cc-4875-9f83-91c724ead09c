import React, { useEffect, useState, useRef } from 'react';
import { LogEntry } from '../workout/LogEntry';
import { LogStats } from '../workout/LogStats';
import {
  ClipboardList,
  Filter,
  Calendar,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { WorkoutLog } from '../../types/workout';
import { workoutLogsApi } from '../../service/workout-logs.api';
import { Categories as categories } from '../../types/categories';



interface WorkoutLogsProps {
  userId?: string;
}

export const WorkoutLogs: React.FC<WorkoutLogsProps> = ({ userId = '1' }) => {
  const [logs, setLogs] = useState<WorkoutLog[]>([]);
  const [filteredLogs, setFilteredLogs] = useState<WorkoutLog[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const categoryFilterRef = useRef<HTMLDivElement>(null);
  const dateFilterRef = useRef<HTMLDivElement>(null);

  // State for filters
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [dateFilter, setDateFilter] = useState<{
    startDate: string | null;
    endDate: string | null;
  }>({ startDate: null, endDate: null });

  // State for dropdown visibility
  const [isCategoryDropdownOpen, setIsCategoryDropdownOpen] = useState(false);
  const [isDateDropdownOpen, setIsDateDropdownOpen] = useState(false);

  const user_id = localStorage.getItem('trainee_id');

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Category dropdown
      if (categoryFilterRef.current &&
        !categoryFilterRef.current.contains(event.target as Node)) {
        setIsCategoryDropdownOpen(false);
      }

      // Date dropdown
      if (dateFilterRef.current &&
        !dateFilterRef.current.contains(event.target as Node)) {
        setIsDateDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (!userId || userId === '1') return;

    const fetchLogs = async () => {
      setLoading(true);
      try {
        const workoutLogs = await workoutLogsApi.getByUser(userId);
        const filteredLogs = filterDuplicateWorkouts(workoutLogs);
        setLogs(filteredLogs);
        setFilteredLogs(filteredLogs);
      } catch (error) {
        console.error('Failed to fetch logs:', error);
        setLogs([]);
        setFilteredLogs([]);
      }
      setLoading(false);
    };
    fetchLogs();
  }, [userId]);

  // Function to filter out duplicate workouts (keep existing implementation)
  const filterDuplicateWorkouts = (logs: WorkoutLog[]): WorkoutLog[] => {
    // ... (existing implementation)
    return logs;
  };

  // Handle category filter toggle
  const toggleCategoryFilter = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(cat => cat !== category)
        : [...prev, category]
    );
  };

  // Apply filters
  useEffect(() => {
    let result = logs;

    // Apply category filter
    if (selectedCategories.length > 0) {
      result = result.filter(log =>
        log.exercises.some(exercise =>
          selectedCategories.includes(exercise.category ?? '')
        )
      );
    }

    // Apply date filter
    if (dateFilter.startDate && dateFilter.endDate) {
      result = result.filter(log => {
        const logDate = new Date(log.date);
        const startDate = new Date(dateFilter.startDate!);
        const endDate = new Date(dateFilter.endDate!);
        return logDate >= startDate && logDate <= endDate;
      });
    }

    setFilteredLogs(result);
  }, [selectedCategories, dateFilter, logs]);

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedCategories([]);
    setDateFilter({ startDate: null, endDate: null });
  };

  if (loading) {
    return <div className="text-center p-6 text-gray-500">טוען...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      {/* <div className="bg-white rounded-xl shadow-sm overflow-hidden"> */}
      <div className="bg-white rounded-xl shadow-sm overflow-hidden min-h-[300px] flex flex-col">
        <div className="border-b border-gray-100 bg-gray-50 p-4">
          <div className="flex items-center justify-between relative">
            <div className="flex items-center gap-2">
              <ClipboardList className="h-5 w-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">היסטוריית אימונים</h2>
            </div>

            {/* Filters Container */}
            <div className="flex items-center gap-4 relative">
              {/* Category Filter */}
              <div
                ref={categoryFilterRef}
                className="relative"
              >
                <button
                  onClick={() => setIsCategoryDropdownOpen(!isCategoryDropdownOpen)}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-md transition-all ${selectedCategories.length > 0
                      ? 'bg-blue-50 text-blue-600'
                      : 'hover:bg-gray-100'
                    }`}
                >
                  <Filter className="h-4 w-4" />
                  <span className="text-sm">קטגוריות</span>
                  {selectedCategories.length > 0 && (
                    <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-0.5">
                      {selectedCategories.length}
                    </span>
                  )}
                  {isCategoryDropdownOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>

                {isCategoryDropdownOpen && (
                  <div className="absolute left-0 mt-2 w-64 bg-white border border-gray-100 rounded-lg shadow-lg z-20 p-3">
                    <div className="grid grid-cols-2 gap-2">
                      {categories.map((category) => (
                        <label
                          key={category.id}
                          className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 rounded px-2 py-1"
                        >
                          <input
                            type="checkbox"
                            className="form-checkbox h-4 w-4 text-blue-600 rounded"
                            checked={selectedCategories.includes(category.category)}
                            onChange={() => toggleCategoryFilter(category.category)}
                          />
                          <span className="text-sm text-gray-700">{category.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Date Filter */}
              <div
                ref={dateFilterRef}
                className="relative"
              >
                <button
                  onClick={() => setIsDateDropdownOpen(!isDateDropdownOpen)}
                  className={`flex items-center gap-2 px-3 py-1.5 rounded-md transition-all ${dateFilter.startDate && dateFilter.endDate
                      ? 'bg-green-50 text-green-600'
                      : 'hover:bg-gray-100'
                    }`}
                >
                  <Calendar className="h-4 w-4" />
                  <span className="text-sm">תאריכים</span>
                  {isDateDropdownOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </button>

                {isDateDropdownOpen && (
                  <div className="absolute left-0 mt-2 w-64 bg-white border border-gray-100 rounded-lg shadow-lg z-20 p-4">
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">תאריך התחלה</label>
                        <input
                          type="date"
                          className="w-full border rounded-md px-2 py-1.5 text-sm"
                          value={dateFilter.startDate || ''}
                          onChange={(e) => setDateFilter(prev => ({
                            ...prev,
                            startDate: e.target.value
                          }))}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">תאריך סיום</label>
                        <input
                          type="date"
                          className="w-full border rounded-md px-2 py-1.5 text-sm"
                          value={dateFilter.endDate || ''}
                          onChange={(e) => setDateFilter(prev => ({
                            ...prev,
                            endDate: e.target.value
                          }))}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Clear Filters Button */}
              {(selectedCategories.length > 0 || (dateFilter.startDate && dateFilter.endDate)) && (
                <button
                  onClick={clearAllFilters}
                  className="flex items-center gap-1 text-red-600 hover:bg-red-50 px-2 py-1 rounded-md transition-all"
                >
                  <X className="h-4 w-4" />
                  <span className="text-sm">נקה סינונים</span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Rest of the component remains the same */}
        {filteredLogs.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            {(selectedCategories.length > 0 || (dateFilter.startDate && dateFilter.endDate))
              ? "לא נמצאו אימונים תואמים לסינון."
              : "לא נמצאו רישומי אימונים."}
          </div>
        ) : (
          <div className="p-6">
            <LogStats logs={filteredLogs} />

            <div className="mt-8 space-y-6">
              <LogEntry userId={user_id ?? ''} logs={filteredLogs} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};