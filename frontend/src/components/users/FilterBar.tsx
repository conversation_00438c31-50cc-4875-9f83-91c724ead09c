import React from 'react';
import { Filter, Users, Award, Clock, ChevronDown } from 'lucide-react';

interface FilterBarProps {
  filters: {
    status: string;
    performance: string;
    team: string;
    dateRange: string;
  };
  onFilterChange: (filters: any) => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({ filters, onFilterChange }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 mb-8 transform hover:-translate-y-1 border-2 border-gray-200" dir="rtl">
      <div className="flex items-center gap-2 mb-6">
        <div className="p-2 rounded-lg bg-blue-100 transform group-hover:rotate-12 transition-transform duration-300">
          <Filter className="h-6 w-6 text-blue-600" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 text-right">Filter Members</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="relative group">
          <label className="block text-base font-semibold text-gray-700 mb-2 flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-500 transform group-hover:rotate-12 transition-transform duration-300" />
            Status
            {filters.status !== 'all' && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-blue-100 text-blue-800 animate-pulse">
                {filters.status === 'active' ? 'Active' : 
                 filters.status === 'inactive' ? 'Inactive' : 
                 filters.status === 'attention' ? 'Needs Attention' : 
                 filters.status}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-200 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 hover:border-blue-300 text-base font-medium text-gray-700 py-2 pr-4 pl-10 appearance-none bg-white"
              value={filters.status}
              onChange={(e) => onFilterChange({ ...filters, status: e.target.value })}
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="attention">Needs Attention</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none group-hover:text-blue-500 transition-colors duration-200" />
          </div>
        </div>

        <div className="relative group">
          <label className="block text-base font-semibold text-gray-700 mb-2 flex items-center gap-2">
            <Award className="h-5 w-5 text-yellow-500 transform group-hover:rotate-12 transition-transform duration-300" />
            Performance
            {filters.performance !== 'all' && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800 animate-pulse">
                {filters.performance === 'above' ? 'Above Target' : 'Below Target'}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-200 shadow-sm focus:border-yellow-500 focus:ring focus:ring-yellow-200 focus:ring-opacity-50 transition-all duration-200 hover:border-yellow-300 text-base font-medium text-gray-700 py-2 pr-4 pl-10 appearance-none bg-white"
              value={filters.performance}
              onChange={(e) => onFilterChange({ ...filters, performance: e.target.value })}
            >
              <option value="all">All Performance</option>
              <option value="above">Above Target</option>
              <option value="below">Below Target</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none group-hover:text-yellow-500 transition-colors duration-200" />
          </div>
        </div>

        <div className="relative group">
          <label className="block text-base font-semibold text-gray-700 mb-2 flex items-center gap-2">
            <Users className="h-5 w-5 text-purple-500 transform group-hover:rotate-12 transition-transform duration-300" />
            Level
            {filters.team !== 'all' && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-purple-100 text-purple-800 animate-pulse">
                {filters.team === 'beginner' ? 'Beginner' :
                 filters.team === 'intermediate' ? 'Intermediate' :
                 filters.team === 'advanced' ? 'Advanced' :
                 filters.team}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-200 shadow-sm focus:border-purple-500 focus:ring focus:ring-purple-200 focus:ring-opacity-50 transition-all duration-200 hover:border-purple-300 text-base font-medium text-gray-700 py-2 pr-4 pl-10 appearance-none bg-white"
              value={filters.team}
              onChange={(e) => onFilterChange({ ...filters, team: e.target.value })}
            >
              <option value="all">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none group-hover:text-purple-500 transition-colors duration-200" />
          </div>
        </div>

        <div className="relative group">
          <label className="block text-base font-semibold text-gray-700 mb-2 flex items-center gap-2">
            <Clock className="h-5 w-5 text-green-500 transform group-hover:rotate-12 transition-transform duration-300" />
            Time Range
            {filters.dateRange !== 'all' && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-green-100 text-green-800 animate-pulse">
                {filters.dateRange === 'today' ? 'Today' :
                 filters.dateRange === 'week' ? 'This Week' :
                 filters.dateRange === 'month' ? 'This Month' :
                 filters.dateRange}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-200 shadow-sm focus:border-green-500 focus:ring focus:ring-green-200 focus:ring-opacity-50 transition-all duration-200 hover:border-green-300 text-base font-medium text-gray-700 py-2 pr-4 pl-10 appearance-none bg-white"
              value={filters.dateRange}
              onChange={(e) => onFilterChange({ ...filters, dateRange: e.target.value })}
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none group-hover:text-green-500 transition-colors duration-200" />
          </div>
        </div>
      </div>
      
      {/* Active Filters */}
      {(filters.status !== 'all' || filters.performance !== 'all' || filters.team !== 'all' || filters.dateRange !== 'all') && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="flex items-center gap-2 text-base text-gray-700">
            <span className="font-semibold">Active Filters:</span>
            <div className="flex flex-wrap gap-2">
              {filters.status !== 'all' && (
                <span className="px-3 py-1.5 rounded-full bg-blue-100 text-blue-700 border-2 border-blue-200 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {filters.status === 'active' ? 'Active' :
                   filters.status === 'inactive' ? 'Inactive' :
                   filters.status === 'attention' ? 'Needs Attention' :
                   filters.status}
                </span>
              )}
              {filters.performance !== 'all' && (
                <span className="px-3 py-1.5 rounded-full bg-yellow-100 text-yellow-700 border-2 border-yellow-200 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Award className="h-4 w-4" />
                  {filters.performance === 'above' ? 'Above Target' : 'Below Target'}
                </span>
              )}
              {filters.team !== 'all' && (
                <span className="px-3 py-1.5 rounded-full bg-purple-100 text-purple-700 border-2 border-purple-200 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {filters.team === 'beginner' ? 'Beginner' :
                   filters.team === 'intermediate' ? 'Intermediate' :
                   filters.team === 'advanced' ? 'Advanced' :
                   filters.team}
                </span>
              )}
              {filters.dateRange !== 'all' && (
                <span className="px-3 py-1.5 rounded-full bg-green-100 text-green-700 border-2 border-green-200 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  {filters.dateRange === 'today' ? 'Today' :
                   filters.dateRange === 'week' ? 'This Week' :
                   filters.dateRange === 'month' ? 'This Month' :
                   filters.dateRange}
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};