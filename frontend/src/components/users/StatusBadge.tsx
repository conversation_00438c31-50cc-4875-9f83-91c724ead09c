import React from 'react';

interface StatusBadgeProps {
  status: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'active':
        return 'bg-gradient-to-r from-emerald-50 to-emerald-100 text-emerald-700 ring-emerald-600/20';
      case 'inactive':
        return 'bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 ring-gray-600/20';
      case 'attention':
        return 'bg-gradient-to-r from-red-50 to-red-100 text-red-700 ring-red-600/20';
      default:
        return 'bg-gradient-to-r from-gray-50 to-gray-100 text-gray-700 ring-gray-600/20';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      case 'attention':
        return 'Needs Attention';
      default:
        return status;
    }
  };

  return (
    <span 
      className={`inline-flex items-center px-2 py-1 text-sm font-medium rounded-full ring-1 transform hover:scale-105 transition-all duration-200 whitespace-nowrap md:text-sm md:px-2 md:py-1 ${getStatusStyles()}`}
    >
      {getStatusText(status)}
    </span>
  );
};