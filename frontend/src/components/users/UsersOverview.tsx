import React, { useState } from 'react';
import { UserTable } from '../users/UserTable';
import { AlertSection } from '../users/AlertSection';
import { FilterBar } from '../users/FilterBar';
import { StatisticCards } from '../users/StatisticCards';
import { users } from '../../data/users';

interface UsersOverviewProps {
  onUserSelect: (userId: string) => void;
}

export const UsersOverview: React.FC<UsersOverviewProps> = ({ onUserSelect }) => {
  const [filters, setFilters] = useState({
    status: 'all',
    performance: 'all',
    team: 'all',
    dateRange: 'all'
  });

  return (
    <div>
      <StatisticCards users={users} />
      <AlertSection users={users} onUserSelect={onUserSelect} />
      <FilterBar filters={filters} onFilterChange={setFilters} />
      <UserTable users={users} filters={filters} onUserSelect={onUserSelect} />
    </div>
  );
};