import React from 'react';
import { User } from '../../types/user';
import { Users, Target, AlertTriangle, TrendingUp, Trophy, Award } from 'lucide-react';
import { getOverviewById } from '../../data/overview';
import { calculateUserPerformance } from '../../utils/userPerformance';

interface StatisticCardsProps {
  users: User[];
}

export const StatisticCards: React.FC<StatisticCardsProps> = ({ users }) => {
  const totalUsers = users.length;
  const usersNeedingAction = users.filter(u => u.status === 'attention').length;

  const usersOnTarget = users.filter(user => {
    const userOverview = getOverviewById(user.id);
    if (!userOverview || !userOverview.progressOverview) return false;

    const performance = calculateUserPerformance(
      userOverview.progressOverview.engagementLevel,
      userOverview.progressOverview.progressLevel,
      userOverview.progressOverview.weeklyIntensity || 0
    );
    return performance >= 80;
  }).length;

  const validPerformances = users.map(user => {
    const userOverview = getOverviewById(user.id);
    if (!userOverview || !userOverview.progressOverview) return null;

    return calculateUserPerformance(
      userOverview.progressOverview.engagementLevel,
      userOverview.progressOverview.progressLevel,
      userOverview.progressOverview.weeklyIntensity || 0
    );
  }).filter(performance => performance !== null && !isNaN(performance)) as number[];

  const averagePerformance = validPerformances.length > 0
    ? Math.round(validPerformances.reduce((sum, perf) => sum + perf, 0) / validPerformances.length)
    : 0;

  const getPerformanceEmoji = (value: number) => {
    if (value >= 90) return '🌟';
    if (value >= 80) return '⭐';
    if (value >= 70) return '✨';
    return '💪';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" dir="rtl">
      <div className="relative bg-blue-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-blue-200 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <Trophy className="h-6 w-6 text-yellow-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-blue-200 text-blue-600 transform group-hover:rotate-12 transition-transform duration-300">
            <Users className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              👥
            </span>
            <span className="text-3xl font-bold text-blue-600 group-hover:scale-110 transition-transform duration-300">
              {totalUsers}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-blue-900">Active Members</div>
          <div className="text-xs text-blue-600 mt-1">Total in Community</div>
        </div>
      </div>

      <div className="relative bg-amber-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-amber-200 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <AlertTriangle className="h-6 w-6 text-red-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-amber-200 text-amber-600 transform group-hover:rotate-12 transition-transform duration-300">
            <AlertTriangle className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              ❗
            </span>
            <span className="text-3xl font-bold text-amber-600 group-hover:scale-110 transition-transform duration-300">
              {usersNeedingAction}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-amber-900">Need Support</div>
          <div className="text-xs text-amber-600 mt-1">Members to Check</div>
        </div>
      </div>

      <div className="relative bg-emerald-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-emerald-200 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <Award className="h-6 w-6 text-emerald-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-emerald-200 text-emerald-600 transform group-hover:rotate-12 transition-transform duration-300">
            <Target className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              🏆
            </span>
            <span className="text-3xl font-bold text-emerald-600 group-hover:scale-110 transition-transform duration-300">
              {usersOnTarget}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-emerald-900">Goal Achievers</div>
          <div className="text-xs text-emerald-600 mt-1">Succeeding in Goals</div>
        </div>
      </div>

      <div className="relative bg-purple-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-purple-200 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <TrendingUp className="h-6 w-6 text-purple-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-purple-200 text-purple-600 transform group-hover:rotate-12 transition-transform duration-300">
            <TrendingUp className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-end">
              <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {getPerformanceEmoji(averagePerformance)}
              </span>
              <span className="text-sm text-purple-500">%</span>
            </div>
            <span className="text-3xl font-bold text-purple-600 group-hover:scale-110 transition-transform duration-300">
              {String(averagePerformance)}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-purple-900">Group Spirit</div>
          <div className="text-xs text-purple-600 mt-1">Overall Energy Level</div>
        </div>
      </div>
    </div>
  );
};