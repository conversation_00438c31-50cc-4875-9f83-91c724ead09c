import React from "react";
import { User } from "../../types/user";
import { StatusBadge } from "./StatusBadge";
import { getOverviewById } from "../../data/overview";
import { calculateUserPerformance } from "../../utils/userPerformance";
import { getLastMessageDate } from "../../utils/messageUtils";
import { formatDate } from "../../utils/dateUtils";
import { Target, ChevronLeft, Medal, Crown, Award, Flame } from "lucide-react";

interface UserTableProps {
  filters: {
    status: string;
    performance: string;
    team: string;
    dateRange: string;
  };
  onUserSelect: (userId: string, userData: any) => void;
  users: User[]; // Accept users as a prop instead of fetching them
  loading?: boolean; // Optional loading state prop
  error?: string | null; // Optional error state prop
}

const UserTable: React.FC<UserTableProps> = ({
  filters,
  onUserSelect,
  users,
  loading = false,
  error = null,
}) => {
  const getPerformanceBadge = (performance: number, rank: number) => {
    if (rank === 0) {
      return (
        <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-100 to-amber-100 px-3 py-1.5 rounded-full border border-yellow-300 transform hover:scale-105 transition-all duration-200">
          <Crown className="h-5 w-5 text-yellow-600 animate-pulse" />
          <span className="font-medium text-yellow-700 text-sm whitespace-nowrap">
            First Place
          </span>
        </div>
      );
    }
    if (rank === 1) {
      return (
        <div className="flex items-center gap-2 bg-gradient-to-r from-gray-100 to-slate-100 px-3 py-1.5 rounded-full border border-gray-300 transform hover:scale-105 transition-all duration-200">
          <Medal className="h-5 w-5 text-gray-600" />
          <span className="font-medium text-gray-700 text-sm whitespace-nowrap">
            Second Place
          </span>
        </div>
      );
    }
    if (rank === 2) {
      return (
        <div className="flex items-center gap-2 bg-gradient-to-r from-orange-100 to-amber-100 px-3 py-1.5 rounded-full border border-orange-300 transform hover:scale-105 transition-all duration-200">
          <Award className="h-5 w-5 text-orange-600" />
          <span className="font-medium text-orange-700 text-sm whitespace-nowrap">
            Third Place
          </span>
        </div>
      );
    }
    if (performance >= 90) {
      return (
        <div className="flex items-center gap-2 bg-blue-100 px-3 py-1.5 rounded-full border border-blue-300 transform hover:scale-105 transition-all duration-200">
          <Flame className="h-5 w-5 text-blue-600" />
          <span className="font-medium text-blue-700 text-sm whitespace-nowrap">
            Outstanding
          </span>
        </div>
      );
    }
    return (
      <div className="flex items-center gap-2 bg-purple-100 px-3 py-1.5 rounded-full border border-purple-300 transform hover:scale-105 transition-all duration-200">
        <Target className="h-5 w-5 text-purple-600" />
        <span className="font-medium text-purple-700 text-sm whitespace-nowrap">
          Rising Star
        </span>
      </div>
    );
  };

  const filteredUsers = users.filter((user) => {
    if (filters.status !== "all" && user.status !== filters.status) {
      return false;
    }

    const userOverview = getOverviewById(user.id);
    const performance = calculateUserPerformance(
      userOverview.engagementLevel,
      userOverview.progressLevel,
      0
    );

    if (filters.performance !== "all") {
      const isAboveTarget = performance >= 80;
      if (filters.performance === "above" && !isAboveTarget) return false;
      if (filters.performance === "below" && isAboveTarget) return false;
    }
    if (
      filters.team !== "all" &&
      user.team.toLowerCase() !== filters.team.toLowerCase()
    ) {
      return false;
    }
    if (filters.dateRange !== "all") {
      const lastMessageDate = getLastMessageDate(user.id);
      if (!lastMessageDate) return false;

      const today = new Date();
      const diffTime = Math.abs(today.getTime() - lastMessageDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      switch (filters.dateRange) {
        case "today":
          if (diffDays > 1) return false;
          break;
        case "week":
          if (diffDays > 7) return false;
          break;
        case "month":
          if (diffDays > 30) return false;
          break;
      }
    }
    return true;
  });

  // Sort users by performance score
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    const aOverview = getOverviewById(a.id);
    const bOverview = getOverviewById(b.id);
    const aPerformance = calculateUserPerformance(
      aOverview.engagementLevel,
      aOverview.progressLevel,
      0
    );
    const bPerformance = calculateUserPerformance(
      bOverview.engagementLevel,
      bOverview.progressLevel,
      0
    );
    return bPerformance - aPerformance;
  });

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Loading users...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
        <p className="text-red-700">{error}</p>
        <button
          className="mt-4 px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    );
  }

  if (users.length === 0) {
    return (
      <div className="bg-white rounded-xl shadow-lg p-6 text-center">
        <p className="text-gray-600">No users found</p>
      </div>
    );
  }

  return (
    <div
      className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden transform hover:-translate-y-1 border border-gray-200"
      dir="rtl"
    >
      <div className="overflow-x-auto">
        <table className="w-full divide-y divide-gray-200">
          <thead className="bg-gradient-to-r from-gray-50 to-white">
            <tr>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-700">
                Rank
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-700">
                Member
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-700">
                Status
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-700">
                Level
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-700">
                Score
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-700">
                Last Active
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {sortedUsers.map((user, index) => {
              const userOverview = getOverviewById(user.id);
              const performance = calculateUserPerformance(
                userOverview.engagementLevel,
                userOverview.progressLevel,
                0
              );
              const lastMessageDate = getLastMessageDate(user.id);
              const lastActive = lastMessageDate
                ? formatDate(lastMessageDate)
                : "Never";

              return (
                <tr
                  key={user.id}
                  onClick={() => {
                    onUserSelect(user.id, user);
                  }}
                  className={`hover:bg-blue-50 cursor-pointer transition-all duration-300 group ${
                    index < 3 ? "bg-gradient-to-r from-gray-50 to-white" : ""
                  }`}
                >
                  <td className="px-4 py-4 text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      #{index + 1}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center gap-3">
                      <div className="relative group/avatar flex-shrink-0">
                        <div className="absolute -inset-0.5 bg-blue-500 rounded-full opacity-0 group-hover/avatar:opacity-75 transition-opacity duration-300 blur"></div>
                        <img
                          className="relative h-10 w-10 rounded-full object-cover transform group-hover/avatar:scale-105 transition-transform duration-300 border-2 border-gray-200 group-hover:border-blue-300"
                          src={user.avatar}
                          alt=""
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-base font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 flex items-center gap-1">
                          {user.name}
                          <ChevronLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transform group-hover:-translate-x-1 transition-all duration-200" />
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <StatusBadge status={user.status} />
                  </td>
                  <td className="px-4 py-4">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 transform hover:scale-105 transition-all duration-200">
                      {user.team === "beginner"
                        ? "Beginner"
                        : user.team === "intermediate"
                        ? "Intermediate"
                        : user.team === "advanced"
                        ? "Advanced"
                        : user.team}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center gap-4">
                      {getPerformanceBadge(performance, index)}
                      <div className="text-lg font-bold text-gray-900">
                        {performance}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <span className="text-sm font-medium text-gray-600 group-hover:text-gray-900 transition-colors duration-200">
                      {lastActive}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export { UserTable };
