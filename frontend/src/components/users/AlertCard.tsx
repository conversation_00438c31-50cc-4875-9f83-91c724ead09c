import React from "react";
import { User } from "../../types/user";
import { AlertTriangle, TrendingDown, Clock, ChevronLeft } from "lucide-react";
import { getLastMessageDate } from "../../utils/messageUtils";
import { formatDate } from "../../utils/dateUtils";

interface AlertCardProps {
  title: string;
  count: number;
  type: "attention" | "performance" | "overdue";
  users: User[];
  onUserSelect: (userId: string, userData: any) => void;
}

export const AlertCard: React.FC<AlertCardProps> = ({
  title,
  count,
  type,
  users,
  onUserSelect,
}) => {
  const getIcon = () => {
    switch (type) {
      case "attention":
        return <AlertTriangle className="h-6 w-6 text-red-500 animate-pulse" />;
      case "performance":
        return <TrendingDown className="h-6 w-6 text-yellow-500" />;
      case "overdue":
        return <Clock className="h-6 w-6 text-orange-500" />;
    }
  };

  const getBackground = () => {
    switch (type) {
      case "attention":
        return "bg-red-100";
      case "performance":
        return "bg-yellow-100";
      case "overdue":
        return "bg-orange-100";
    }
  };

  const getBorderColor = () => {
    switch (type) {
      case "attention":
        return "border-red-200";
      case "performance":
        return "border-yellow-200";
      case "overdue":
        return "border-orange-200";
    }
  };

  const getIconBackground = () => {
    switch (type) {
      case "attention":
        return "bg-red-200";
      case "performance":
        return "bg-yellow-200";
      case "overdue":
        return "bg-orange-200";
    }
  };

  return (
    <div
      className={`${getBackground()} rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 ${getBorderColor()} relative group`}
      dir="rtl"
    >
      <div className="absolute top-0 left-0 -mt-2 -ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {getIcon()}
      </div>

      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div
            className={`p-3 rounded-xl ${getIconBackground()} transform group-hover:rotate-12 transition-transform duration-300`}
          >
            {getIcon()}
          </div>
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-3xl font-bold text-gray-800 group-hover:scale-110 transition-transform duration-300">
            {count}
          </span>
          {count > 0 && type === "attention" && (
            <span className="text-lg animate-pulse">❗</span>
          )}
          {count > 0 && type === "performance" && (
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              📊
            </span>
          )}
          {count > 0 && type === "overdue" && (
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              ⏰
            </span>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {users.slice(0, 3).map((user) => {
          const lastMessageDate = getLastMessageDate(user.id);
          const lastActive = lastMessageDate
            ? formatDate(lastMessageDate)
            : "Never";

          return (
            <div
              key={user.id}
              onClick={() => onUserSelect(user.id, user)}
              className="bg-white rounded-lg p-3 cursor-pointer transform hover:scale-102 transition-all duration-200 hover:shadow-md group/item border border-gray-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <div className="absolute -inset-0.5 bg-blue-500 rounded-full opacity-0 group-hover/item:opacity-75 transition-opacity duration-300 blur"></div>
                    <img
                      className="relative h-10 w-10 rounded-full object-cover transform group-hover/item:scale-105 transition-transform duration-300"
                      src={user.avatar}
                      alt=""
                    />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{user.name}</div>
                    <div className="text-sm text-gray-500">{lastActive}</div>
                  </div>
                </div>
                <ChevronLeft className="h-5 w-5 text-gray-400 transform group-hover/item:-translate-x-1 transition-transform duration-200" />
              </div>
              {user.attentionNote && (
                <div
                  className={`mt-2 text-sm px-3 py-1.5 rounded-lg flex items-center gap-2 transform hover:scale-102 transition-transform duration-200
                  ${
                    type === "attention"
                      ? "bg-red-100 text-red-700"
                      : type === "performance"
                      ? "bg-yellow-100 text-yellow-700"
                      : "bg-orange-100 text-orange-700"
                  }`}
                >
                  {type === "attention" && (
                    <AlertTriangle className="h-4 w-4 animate-pulse" />
                  )}
                  {type === "performance" && (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  {type === "overdue" && <Clock className="h-4 w-4" />}
                  {user.attentionNote}
                </div>
              )}
            </div>
          );
        })}
        {users.length > 3 && (
          <button className="w-full text-sm font-medium text-blue-600 hover:text-blue-800 py-2 flex items-center justify-center gap-1 group/button">
            Show all {users.length} members
            <ChevronLeft className="h-4 w-4 transform group-hover/button:-translate-x-1 transition-transform duration-200" />
          </button>
        )}
      </div>
    </div>
  );
};
