import React, { useState, useEffect } from "react";
import { MacroDisplay } from "../meal/MacroDisplay";
import { MacroRange, FoodItem, MealCategory } from "../../types/food";
import { Utensils, Bee<PERSON>, Cookie } from "lucide-react";
import { mealsApi } from "../meal/services/meals.api";
import { getFoodByCategoryAndName } from "../../data/foodDatabase";
interface UserMealPlanProps {
  userId: string;
}

interface MealWithFood {
  id: string;
  name: string;
  categories: MealCategory[];
  macroRange: MacroRange;
  foodItems?: FoodItem[];
}

export const UserMealPlan: React.FC<UserMealPlanProps> = ({ userId }) => {
  const [meals, setMeals] = useState<MealWithFood[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [foodItems, setFoodItems] = useState<Record<string, FoodItem>>({});
  const [totalMacroRange, setTotalMacroRange] = useState<MacroRange>({
    min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
    max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
  });

  const user = localStorage.getItem("user");
  const userData = user ? JSON.parse(user) : {};

  const transformFetchedMeals = (fetchedMeals: any) => {
    return fetchedMeals.map((meal: any) => {
      // Transform the categories and options structure
      const transformedCategories = [];

      // Check if meal has categories
      if (meal.categories && meal.categories.length > 0) {
        // Group food items by category
        const proteinItems = meal.categories.filter(
          (item: any) => item.name === "Protein"
        );
        const carbItems = meal.categories.filter(
          (item: any) => item.name === "Carb"
        );

        if (proteinItems.length > 0) {
          transformedCategories.push({
            name: "Protein",
            options: proteinItems[0].options.map((item: any) => ({
              foodId: item.foodId,
              amount: item.amount || 0,
            })),
          });
        }

        // Create Carb category if it has items
        if (carbItems.length > 0) {
          transformedCategories.push({
            name: "Carb",
            options: carbItems[0].options.map((item: any) => ({
              foodId: item.foodId,
              amount: item.amount || 0,
            })),
          });
        }
      }

      return {
        id: meal.id,
        name: meal.name,
        categories: transformedCategories,
        macroRange: meal.macroRange || {
          min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
          max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
        },
        foodItems: meal.foodItems || [],
      };
    });
  };

  // Initialize food items from meals' foodItems
  const initializeFoodItems = (mealsList: MealWithFood[]) => {
    const newFoodItems: Record<string, FoodItem> = {};
    mealsList.forEach((meal) => {
      if (meal.foodItems && meal.foodItems.length) {
        meal.foodItems.forEach((item) => {
          newFoodItems[item.id] = item;
        });
      }
    });
    setFoodItems(newFoodItems);
    return newFoodItems;
  };

  // Calculate total macros directly from meal macroRanges
  const calculateTotalMacros = (mealsList: MealWithFood[]) => {
    const total: MacroRange = {
      min: { protein: 0, carbs: 0, fats: 0, calories: 0 },
      max: { protein: 0, carbs: 0, fats: 0, calories: 0 },
    };

    mealsList.forEach((meal) => {
      if (meal.macroRange) {
        // Add min values
        if (meal.macroRange.min) {
          total.min.protein += meal.macroRange.min.protein || 0;
          total.min.carbs += meal.macroRange.min.carbs || 0;
          total.min.fats += meal.macroRange.min.fats || 0;
          total.min.calories += meal.macroRange.min.calories || 0;
        }

        // Add max values
        if (meal.macroRange.max) {
          total.max.protein += meal.macroRange.max.protein || 0;
          total.max.carbs += meal.macroRange.max.carbs || 0;
          total.max.fats += meal.macroRange.max.fats || 0;
          total.max.calories += meal.macroRange.max.calories || 0;
        }
      }
    });

    return total;
  };

  useEffect(() => {
    setLoading(true);

    mealsApi
      .findByTrainee(userData.id)
      .then((response) => {
        const transformedMeals = transformFetchedMeals(response);

        initializeFoodItems(transformedMeals);

        const totalMacros = calculateTotalMacros(transformedMeals);
        setTotalMacroRange(totalMacros);

        setMeals(transformedMeals);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching meals:", error);
        setError("Failed to load meal plan");
        setLoading(false);
      });
  }, [userId]);

  if (loading) {
    return (
      <div className="p-6 text-center" dir="rtl">
        Loading meal plan...
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center text-red-500" dir="rtl">
        {error}
      </div>
    );
  }

  if (meals.length === 0) {
    return (
      <div className="max-w-4xl p-6 mx-auto" dir="rtl">
        <div className="p-6 text-center bg-white border-2 border-blue-200 shadow-lg rounded-xl">
          <div className="text-gray-600">
            No meal plan found. Wait for trainer update.
          </div>
        </div>
      </div>
    );
  }

  const proteinPerServe = (category: string, name: string) => {
    const getFoodItem = getFoodByCategoryAndName(category, name);
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <div className="p-6 bg-white border-2 border-blue-200 shadow-lg rounded-xl">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 transition-transform duration-300 transform bg-blue-100 rounded-xl hover:rotate-12">
            <Utensils className="w-6 h-6 text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-800">
            Daily Meal Plan
          </h2>
        </div>

        <div className="mb-6">
          <MacroDisplay
            macroRange={totalMacroRange}
            title="Total Daily Macros"
          />
        </div>

        <div className="space-y-6">
          {meals.map((meal) => (
            <div
              key={meal.id}
              className="p-6 transition-all duration-300 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md"
            >
              <h3 className="mb-4 text-lg font-semibold text-gray-800">
                {meal.name}
              </h3>

              <div className="space-y-6">
                {/* Protein Options */}
                {meal.categories.find((cat) => cat.name === "Protein") && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 mb-3">
                      <Beef className="w-5 h-5 text-blue-600" />
                      <h4 className="font-medium text-gray-700">
                        Protein Options
                      </h4>
                    </div>
                    <div className="grid grid-cols-1 gap-2">
                      {meal.categories
                        .find((cat) => cat.name === "Protein")
                        ?.options.map((option, idx) => {
                          const food = foodItems[option.foodId];
                          return (
                            <div
                              key={idx}
                              className="flex items-center justify-between p-3 transition-all duration-200 transform rounded-lg bg-blue-50 hover:scale-102"
                            >
                              <span className="text-blue-900">
                                {food ? food.name : "Loading..."} -{" "}
                                {option.amount}g
                              </span>
                              {food && (
                                <div className="text-sm text-blue-700">
                                  {Math.round(
                                    (food.macrosPer100g.protein *
                                      option.amount) /
                                      100
                                  )}
                                  g protein
                                </div>
                              )}
                            </div>
                          );
                        })}
                    </div>
                  </div>
                )}
                {/* Carb Options */}
                {meal.categories.find((cat) => cat.name === "Carb") && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 mb-3">
                      <Cookie className="w-5 h-5 text-amber-600" />
                      <h4 className="font-medium text-gray-700">
                        Carb Options
                      </h4>
                    </div>
                    <div className="grid grid-cols-1 gap-2">
                      {meal.categories
                        .find((cat) => cat.name === "Carb")
                        ?.options.map((option, idx) => {
                          const food = foodItems[option.foodId];
                          return (
                            <div
                              key={idx}
                              className="flex items-center justify-between p-3 transition-all duration-200 transform rounded-lg bg-amber-50 hover:scale-102"
                            >
                              <span className="text-amber-900">
                                {food ? food.name : "Loading..."} -{" "}
                                {option.amount}g
                              </span>
                              {food && (
                                <div className="text-sm text-amber-700">
                                  {Math.round(
                                    (food.macrosPer100g.carbs * option.amount) /
                                      100
                                  )}
                                  g carbs
                                </div>
                              )}
                            </div>
                          );
                        })}
                    </div>
                  </div>
                )}

                {/* Use the meal's macroRange directly */}
                <MacroDisplay
                  macroRange={meal.macroRange}
                  title="Meal Macro Range"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
