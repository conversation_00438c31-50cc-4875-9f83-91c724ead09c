import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Play,
  Square,
  FastForward,
  CheckCircle,
  HelpCircle,
} from "lucide-react";
import { useWorkout } from "../../contexts/WorkoutContext";
import { useWorkoutLog } from "../../contexts/WorkoutLogContext";
import { WorkoutLog } from "../../types/workout";
import { trainingPlansApi } from "../training/service/training-plans.api";
import { workoutLogsApi } from "../../service/workout-logs.api";
import { workoutSetsApi } from "../../service/workout-sets.api";
import axios from "axios";

interface Exercise {
  id: string;
  sets: string;
  reps: string;
  rest: string;
  instructions?: string;
  name: string;
}

interface Day {
  id: string;
  day: string;
  focus: string;
  exercises: Exercise[];
}

interface WorkoutLoggerProps {
  userId: string;
  onWorkoutStart: () => void;
  onWorkoutEnd: () => void;
  onRestStart: (duration: number) => void;
  onPauseToggle: (paused: boolean) => void;
  restTimer: number;
  isPaused: boolean;
}

interface ExerciseLog {
  name: string;
  sets: { weight: number; reps: number }[];
}

export const WorkoutLogger: React.FC<WorkoutLoggerProps> = ({
  userId,
  onWorkoutStart,
  onWorkoutEnd,
  onRestStart,
  onPauseToggle,
  restTimer,
  isPaused,
}) => {
  const {
    isWorkoutStarted,
    setIsWorkoutStarted,
    selectedDay,
    setSelectedDay,
    currentExerciseIndex,
    setCurrentExerciseIndex,
    activeExercise,
    setActiveExercise,
    weight,
    setWeight,
    reps,
    setReps,
  } = useWorkout();

  const { addWorkoutLog } = useWorkoutLog();
  const [exerciseLogs, setExerciseLogs] = useState<ExerciseLog[]>([]);
  const [showFinishConfirmation, setShowFinishConfirmation] = useState(false);
  const [showControlsHelp, setShowControlsHelp] = useState(false);
  const [trainingPlan, setTrainingPlan] = useState<Day[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState<string | null>(null);
  const [workoutLog, setWorkoutLog] = useState<any>(null);
  // Add workout start time tracking
  const [workoutStartTime, setWorkoutStartTime] = useState<Date | null>(null);
  const [totalPausedTime, setTotalPausedTime] = useState<number>(0);
  const [pauseStartTime, setPauseStartTime] = useState<Date | null>(null);
  const [workoutLogId, setWorkoutLogId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Effect to clear error after 3 seconds
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    if (error) {
      timeoutId = setTimeout(() => {
        setError(null);
      }, 3000);
    }

    // Cleanup function to clear timeout
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [error]);

  async function getTrainingPlans() {
    const res = await trainingPlansApi.getById(userId);
    setTrainingPlan(res);
  }

  async function getById(trainingPlanId: string | null) {
    const res = await trainingPlansApi.getByTrainingId(trainingPlanId);

    const newTrainingPlan: Day = {
      id: res.id,
      day: res.day,
      focus: res.focus,
      exercises: res.exercises.map((exercise: any) => ({
        id: exercise.id,
        name: exercise.name,
        instructions: exercise.instructions,
        sets: exercise.sets,
        reps: exercise.reps,
        rest: exercise.rest,
      })),
    };

    setTrainingPlan((prevPlans) => {
      const otherPlans = prevPlans.filter(
        (plan) => plan.id !== newTrainingPlan.id
      );
      return [...otherPlans, newTrainingPlan]; // Keep all plans
    });

    return res;
  }

  useEffect(() => {
    getTrainingPlans();
  }, []);

  // Track paused time when isPaused changes
  useEffect(() => {
    if (isPaused && !pauseStartTime) {
      // Starting a pause
      setPauseStartTime(new Date());
    } else if (!isPaused && pauseStartTime) {
      // Ending a pause
      const now = new Date();
      const pauseDuration = Math.floor(
        (now.getTime() - pauseStartTime.getTime()) / 1000
      );
      setTotalPausedTime((prev) => prev + pauseDuration);
      setPauseStartTime(null);
    }
  }, [isPaused]);

  const handleDayChange = (selectedDayValue: string) => {
    setSelectedDay(selectedDayValue);
    const selectedPlan = trainingPlan.find(
      (day) => day.day === selectedDayValue
    );
    const planId = selectedPlan ? selectedPlan.id : null;
    setSelectedPlanId(planId);
  };

  const startWorkout = async () => {
    if (!selectedPlanId) {
      console.error("Error: No selected training plan.");
      return;
    }

    // Record workout start time
    setWorkoutStartTime(new Date());
    setTotalPausedTime(0);
    setPauseStartTime(null);

    try {
      const res = await getById(selectedPlanId);

      if (!res || !res.exercises.length) {
        console.error("Error: No exercises found in training plan.");
        return;
      }

      const workoutLogRes = await workoutLogsApi.create({
        trainingPlanId: res.id,
        date: new Date().toISOString().split("T")[0],
      });

      setWorkoutLogId(workoutLogRes.id);
      setWorkoutLog(workoutLogRes);

      // 🔹 Create a Map for fast lookup
      const workoutLogMap = new Map(
        workoutLogRes.exercises.map((exercise) => [exercise.name, exercise.id])
      );

      // 🔹 Assign correct `id` for each exercise
      const updatedExercises = res.exercises.map((exercise: any) => {
        const exerciseId = workoutLogMap.get(exercise.name) || "";

        if (!exerciseId) {
          console.error(`Error: Missing ID for exercise "${exercise.name}"`);
        }

        return {
          exercise_id: exercise.id,
          id: exerciseId,
          name: exercise.name,
          instructions: exercise.instructions,
          reps: exercise.reps,
          rest: exercise.rest,
          sets: exercise.sets,
        };
      });

      setTrainingPlan((prevPlans) => {
        const otherPlans = prevPlans.filter((plan) => plan.id !== res.id);
        return [...otherPlans, { ...res, exercises: updatedExercises }];
      });

      if (updatedExercises.length === 0) {
        console.error(" Error: No exercises available to start.");
        return;
      }

      const firstExercise = updatedExercises[0];

      setActiveExercise({
        id: firstExercise.id,
        exercise_id: firstExercise.exercise_id,
        name: firstExercise.name,
        instructions: firstExercise.instructions,
        targetReps: firstExercise.reps,
        rest: firstExercise.rest,
        sets: Array(parseInt(firstExercise.sets.match(/\d+/)?.[0] || "1")).fill(
          {
            weight: 0,
            reps: 0,
            completed: false,
          }
        ),
        currentSet: 0,
      });

      setCurrentExerciseIndex(0);
      setIsWorkoutStarted(true);
      setExerciseLogs([]);
      onWorkoutStart();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response) {
          setError(
            error.response.data.message ||
              "Something went wrong. Please try again."
          );
        } else if (error.request) {
          setError(
            "No response from server. Please check your internet connection."
          );
        } else {
          setError("An unexpected error occurred. Please try again.");
        }
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
      console.error("Error:", error);
    }
  };

  const skipExercise = () => {
    const workout = trainingPlan.find((day) => day.day === selectedDay);
    if (!workout || currentExerciseIndex >= workout.exercises.length - 1)
      return;

    // Save current exercise logs if any sets were completed
    if (activeExercise && activeExercise.currentSet > 0) {
      const completedSets = activeExercise.sets
        .slice(0, activeExercise.currentSet)
        .map((set) => ({
          weight: set.weight,
          reps: set.reps,
        }));

      setExerciseLogs((prev) => [
        ...prev,
        {
          name: activeExercise.name,
          sets: completedSets,
        },
      ]);
    }

    const nextExercise = workout.exercises[currentExerciseIndex + 1];
    const sets = Array(parseInt(nextExercise.sets)).fill({
      weight: 0,
      reps: 0,
      completed: false,
    });

    setActiveExercise({
      exercise_id: nextExercise.id,
      name: nextExercise.name,
      sets,
      currentSet: 0,
      rest: nextExercise.rest,
      instructions: nextExercise.instructions,
      targetReps: nextExercise.reps,
    });

    setCurrentExerciseIndex(currentExerciseIndex + 1);
    onRestStart(0);
    setWeight("");
    setReps("");
  };

  const skipRest = () => {
    onRestStart(0);
  };

  const togglePause = () => {
    onPauseToggle(!isPaused);
  };

  const handleFinishWorkoutClick = () => {
    if (activeExercise) {
      // Save completed sets in exerciseLogs
      if (activeExercise.currentSet > 0) {
        const completedSets = activeExercise.sets
          .slice(0, activeExercise.currentSet)
          .map((set) => ({
            weight: set.weight,
            reps: set.reps,
          }));

        setExerciseLogs((prev) => [
          ...prev,
          { name: activeExercise.name, sets: completedSets },
        ]);
      }
    }

    setShowFinishConfirmation(true);
  };

  // Calculate workout duration in minutes
  const calculateWorkoutDuration = (): string => {
    if (!workoutStartTime) return "0 min";

    const now = new Date();
    let totalSeconds = Math.floor(
      (now.getTime() - workoutStartTime.getTime()) / 1000
    );

    // Subtract paused time
    let pausedSeconds = totalPausedTime;

    // Add current pause if workout is paused
    if (isPaused && pauseStartTime) {
      pausedSeconds += Math.floor(
        (now.getTime() - pauseStartTime.getTime()) / 1000
      );
    }

    // Subtract total paused time
    totalSeconds -= pausedSeconds;

    // Convert to minutes and round to the nearest minute
    const minutes = Math.max(1, Math.round(totalSeconds / 60));
    return `${minutes} min`;
  };

  const confirmFinishWorkout = async () => {
    try {
      const workout = trainingPlan.find((day) => day.day === selectedDay);

      if (workout) {
        // Calculate actual duration
        const duration = calculateWorkoutDuration();

        const workoutLogs: WorkoutLog = {
          id: selectedPlanId,
          date: new Date().toISOString().split("T")[0],
          trainingPlanId: selectedPlanId,
          userId: userId,
          duration: duration,
          type: workout.focus,
          exercises: exerciseLogs,
        };

        await workoutLogsApi.complete(workoutLog.id);
        await addWorkoutLog(workoutLogId || "", workoutLogs);
      }

      setIsWorkoutStarted(false);
      setActiveExercise(null);
      setCurrentExerciseIndex(0);
      setWeight("");
      setReps("");
      setExerciseLogs([]);
      setShowFinishConfirmation(false);
      setWorkoutStartTime(null);
      setTotalPausedTime(0);
      setPauseStartTime(null);
      onWorkoutEnd();
    } catch (error) {
      console.error("Error saving workout log:", error);
    }
  };

  const completeSet = async () => {
    if (!activeExercise || !weight || !reps || !workoutLog) {
      console.error("Missing required data for completing set.");
      return;
    }

    const workout = trainingPlan.find((day) => day.day === selectedDay);
    if (!workout) {
      console.error("❌ Error: No matching training plan found.");
      return;
    }

    const currentWeight = parseFloat(weight);
    const currentReps = parseInt(reps);

    // Map workoutLog exercises for fast lookup
    const workoutLogMap = new Map<string, string>(
      workoutLog.exercises.map((exercise: any) => [exercise.name, exercise.id])
    );

    // Ensure workoutExerciseId is a string
    const exerciseId = workoutLogMap.get(activeExercise.name) ?? "";

    const setData = {
      workoutExerciseId: exerciseId,
      setNumber: activeExercise.currentSet + 1,
      weight: currentWeight,
      reps: currentReps,
    };

    try {
      await workoutSetsApi.create(setData);
    } catch (error) {
      console.error("Error logging workout set:", error);
    }

    // Update activeExercise state with completed set
    const newSets = [...activeExercise.sets];
    newSets[activeExercise.currentSet] = {
      weight: currentWeight,
      reps: currentReps,
      completed: true,
    };

    const restTime = parseInt(activeExercise.rest) || 60;
    onRestStart(restTime);
    setWeight("");
    setReps("");

    if (activeExercise.currentSet === activeExercise.sets.length - 1) {
      setExerciseLogs((prev) => [
        ...prev,
        {
          name: activeExercise.name,
          sets: newSets,
        },
      ]);

      if (currentExerciseIndex < workout.exercises.length - 1) {
        const nextExercise = workout.exercises[currentExerciseIndex + 1];

        // Ensure nextExerciseId is always a string
        const nextExerciseId = workoutLogMap.get(nextExercise.name) ?? "";

        if (!nextExerciseId) {
          console.error("❌ Error: Missing ID for Next Exercise", nextExercise);
        }

        setActiveExercise({
          id: nextExerciseId || "",
          exercise_id: nextExercise.id,
          name: nextExercise.name,
          sets: Array(
            parseInt(nextExercise.sets.match(/\d+/)?.[0] || "1")
          ).fill({
            weight: 0,
            reps: 0,
            completed: false,
          }),
          currentSet: 0,
          rest: nextExercise.rest,
          instructions: nextExercise.instructions,
          targetReps: nextExercise.reps,
        });

        setCurrentExerciseIndex(currentExerciseIndex + 1);
      } else {
        handleFinishWorkoutClick();
      }
    } else {
      setActiveExercise({
        ...activeExercise,
        sets: newSets,
        currentSet: activeExercise.currentSet + 1,
      });
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Display workout duration in the workout screen
  const renderWorkoutDuration = () => {
    if (!workoutStartTime) return null;

    return (
      <div className="bg-blue-50 p-3 rounded-lg">
        <div className="text-sm text-blue-800">Workout Duration</div>
        <div className="text-lg font-semibold text-blue-900">
          {calculateWorkoutDuration()}
        </div>
      </div>
    );
  };

  if (!isWorkoutStarted) {
    return (
      <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-blue-200">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-3 rounded-xl bg-blue-100 transform hover:rotate-12 transition-transform duration-300">
              <Dumbbell className="h-6 w-6 text-blue-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800">Workout Log</h2>
          </div>

          <div className="space-y-4">
            {/* Error Message with Transition */}
            {error && (
              <div
                className="bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded relative transition-all duration-300 ease-in-out"
                role="alert"
              >
                <span className="block sm:inline">{error}</span>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Workout Day
              </label>

              <select
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                value={selectedDay}
                onChange={(e) => handleDayChange(e.target.value)}
              >
                <option value="">Choose Day</option>
                {trainingPlan.map((day, index) => (
                  <option key={index} value={day.day}>
                    {day.day} - {day.focus}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={startWorkout}
              disabled={!selectedDay}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Start Workout
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!activeExercise) return null;

  const workout = trainingPlan.find((day) => day.day === selectedDay);
  const remainingSets = activeExercise.sets.length - activeExercise.currentSet;
  const remainingExercises = workout
    ? workout.exercises.length - currentExerciseIndex - 1
    : 0;

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-blue-200">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-xl bg-blue-100 transform hover:rotate-12 transition-transform duration-300">
              <Dumbbell className="h-6 w-6 text-blue-600" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800">
              {selectedDay}
            </h2>
          </div>
          {restTimer > 0 && (
            <div className="flex items-center gap-4">
              <button
                onClick={skipRest}
                className="flex items-center gap-2 bg-yellow-100 px-4 py-2 rounded-lg hover:bg-yellow-200 transition-colors duration-200"
              >
                <FastForward className="h-5 w-5 text-yellow-600" />
                <span className="font-medium text-yellow-800">Skip Rest</span>
              </button>
              <div className="flex items-center gap-2 bg-yellow-100 px-4 py-2 rounded-lg">
                <Timer className="h-5 w-5 text-yellow-600" />
                <span className="font-medium text-yellow-800">
                  Rest: {formatTime(restTimer)}
                </span>
              </div>
            </div>
          )}
        </div>

        <div className="space-y-6">
          {/* Controls Help */}
          <div className="bg-blue-50 rounded-lg p-4 relative">
            <button
              onClick={() => setShowControlsHelp(!showControlsHelp)}
              className="absolute top-2 right-2 p-2 rounded-full hover:bg-blue-100 transition-colors duration-200"
            >
              <HelpCircle className="h-5 w-5 text-blue-600" />
            </button>
            {showControlsHelp && (
              <div className="space-y-3 mt-2">
                <h4 className="font-medium text-blue-800">Control Buttons:</h4>
                <ul className="space-y-2 text-blue-700">
                  <li className="flex items-center gap-2">
                    <Square className="h-4 w-4" /> Pause Workout - Stops rest
                    timer and allows break
                  </li>
                  <li className="flex items-center gap-2">
                    <Play className="h-4 w-4" /> Continue Workout - Resumes
                    workout after pause
                  </li>
                  <li className="flex items-center gap-2">
                    <SkipForward className="h-4 w-4" /> Skip Exercise - Move to
                    next exercise
                  </li>
                  <li className="flex items-center gap-2">
                    <FastForward className="h-4 w-4" /> Skip Rest - Ends current
                    rest period
                  </li>
                  <li className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" /> Finish Workout -
                    Completes current workout
                  </li>
                </ul>
              </div>
            )}
          </div>

          {/* Current Exercise */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-800">
                {activeExercise.name}
              </h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={togglePause}
                  className={`p-2 rounded-lg transition-colors duration-200 ${
                    isPaused
                      ? "bg-green-100 hover:bg-green-200 text-green-700"
                      : "bg-yellow-100 hover:bg-yellow-200 text-yellow-700"
                  }`}
                  title={isPaused ? "Continue Workout" : "Pause Workout"}
                >
                  {isPaused ? (
                    <Play className="h-5 w-5" />
                  ) : (
                    <Square className="h-5 w-5" />
                  )}
                </button>
                <button
                  onClick={skipExercise}
                  className="p-2 bg-purple-100 rounded-lg hover:bg-purple-200 transition-colors duration-200"
                  title="Skip Exercise"
                >
                  <SkipForward className="h-5 w-5 text-purple-700" />
                </button>
                <button
                  onClick={handleFinishWorkoutClick}
                  className="p-2 bg-red-100 rounded-lg hover:bg-red-200 transition-colors duration-200"
                  title="Finish Workout"
                >
                  <CheckCircle className="h-5 w-5 text-red-700" />
                </button>
              </div>
            </div>

            {/* Finish Workout Confirmation Dialog */}
            {showFinishConfirmation && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Are you sure you want to finish the workout?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Progress cannot be recovered after finishing the workout.
                  </p>
                  <div className="flex justify-end gap-4">
                    <button
                      onClick={() => setShowFinishConfirmation(false)}
                      className="px-4 py-2 text-gray-700 hover:text-gray-900 transition-colors duration-200"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={confirmFinishWorkout}
                      className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                    >
                      Finish Workout
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeExercise.currentSet === 0 && activeExercise.instructions && (
              <div className="mb-4 p-4 bg-blue-50 rounded-lg text-blue-800">
                {activeExercise.instructions}
              </div>
            )}

            {restTimer === 0 && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Weight (kg)
                    </label>
                    <input
                      type="number"
                      value={weight}
                      onChange={(e) => setWeight(e.target.value)}
                      className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      placeholder="Enter weight"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Reps (Target: {activeExercise.targetReps})
                    </label>
                    <input
                      type="number"
                      value={reps}
                      onChange={(e) => setReps(e.target.value)}
                      className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      placeholder="Enter reps"
                    />
                  </div>
                </div>

                <button
                  onClick={completeSet}
                  disabled={!weight || !reps}
                  className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Complete Set
                </button>
              </div>
            )}

            <div className="mt-4 grid grid-cols-3 gap-4">
              <div className="bg-purple-50 p-3 rounded-lg">
                <div className="text-sm text-purple-800">Sets Remaining</div>
                <div className="text-lg font-semibold text-purple-900">
                  {remainingSets}
                </div>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="text-sm text-green-800">
                  Exercises Remaining
                </div>
                <div className="text-lg font-semibold text-green-900">
                  {remainingExercises}
                </div>
              </div>
              {renderWorkoutDuration()}
            </div>
          </div>

          {/* Next Exercise Preview */}
          {remainingExercises > 0 && workout && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="text-sm text-gray-500 mb-2">Next Exercise</div>
              <div className="font-medium text-gray-800">
                {workout.exercises[currentExerciseIndex + 1].name}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
