import axios from 'axios';
import { API } from '../../../utils/api';


export const userApi = {
  getAll: async (trainerId?: string): Promise<[]> => {
  
    let config = {
      method: 'get',
      url: `${API}/trainer-trainee/trainees/${trainerId}?onlyActive=true`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
    };
  
    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },
};
