import React, { useEffect, useState } from "react";
import { trainingPlansApi } from "../../components/training/service/training-plans.api";
import { Dumbbell } from "lucide-react";

// Define interfaces for the data structure
interface Exercise {
  id: string;
  sets: string;
  reps: string;
  rest: string;
  instructions?: string;
  name: string;
}

interface Day {
  id: string;
  day: string;
  focus: string;
  exercises: Exercise[];
}

interface UserWorkoutPlanProps {
  userId: string;
}

export const UserWorkoutPlan: React.FC<UserWorkoutPlanProps> = ({ userId }) => {
  const [trainingPlan, setTrainingPlan] = useState<Day[]>([]);

  async function getTrainingPlans() {
    const res = await trainingPlansApi.getById(userId);
    setTrainingPlan(res);
  }

  useEffect(() => {
    getTrainingPlans();
  }, [userId]);

  return (
    <div className="max-w-4xl mx-auto space-y-6" dir="rtl">
      <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-blue-200">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-3 rounded-xl bg-blue-100 transform hover:rotate-12 transition-transform duration-300">
            <Dumbbell className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-800">
            תוכנית אימונים שבועית
          </h2>
        </div>

        <div className="space-y-6">
          {trainingPlan.map((day, dayIndex) => (
            <div
              key={dayIndex}
              className="bg-white rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-200"
            >
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                {day.day} - {day.focus}
              </h3>

              <div className="space-y-4">
                {day.exercises.map((exercise, exerciseIndex) => (
                  <div
                    key={exerciseIndex}
                    className="bg-gray-50 rounded-lg p-4"
                  >
                    <h4 className="font-medium text-gray-900 mb-2">
                      {exercise.name}
                    </h4>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="bg-blue-50 p-2 rounded-lg">
                        <div className="text-sm text-blue-800 font-medium">
                          {exercise.sets}
                        </div>
                        <div className="text-xs text-blue-600">סטים</div>
                      </div>

                      <div className="bg-green-50 p-2 rounded-lg">
                        <div className="text-sm text-green-800 font-medium">
                          {exercise.reps}
                        </div>
                        <div className="text-xs text-green-600">חזרות</div>
                      </div>

                      <div className="bg-purple-50 p-2 rounded-lg">
                        <div className="text-sm text-purple-800 font-medium">
                          {exercise.rest}
                        </div>
                        <div className="text-xs text-purple-600">מנוחה</div>
                      </div>
                    </div>

                    {exercise.instructions && (
                      <div className="mt-2 text-sm text-gray-600 bg-yellow-50 p-2 rounded-lg">
                        {exercise.instructions}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
