import React from "react";

interface CustomLoaderProps {
  message?: string;
}

export const CustomLoader: React.FC<CustomLoaderProps> = ({
  message = "Loading data, Please wait",
}) => {
  return (
    <div
      className="d-flex flex-column justify-content-center align-items-center position-relative"
      style={{ height: "100%" }}
    >
      {/* Spinner */}
      <div className="spinner-border text-primary" role="status"></div>

      {/* Loading message */}
      <p className="mt-2 fs-4">{message}</p>
    </div>
  );
};
