import axios from 'axios';
import { API } from '../../../utils/api';

export const trainingExerciseApi = {
  create: async (data: any) => {
    const token = localStorage.getItem('access_token');
    return axios.post(`${API}/training-exercises`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  getAll: async () => {
    const token = localStorage.getItem('access_token');
    return axios.get(`${API}/training-exercises`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }).then(res => res.data);
  },

  getByTrainingPlanId: async (trainingPlanId: string) => {
    const token = localStorage.getItem('access_token');
    return axios.get(`${API}/training-exercises/plan/${trainingPlanId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }).then(res => res.data);
  },

  update: async (id: string, data: any) => {
    const token = localStorage.getItem('access_token');
    return axios.put(`${API}/training-exercises/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  delete: async (id: string) => {
    const token = localStorage.getItem('access_token');
    return axios.delete(`${API}/training-exercises/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }).then(res => res.data);
  }
};
