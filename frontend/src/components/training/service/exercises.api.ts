import axios from 'axios';
import { API } from '../../../utils/api';
import { Exercise } from '../../../types/exercise';

export const exercisesApi = {
  getAll: async (): Promise<Exercise[]> => {
    let config = {
      method: 'get',
      url: `${API}/exercises`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  create: async (data: any) => {
    const token = localStorage.getItem('access_token');
    return axios.post(API, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  }
};
