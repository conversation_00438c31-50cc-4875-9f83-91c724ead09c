import axios from 'axios';
import { API } from '../../../utils/api';

export const trainingPlansApi = {
  create: async (data: any) => {
    const token = localStorage.getItem('access_token');
    return axios.post(`${API}/training-plans`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  getById: async (id: string) => {
    const token = localStorage.getItem('access_token');
    return axios.get(`${API}/training-plans/user/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }).then(res => res.data);
  },

  getByTrainingId: async (id: string | null) => {
    const token = localStorage.getItem('access_token');
    return axios.get(`${API}/training-plans/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }).then(res => res.data);
  },


  delete: async (id: string) => {
    const token = localStorage.getItem('access_token');
    return axios.delete(`${API}/training-plans/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }).then(res => res.data);
  }
  
};
