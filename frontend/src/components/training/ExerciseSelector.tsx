import React, { useEffect, useState } from "react";
import { ExerciseOption, exerciseCategories } from "../../types/training";
import { exercisesApi } from "./service/exercises.api";

interface ExerciseSelectorProps {
  onExerciseSelect: (exercise: ExerciseOption) => void;
  prefersDarkMode?: boolean;
}

export const ExerciseSelector: React.FC<ExerciseSelectorProps> = ({
  onExerciseSelect,
  prefersDarkMode = false,
}) => {
  const [exercises, setExercises] = useState<ExerciseOption[]>([]);

  async function getExercises() {
    const res = await exercisesApi.getAll();
    const formattedExercises: ExerciseOption[] = res.map((exercise) => ({
      ...exercise,
      category: exercise.category as
        | "chest"
        | "back"
        | "legs"
        | "shoulders"
        | "arms"
        | "abs"
        | "cardio",
      id: exercise.id,
    }));

    setExercises(formattedExercises);
  }

  useEffect(() => {
    getExercises();
  }, []);

  return (
    <div className="grid grid-cols-2 gap-4">
      {Object.entries(exerciseCategories).map(([category, label]) => (
        <div key={category}>
          <label
            className={`block text-sm font-medium ${
              prefersDarkMode ? "text-gray-300" : "text-gray-700"
            } mb-2`}
          >
            {label}
          </label>
          <select
            className={`w-full p-2 pr-8 border rounded-lg appearance-none cursor-pointer transition-colors duration-200 ${
              prefersDarkMode
                ? "border-purple-700 bg-gray-800 text-gray-200 hover:border-purple-600"
                : "border-gray-300 bg-white text-gray-800 hover:border-gray-400"
            }`}
            onChange={(e) => {
              const selectedExerciseId = e.target.value;
              const exercise = exercises.find(
                (ex) => ex.id === selectedExerciseId
              );
              if (exercise) onExerciseSelect(exercise);
              e.target.value = "";
            }}
            defaultValue=""
            style={{
              backgroundImage: `url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='${
                prefersDarkMode ? "%23A78BFA" : "%236B7280"
              }' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E")`,
              backgroundPosition: "right 0.5rem center",
              backgroundRepeat: "no-repeat",
              backgroundSize: "1.5em 1.5em",
              paddingRight: "2.5rem",
            }}
          >
            <option value="" disabled>
              Select Exercise
            </option>
            {exercises
              .filter((ex) => ex.category === category)
              .map((exercise) => (
                <option
                  key={exercise.id}
                  value={exercise.id}
                  className={prefersDarkMode ? "bg-gray-800" : "bg-white"}
                >
                  {exercise.name}
                </option>
              ))}
          </select>
        </div>
      ))}
    </div>
  );
};
