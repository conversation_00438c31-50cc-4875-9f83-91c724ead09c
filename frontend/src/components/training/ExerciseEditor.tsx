import React, { useState } from 'react';
import { Exercise } from '../../types/training';
import { Dumbbell, AlertCircle, X, Edit2, Check } from 'lucide-react';

interface ExerciseEditorProps {
  exercise: Exercise;
  onUpdate: (updatedExercise: Exercise) => void;
  onDelete: () => void;
  isEditing?: boolean;
  prefersDarkMode?: boolean;
}

export const ExerciseEditor: React.FC<ExerciseEditorProps> = ({
  exercise,
  onUpdate,
  onDelete,
  isEditing: isPlanEditing = true,
  prefersDarkMode = false
}) => {
  const [isExerciseEditing, setIsExerciseEditing] = useState(false);
  const [editedExercise, setEditedExercise] = useState(exercise);

  const handleSave = () => {
    onUpdate(editedExercise);
    setIsExerciseEditing(false);
  };

  return (
    <div className={`${prefersDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 border`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Dumbbell className={`h-5 w-5 ${prefersDarkMode ? 'text-purple-400' : 'text-blue-600'}`} />
          {isExerciseEditing ? (
            <input
              type="text"
              value={editedExercise.name}
              onChange={(e) => setEditedExercise({ ...editedExercise, name: e.target.value })}
              className={`px-2 py-1 border rounded-md focus:outline-none focus:ring-2 ${
                prefersDarkMode
                  ? 'border-purple-700 bg-gray-800 text-gray-200 focus:ring-purple-600'
                  : 'border-gray-300 focus:ring-blue-500 text-gray-800'
              }`}
            />
          ) : (
            <h4 className={`font-medium ${prefersDarkMode ? 'text-gray-200' : 'text-gray-900'}`}>{exercise.name}</h4>
          )}
        </div>
        <div className="flex items-center gap-2">
          {isExerciseEditing ? (
            <>
              <button
                onClick={handleSave}
                className={`p-1 rounded-full ${prefersDarkMode ? 'bg-green-900 text-green-300 hover:bg-green-800' : 'hover:bg-green-100 text-green-600'} transition-colors duration-200`}
              >
                <Check className="h-4 w-4" />
              </button>
              <button
                onClick={() => {
                  setIsExerciseEditing(false);
                  setEditedExercise(exercise);
                }}
                className={`p-1 rounded-full ${prefersDarkMode ? 'bg-red-900 text-red-300 hover:bg-red-800' : 'hover:bg-red-100 text-red-600'} transition-colors duration-200`}
              >
                <X className="h-4 w-4" />
              </button>
            </>
          ) : (
            // Only show edit/delete buttons if the plan is in editing mode
            isPlanEditing && (
              <>
                <button
                  onClick={() => setIsExerciseEditing(true)}
                  className={`p-1 rounded-full ${prefersDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'} transition-colors duration-200`}
                >
                  <Edit2 className="h-4 w-4" />
                </button>
                <button
                  onClick={onDelete}
                  className={`p-1 rounded-full ${prefersDarkMode ? 'bg-red-900 text-red-300 hover:bg-red-800' : 'hover:bg-red-100 text-red-600'} transition-colors duration-200`}
                >
                  <X className="h-4 w-4" />
                </button>
              </>
            )
          )}
        </div>
      </div>

      {isExerciseEditing ? (
        <div className="space-y-3">
          <div>
            <label className={`block text-sm font-medium ${prefersDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Sets</label>
            <input
              type="text"
              value={editedExercise.sets}
              onChange={(e) => setEditedExercise({ ...editedExercise, sets: e.target.value })}
              className={`w-full px-2 py-1 border rounded-md focus:outline-none focus:ring-2 ${
                prefersDarkMode
                  ? 'border-purple-700 bg-gray-800 text-gray-200 focus:ring-purple-600'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
            />
          </div>
          <div>
            <label className={`block text-sm font-medium ${prefersDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Reps</label>
            <input
              type="text"
              value={editedExercise.reps}
              onChange={(e) => setEditedExercise({ ...editedExercise, reps: e.target.value })}
              className={`w-full px-2 py-1 border rounded-md focus:outline-none focus:ring-2 ${
                prefersDarkMode
                  ? 'border-purple-700 bg-gray-800 text-gray-200 focus:ring-purple-600'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
            />
          </div>
          <div>
            <label className={`block text-sm font-medium ${prefersDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Rest</label>
            <input
              type="text"
              value={editedExercise.rest}
              onChange={(e) => setEditedExercise({ ...editedExercise, rest: e.target.value })}
              className={`w-full px-2 py-1 border rounded-md focus:outline-none focus:ring-2 ${
                prefersDarkMode
                  ? 'border-purple-700 bg-gray-800 text-gray-200 focus:ring-purple-600'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
            />
          </div>
          <div>
            <label className={`block text-sm font-medium ${prefersDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Instructions</label>
            <textarea
              value={editedExercise.instructions || ''}
              onChange={(e) => setEditedExercise({ ...editedExercise, instructions: e.target.value })}
              className={`w-full px-2 py-1 border rounded-md focus:outline-none focus:ring-2 ${
                prefersDarkMode
                  ? 'border-purple-700 bg-gray-800 text-gray-200 focus:ring-purple-600'
                  : 'border-gray-300 focus:ring-blue-500'
              }`}
              rows={2}
            />
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <div className="grid grid-cols-3 gap-2">
            <div className={`${prefersDarkMode ? 'bg-purple-900' : 'bg-blue-50'} p-2 rounded-lg`}>
              <div className={`text-sm ${prefersDarkMode ? 'text-purple-200 font-medium' : 'text-blue-800 font-medium'}`}>{exercise.sets}</div>
              <div className={`text-xs ${prefersDarkMode ? 'text-purple-300' : 'text-blue-600'}`}>Sets</div>
            </div>
            <div className={`${prefersDarkMode ? 'bg-green-900' : 'bg-green-50'} p-2 rounded-lg`}>
              <div className={`text-sm ${prefersDarkMode ? 'text-green-200 font-medium' : 'text-green-800 font-medium'}`}>{exercise.reps}</div>
              <div className={`text-xs ${prefersDarkMode ? 'text-green-300' : 'text-green-600'}`}>Reps</div>
            </div>
            <div className={`${prefersDarkMode ? 'bg-blue-900' : 'bg-purple-50'} p-2 rounded-lg`}>
              <div className={`text-sm ${prefersDarkMode ? 'text-blue-200 font-medium' : 'text-purple-800 font-medium'}`}>{exercise.rest}</div>
              <div className={`text-xs ${prefersDarkMode ? 'text-blue-300' : 'text-purple-600'}`}>Rest</div>
            </div>
          </div>
          {exercise.instructions && (
            <div className={`flex items-start gap-1 mt-2 ${prefersDarkMode ? 'bg-amber-900' : 'bg-amber-50'} p-2 rounded`}>
              <AlertCircle className={`${prefersDarkMode ? 'h-3 w-3 text-amber-300' : 'h-4 w-4 text-amber-600'} mt-0.5 flex-shrink-0`} />
              <div className={`${prefersDarkMode ? 'text-xs text-amber-200' : 'text-sm text-amber-800'}`}>{exercise.instructions}</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};