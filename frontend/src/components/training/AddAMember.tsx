import React, { useState, useEffect } from 'react';
import { User<PERSON>lus, X, Check, Loader2, Alert<PERSON>riangle, Phone } from 'lucide-react';
import { trainerAssignmentApi } from '../../service/trainer-assignment-service.api';
import defaultProfile from '../../default-profile-image.png';

// Define types for more precise type checking
type TeamLevel = 'beginner' | 'intermediate' | 'advanced';
type FitnessGoal = 'fat_loss' | 'muscle_gain';
type Gender = 'male' | 'female' | 'other';
type ActivityLevel = 'sedentary' | 'lightly_active' | 'moderately_active' | 'very_active' | 'extra_active';

// Interface for user data structure
interface UserData {
  name: string;
  email: string;
  mobileNumber: string;
  countryCode: string;
  avatar: string;
  team: TeamLevel;
  goal: FitnessGoal;
  startingWeight: number;
  startingFatPercentage: number;
  age: number;
  gender: Gender;
  height: number;
  activityLevel: ActivityLevel;
}

interface TrainerAssignmentData {
  trainee_name: string;
  trainerId: string;
  trainee_email: string;
  trainee_mobileNumber: string;
  isActive: boolean;
  startDate: string;
  endDate: string;
  approved: boolean;
  trainee_age: number;
  trainee_profileImageUrl: string;
  trainee_level: TeamLevel;
  trainee_fitnessGoal: FitnessGoal;
  trainee_weight: number;
  trainee_bodyFatPercentage: number;
  trainee_gender: Gender;
  trainee_height: number;
  trainee_activityLevel: ActivityLevel;
}

// Props interface for the component
interface AdminPanelProps {
  onAddUser: (userData: UserData) => void;
}

export const AdminPanel: React.FC<AdminPanelProps> = ({ onAddUser }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<UserData>({
    name: '',
    email: '',
    mobileNumber: '',
    countryCode: '+972',
    avatar: defaultProfile,
    team: 'beginner',
    goal: 'fat_loss',
    startingWeight: 0,
    startingFatPercentage: 0,
    age: 0,
    gender: 'male',
    height: 0,
    activityLevel: 'moderately_active',
  });


  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [error]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const storedUser = JSON.parse(localStorage.getItem("user") || "{}");

      // Format the full mobile number with country code
      const fullMobileNumber = formData.mobileNumber ?
        `${formData.countryCode}${formData.mobileNumber.startsWith('0') ?
          formData.mobileNumber.substring(1) : formData.mobileNumber}` : '';

      // Prepare the data for trainer assignment using the form data
      const assignmentData: TrainerAssignmentData = {
        trainee_name: formData.name,
        trainerId: storedUser.id,
        trainee_email: formData.email,
        trainee_mobileNumber: fullMobileNumber,
        isActive: true,
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString().split('T')[0],
        approved: true,
        trainee_age: formData.age,
        trainee_profileImageUrl: formData.avatar,
        trainee_level: formData.team,
        trainee_fitnessGoal: formData.goal,
        trainee_weight: formData.startingWeight,
        trainee_bodyFatPercentage: formData.startingFatPercentage,
        trainee_gender: formData.gender,
        trainee_height: formData.height,
        trainee_activityLevel: formData.activityLevel
      };

      // Call the trainer assignment API and get the response
      const assignmentResponse = await trainerAssignmentApi.assignTrainer(assignmentData);

      // Pass the actual data from the API response to the parent component
      const newUserData = {
        ...formData,
        id: assignmentResponse.trainee.id,
        assignmentId: assignmentResponse.id
      };

      onAddUser(newUserData);

      // Reset form and close
      setFormData({
        name: '',
        email: '',
        mobileNumber: '',
        countryCode: '+972',
        avatar: defaultProfile,
        team: 'beginner',
        goal: 'fat_loss',
        startingWeight: 0,
        startingFatPercentage: 0,
        age: 0,
        gender: 'male',
        height: 0,
        activityLevel: 'moderately_active',
      });
      setIsLoading(false);
      setIsOpen(false);
    } catch (error) {
      console.error("Failed to assign trainer:", error);
      setError('An error occurred while adding the member. Please try again.');
      setIsLoading(false);
    }
  };


  const updateFormData = <K extends keyof UserData>(key: K, value: UserData[K]) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="w-full bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 mb-8 flex items-center justify-center gap-3 border-2 border-blue-200 group"
      >
        <div className="p-3 rounded-xl bg-blue-100 transform group-hover:rotate-12 transition-transform duration-300">
          <UserPlus className="h-6 w-6 text-blue-600" />
        </div>
        <span className="text-lg font-semibold text-gray-800">Add New Member</span>
      </button>
    );
  }

  return (
    <div className="w-full bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 mb-8 border-2 border-blue-200">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-xl bg-blue-100">
            <UserPlus className="h-6 w-6 text-blue-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-800">Add New Member</h2>
        </div>
        <button
          onClick={() => setIsOpen(false)}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
        >
          <X className="h-5 w-5 text-gray-500" />
        </button>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-red-700 font-medium">Error</p>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
              <input
                type="text"
                required
                value={formData.name}
                onChange={(e) => updateFormData('name', e.target.value)}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
              <input
                type="email"
                required
                value={formData.email}
                onChange={(e) => updateFormData('email', e.target.value)}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
            </div>
          </div>
        </div>

        {/* Contact Information - New section for mobile number */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                <Phone className="h-4 w-4 text-gray-500 mr-2" />
                Mobile Number <span className="text-red-500 ml-1">*</span>
              </label>
              <div className="flex">
                {/* Country Code Selector */}
                <select
                  value={formData.countryCode}
                  onChange={(e) => updateFormData('countryCode', e.target.value)}
                  className="w-24 h-10 px-2 border-2 border-gray-200 rounded-l-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 appearance-none"
                >
                  <option value="+972">+972 🇮🇱</option>
                  <option value="+1">+1 🇺🇸</option>
                  <option value="+44">+44 🇬🇧</option>
                  <option value="+33">+33 🇫🇷</option>
                  <option value="+49">+49 🇩🇪</option>
                  <option value="+7">+7 🇷🇺</option>
                  <option value="+86">+86 🇨🇳</option>
                  <option value="+91">+91 🇮🇳</option>
                </select>
                {/* Phone Number Input */}
                <input
                  type="tel"
                  required
                  value={formData.mobileNumber}
                  onChange={(e) => updateFormData('mobileNumber', e.target.value)}
                  className="flex-1 h-10 px-3 border-2 border-gray-200 rounded-r-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  placeholder="50-1234567"
                  autoComplete="tel"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Enter a WhatsApp-enabled number to receive updates and stay connected with their fitness journey.
              </p>
            </div>
            {/* Empty div to maintain grid layout */}
            <div></div>
          </div>
        </div>

        {/* Demographics */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Demographics</h3>
          <div className="grid grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Age</label>
              <input
                type="number"
                required
                min="0"
                max="120"
                value={formData.age || ''}
                onChange={(e) => updateFormData('age', parseInt(e.target.value))}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
              <select
                value={formData.gender}
                onChange={(e) => updateFormData('gender', e.target.value as Gender)}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              >
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Level</label>
              <select
                value={formData.team}
                onChange={(e) => updateFormData('team', e.target.value as TeamLevel)}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              >
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
          </div>
        </div>

        {/* Measurements */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Measurements</h3>
          <div className="grid grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Height (cm)</label>
              <input
                type="number"
                required
                min="0"
                max="300"
                value={formData.height || ''}
                onChange={(e) => updateFormData('height', parseInt(e.target.value))}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Starting Weight (kg)</label>
              <input
                type="number"
                required
                min="0"
                max="300"
                step="0.1"
                value={formData.startingWeight || ''}
                onChange={(e) => updateFormData('startingWeight', parseFloat(e.target.value))}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Starting Body Fat (%)</label>
              <input
                type="number"
                required
                min="0"
                max="100"
                step="0.1"
                value={formData.startingFatPercentage || ''}
                onChange={(e) => updateFormData('startingFatPercentage', parseFloat(e.target.value))}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
            </div>
          </div>
        </div>

        {/* Goals and Activity */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Goals and Activity Level</h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Goal</label>
              <select
                value={formData.goal}
                onChange={(e) => updateFormData('goal', e.target.value as FitnessGoal)}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              >
                <option value="fat_loss">Fat Loss</option>
                <option value="muscle_gain">Muscle Gain</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Activity Level</label>
              <select
                value={formData.activityLevel}
                onChange={(e) => updateFormData('activityLevel', e.target.value as ActivityLevel)}
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              >
                <option value="sedentary">Sedentary (little or no exercise)</option>
                <option value="lightly_active">Lightly Active (light exercise 1-3 days/week)</option>
                <option value="moderately_active">Moderately Active (moderate exercise 3-5 days/week)</option>
                <option value="very_active">Very Active (hard exercise 6-7 days/week)</option>
                <option value="extra_active">Extra Active (very hard exercise & physical job)</option>
              </select>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
          >

            {isLoading ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin" />
                <span>Adding...</span>
              </>
            ) : (
              <>
                <Check className="h-5 w-5" />
                <span>Add Member</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};