import React, { useState } from "react";
import { TrainingDay, Exercise, ExerciseOption } from "../../types/training";
import { ExerciseEditor } from "./ExerciseEditor";
import { ExerciseSelector } from "./ExerciseSelector";
import {
  Calendar,
  ChevronDown,
  ChevronUp,
  Edit2,
  Check,
  X,
} from "lucide-react";
import { trainingPlansApi } from "./service/training-plans.api";

interface TrainingDayEditorProps {
  day: TrainingDay;
  onUpdate: (updatedDay: TrainingDay) => void;
  onDelete: () => void;
  isNew?: boolean;
  prefersDarkMode?: boolean;
}

export const TrainingDayEditor: React.FC<TrainingDayEditorProps> = ({
  day,
  onUpdate,
  onDelete,
  isNew = false,
  prefersDarkMode = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(isNew);
  const [editedDay, setEditedDay] = useState(day.day);
  const [editedFocus, setEditedFocus] = useState(day.focus);
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);

  // Check if this is a new plan (no ID)
  const isNewPlan = !day.id;

  const handleExerciseAdd = (exercise: ExerciseOption) => {
    const newExercise: Exercise = {
      id: exercise.id,
      name: exercise.name,
      sets: exercise.defaultSets,
      reps: exercise.defaultReps,
      rest: exercise.defaultRest,
      instructions: exercise.instructions,
    };

    onUpdate({
      ...day,
      exercises: [...day.exercises, newExercise],
    });
  };

  const handleExerciseUpdate = (index: number, updatedExercise: Exercise) => {
    const newExercises = [...day.exercises];
    newExercises[index] = updatedExercise;
    onUpdate({
      ...day,
      exercises: newExercises,
    });
  };

  const handleExerciseDelete = (index: number) => {
    onUpdate({
      ...day,
      exercises: day.exercises.filter((_, i) => i !== index),
    });
  };

  const handleSave = () => {
    onUpdate({
      ...day,
      day: editedDay,
      focus: editedFocus,
    });

    setShowSaveSuccess(true);
    setTimeout(() => setShowSaveSuccess(false), 3000);
    setIsEditing(false);
  };

  const handleCreateTrainingPlan = async () => {
    try {
      const payload = {
        day: editedDay,
        focus: editedFocus,
        traineeId: localStorage.getItem("trainee_id"),
        exercises: day.exercises.map((ex) => ({
          exerciseId: ex.id,
          sets: ex.sets,
          name: ex.name,
          reps: ex.reps,
          rest: ex.rest,
          instructions: ex.instructions,
        })),
      };

      const response = await trainingPlansApi.create(payload);

      // Update the day object with the ID returned from the API
      if (response && response.id) {
        onUpdate({
          ...day,
          id: response.id,
        });
        setShowSaveSuccess(true);
        setTimeout(() => setShowSaveSuccess(false), 3000);
      }
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className={`${prefersDarkMode ? 'bg-gray-900 border-purple-800' : 'bg-white border-gray-200'} rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border-2`}>
      <div className={`p-4 ${prefersDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gradient-to-r from-indigo-50 to-white border-gray-200'} border-b`}>
        {showSaveSuccess && (
          <div className={`p-2 mt-2 text-sm ${prefersDarkMode ? 'text-green-300 bg-green-900 border border-green-700' : 'text-green-600 bg-green-50'} rounded-lg`}>
            Changes saved successfully
          </div>
        )}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1">
            <div className={`p-2 rounded-lg ${prefersDarkMode ? 'bg-purple-900' : 'bg-indigo-100'} transform group-hover:rotate-12 transition-transform duration-300`}>
              <Calendar className={`h-5 w-5 ${prefersDarkMode ? 'text-purple-300' : 'text-indigo-600'}`} />
            </div>
            {isEditing ? (
              <div className="flex items-center gap-4 flex-1">
                <input
                  type="text"
                  value={editedDay}
                  onChange={(e) => setEditedDay(e.target.value)}
                  className={`w-32 px-2 py-1 border rounded-md focus:outline-none focus:ring-2 ${
                    prefersDarkMode
                      ? 'border-purple-700 bg-gray-800 text-gray-200 focus:ring-purple-600'
                      : 'border-gray-300 focus:ring-indigo-500'
                  }`}
                  placeholder="Day"
                />
                <input
                  type="text"
                  value={editedFocus}
                  onChange={(e) => setEditedFocus(e.target.value)}
                  className={`flex-1 px-2 py-1 border rounded-md focus:outline-none focus:ring-2 ${
                    prefersDarkMode
                      ? 'border-purple-700 bg-gray-800 text-gray-200 focus:ring-purple-600'
                      : 'border-gray-300 focus:ring-indigo-500'
                  }`}
                  placeholder="Focus"
                />
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleSave}
                    className={`p-1 rounded-full ${prefersDarkMode ? 'bg-green-900 text-green-300 hover:bg-green-800' : 'hover:bg-green-100 text-green-600'} transition-colors duration-200`}
                  >
                    <Check className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => {
                      setIsEditing(false);
                      setEditedDay(day.day);
                      setEditedFocus(day.focus);
                    }}
                    className={`p-1 rounded-full ${prefersDarkMode ? 'bg-red-900 text-red-300 hover:bg-red-800' : 'hover:bg-red-100 text-red-600'} transition-colors duration-200`}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-2 flex-1">
                <h3 className={`text-lg font-semibold ${prefersDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                  {day.day} - {day.focus}
                </h3>
                <button
                  onClick={() => {
                    setIsEditing(true);
                    setEditedDay(day.day);
                    setEditedFocus(day.focus);
                  }}
                  className={`p-1 rounded-full ${prefersDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-400'} transition-colors duration-200`}
                >
                  <Edit2 className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={onDelete}
              className={`p-1 rounded-full ${prefersDarkMode ? 'bg-red-900 text-red-300 hover:bg-red-800' : 'hover:bg-red-100 text-red-600'} transition-colors duration-200`}
            >
              <X className="h-5 w-5" />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={`p-1 rounded-full ${prefersDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-400'} transition-colors duration-200`}
            >
              {isExpanded ? (
                <ChevronUp className="h-5 w-5" />
              ) : (
                <ChevronDown className="h-5 w-5" />
              )}
            </button>
            {isNewPlan ? (
              <button
                onClick={handleCreateTrainingPlan}
                className={`flex items-center gap-2 px-4 py-2 ${prefersDarkMode ? 'bg-green-800 hover:bg-green-700' : 'bg-green-600 hover:bg-green-700'} text-white rounded-lg transition-colors duration-200`}
              >
                Save Training Plan
              </button>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className={`flex items-center gap-2 px-4 py-2 ${prefersDarkMode ? 'bg-purple-800 hover:bg-purple-700' : 'bg-blue-600 hover:bg-blue-700'} text-white rounded-lg transition-colors duration-200`}
              >
                Edit Plan
              </button>
            )}
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className={`p-4 space-y-4 ${prefersDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
          {/* Show ExerciseSelector only for new plans or when in editing mode for existing plans */}
          {(isNewPlan || isEditing) && (
            <ExerciseSelector onExerciseSelect={handleExerciseAdd} prefersDarkMode={prefersDarkMode} />
          )}

          <div className="space-y-4">
            {day.exercises.map((exercise, index) => (
              <ExerciseEditor
                key={index}
                exercise={exercise}
                onUpdate={(updatedExercise) =>
                  handleExerciseUpdate(index, updatedExercise)
                }
                onDelete={() => handleExerciseDelete(index)}
                isEditing={isEditing}
                prefersDarkMode={prefersDarkMode}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
