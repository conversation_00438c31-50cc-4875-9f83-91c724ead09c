import React from 'react';
import { <PERSON><PERSON><PERSON>ange } from '../../types/food';
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'lucide-react';

interface MacroDisplayProps {
  macroRange: MacroRange;
  title?: string;
  showTitle?: boolean;
  isDarkMode?: boolean;
}

export const MacroDisplay: React.FC<MacroDisplayProps> = ({
  macroRange,
  title = "Macro Range",
  showTitle = true,
  isDarkMode = false
}) => {
  const formatNumber = (num: number) => Math.round(num);

  return (
    <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 ${isDarkMode ? 'border border-gray-700' : ''}`}>
      {showTitle && (
        <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'} mb-3 text-right`}>{title}</h3>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-4">
          <div className={`flex items-center justify-between gap-2 ${isDarkMode ? 'bg-blue-900/20 border border-blue-700' : 'bg-blue-50'} p-3 rounded-lg`}>
            <div className="flex items-center gap-2">
              <Beef className={`h-5 w-5 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} />
              <span className={`font-medium ${isDarkMode ? 'text-blue-300' : 'text-blue-800'}`}>Protein</span>
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-blue-300' : 'text-blue-600'}`}>
              {formatNumber(macroRange.min.protein)}-{formatNumber(macroRange.max.protein)}g
            </div>
          </div>

          <div className={`flex items-center justify-between gap-2 ${isDarkMode ? 'bg-amber-900/20 border border-amber-700' : 'bg-amber-50'} p-3 rounded-lg`}>
            <div className="flex items-center gap-2">
              <Cookie className={`h-5 w-5 ${isDarkMode ? 'text-amber-400' : 'text-amber-600'}`} />
              <span className={`font-medium ${isDarkMode ? 'text-amber-300' : 'text-amber-800'}`}>Carbs</span>
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-amber-300' : 'text-amber-600'}`}>
              {formatNumber(macroRange.min.carbs)}-{formatNumber(macroRange.max.carbs)}g
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className={`flex items-center justify-between gap-2 ${isDarkMode ? 'bg-green-900/20 border border-green-700' : 'bg-green-50'} p-3 rounded-lg`}>
            <div className="flex items-center gap-2">
              <Scale className={`h-5 w-5 ${isDarkMode ? 'text-green-400' : 'text-green-600'}`} />
              <span className={`font-medium ${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>Fat</span>
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-green-300' : 'text-green-600'}`}>
              {formatNumber(macroRange.min.fats)}-{formatNumber(macroRange.max.fats)}g
            </div>
          </div>

          <div className={`flex items-center justify-between gap-2 ${isDarkMode ? 'bg-red-900/20 border border-red-700' : 'bg-red-50'} p-3 rounded-lg`}>
            <div className="flex items-center gap-2">
              <Flame className={`h-5 w-5 ${isDarkMode ? 'text-red-400' : 'text-red-600'}`} />
              <span className={`font-medium ${isDarkMode ? 'text-red-300' : 'text-red-800'}`}>Calories</span>
            </div>
            <div className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-600'}`}>
              {formatNumber(macroRange.min.calories)}-{formatNumber(macroRange.max.calories)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};