import axios from 'axios';
import { FoodItem, MacroNutrients } from '../../../types/food';
import { API } from '../../../utils/api';


export const foodItemsApi = {
  getAll: async (category?: string): Promise<FoodItem[]> => {
  
    let config = {
      method: 'get',
      url: `${API}/food-items`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      },
      params: category ? { category } : {}
    };
  
    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },
  
  // Fetch a single food item by ID
  getOne: async (id: string): Promise<FoodItem> => {
    const response = await axios.get(`${API}/food-items/${id}`, {
      headers: { Authorization: `Bearer ${localStorage.getItem('access_token')}` }
    });
    return response.data;
},

  // Create a new food item
  create: async (foodItem: Omit<FoodItem, 'id'>): Promise<FoodItem> => {
    const response = await axios.post(API, foodItem, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });
    return response.data;
  },

  // Update an existing food item
  update: async (id: string, foodItem: Partial<FoodItem>): Promise<FoodItem> => {
    const response = await axios.patch(`${API}/${id}`, foodItem, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`
      }
    });
    return response.data;
  },

  // Delete a food item
  remove: async (id: string): Promise<void> => {
    await axios.delete(`${API}/${id}`, {
      headers: { Authorization: `Bearer ${localStorage.getItem('token')}` }
    });
  },

  // Calculate macros for a food item based on amount
  calculateMacros: async (foodId: string, amount: number): Promise<MacroNutrients> => {
    const response = await axios.post(
      `${API}/macros`,
      { foodId, amount },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${localStorage.getItem('token')}`
        }
      }
    );
    return response.data;
  }
};
