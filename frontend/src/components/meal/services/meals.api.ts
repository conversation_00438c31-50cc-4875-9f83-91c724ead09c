import axios from "axios";
import { API } from "../../../utils/api";

export interface MealType {
  name: string;
  description: string;
  mealTime: string;
  traineeId: string;
  isFeatured: boolean;
  defaultServing: string;
  maxServing: string;
  minServing: string;
  macroRange: {
    min: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
    max: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
  };
  categories: {
    id: string;
    name: string;
  }[];
  foodItemIds: string[];
}

export const mealsApi = {
  // Create a new food item
  create: async (meal: MealType): Promise<MealType> => {
    let data = JSON.stringify(meal);
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${API}/meals`,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        "Content-Type": "application/json",
      },
      data: data,
    };

    try {
      const response = await axios.request<MealType>(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  findByTrainee: async (traineeId: string): Promise<MealType[]> => {
    let config = {
      method: "get",
      url: `${API}/meals/trainee/${traineeId}`,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        "Content-Type": "application/json",
      },
    };

    try {
      const response = await axios.request<MealType[]>(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  update: async (mealId: string, mealData: MealType): Promise<MealType> => {
    let config = {
      method: "put",
      url: `${API}/meals/${mealId}`,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        "Content-Type": "application/json",
      },
      data: mealData,
    };

    try {
      const response = await axios.request<MealType>(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  remove: async (mealId: string): Promise<MealType[]> => {
    let config = {
      method: "delete",
      url: `${API}/meals/${mealId}`,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        "Content-Type": "application/json",
      },
    };

    try {
      const response = await axios.request<MealType[]>(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },
};
