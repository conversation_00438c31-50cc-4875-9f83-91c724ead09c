import React, { useState, useEffect } from "react";
import { FoodItem } from "../../types/food";
import { Plus, Minus } from "lucide-react";
import { foodItemsApi } from "./services/food-items.api";

interface FoodSelectorProps {
  food: FoodItem;
  amount: number;
  onAmountChange: (amount: number, food: FoodItem) => void;
  onRemove: () => void;
  isNewMeal?: boolean;
  showOptions?: boolean;
}

export const FoodSelector: React.FC<FoodSelectorProps> = ({
  food,
  amount,
  onAmountChange,
  onRemove,
  isNewMeal = false,
  showOptions = true,
}) => {
  const [foods, setFoods] = useState<FoodItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [localAmount, setLocalAmount] = useState(amount);

  useEffect(() => {
    setLocalAmount(amount);
  }, [amount]);

  useEffect(() => {
    const fetchFoods = async () => {
      try {
        setLoading(true);
        const data = await foodItemsApi.getAll();
        setFoods(data);
        setError(null);
      } catch (err) {
        setError("Failed to load food items");
        console.error("Error fetching food items:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchFoods();
  }, []);

  const handleIncrement = () => {
    if (food.maxServing && localAmount >= food.maxServing) return;
    const newAmount = localAmount + 10;
    setLocalAmount(newAmount);
    onAmountChange(newAmount, { ...food, defaultServing: newAmount });
  };

  const handleDecrement = () => {
    if (food.minServing && localAmount <= food.minServing) {
      onRemove();
      return;
    }
    const newAmount = Math.max(0, localAmount - 10);
    setLocalAmount(newAmount);
    onAmountChange(newAmount, { ...food, defaultServing: newAmount });
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newAmountInput = parseInt(e.target.value) || 0;
    let newAmount = newAmountInput;

    if (food.minServing !== undefined && newAmount < food.minServing) {
      newAmount = food.minServing;
    }

    if (food.maxServing !== undefined && newAmount > food.maxServing) {
      newAmount = food.maxServing;
    }

    const updatedFood = {
      ...food,
      defaultServing: newAmount,
      minServing: Math.min(newAmount, food.minServing || newAmount),
      maxServing: Math.max(newAmount, food.maxServing || newAmount),
    };

    setLocalAmount(newAmount);
    onAmountChange(newAmount, updatedFood);
  };

  const macros = {
    protein: (food.macrosPer100g.protein * localAmount) / 100,
    carbs: (food.macrosPer100g.carbs * localAmount) / 100,
    fats: (food.macrosPer100g.fats * localAmount) / 100,
    calories: (food.macrosPer100g.calories * localAmount) / 100,
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
        <div className="py-6 text-center">
          <div className="w-8 h-8 mx-auto mb-2 border-t-2 border-blue-500 border-solid rounded-full animate-spin"></div>
          <p className="text-gray-600">Loading food data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-white border border-red-200 rounded-lg shadow-sm bg-red-50">
        <div className="py-4 text-center">
          <p className="mb-2 font-medium text-red-600">Error</p>
          <p className="text-red-500">{error}</p>
          <button
            className="px-4 py-2 mt-3 text-red-700 transition-colors bg-red-100 rounded hover:bg-red-200"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 transition-all duration-300 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md">
      <div className="flex items-center justify-between mb-3">
        <span className="font-medium text-gray-900">{food.name}</span>
        {showOptions && (
          <div className="flex items-center gap-2">
            <button
              onClick={handleDecrement}
              className="p-1 transition-colors duration-200 rounded-full hover:bg-gray-100"
              aria-label="Decrease amount"
            >
              <Minus className="w-4 h-4 text-gray-600" />
            </button>
            <input
              type="number"
              value={localAmount}
              onChange={handleAmountChange}
              className="w-16 text-center border border-gray-300 rounded-md"
              min={food.minServing || 0}
              max={food.maxServing || 1000}
              aria-label="Food amount in grams"
              onBlur={() => {
                const boundedAmount = Math.min(
                  Math.max(localAmount, food.minServing || 0),
                  food.maxServing || 1000
                );
                if (boundedAmount !== localAmount) {
                  setLocalAmount(boundedAmount);
                  onAmountChange(boundedAmount, {
                    ...food,
                    defaultServing: boundedAmount,
                  });
                }
              }}
            />
            <button
              onClick={handleIncrement}
              className="p-1 transition-colors duration-200 rounded-full hover:bg-gray-100"
              aria-label="Increase amount"
            >
              <Plus className="w-4 h-4 text-gray-600" />
            </button>
            <span className="text-sm text-gray-600">g</span>
          </div>
        )}
      </div>

      <div className="grid grid-cols-4 gap-2 text-sm">
        <div className="p-2 text-center rounded bg-blue-50">
          <div className="font-medium text-blue-800">
            {Math.round(macros.protein)}g
          </div>
          <div className="text-blue-600">Protein</div>
        </div>
        <div className="p-2 text-center rounded bg-amber-50">
          <div className="font-medium text-amber-800">
            {Math.round(macros.carbs)}g
          </div>
          <div className="text-amber-600">Carbs</div>
        </div>
        <div className="p-2 text-center rounded bg-green-50">
          <div className="font-medium text-green-800">
            {Math.round(macros.fats)}g
          </div>
          <div className="text-green-600">Fat</div>
        </div>
        <div className="p-2 text-center rounded bg-red-50">
          <div className="font-medium text-red-800">
            {Math.round(macros.calories)}
          </div>
          <div className="text-red-600">Calories</div>
        </div>
      </div>
    </div>
  );
};
