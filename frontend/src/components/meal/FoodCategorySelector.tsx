import React, { useState, useEffect } from 'react';
import { FoodItem } from '../../types/food';
import { Plus } from 'lucide-react';
import { foodItemsApi } from '../../components/meal/services/food-items.api';

interface FoodCategorySelectorProps {
  category: string;
  onFoodSelect: (food: FoodItem) => void;
  assignedFoods?: string[];
}

export const FoodCategorySelector: React.FC<FoodCategorySelectorProps> = ({
  category,
  onFoodSelect,
  assignedFoods = [],
}) => {
  const [foods, setFoods] = useState<FoodItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFoods = async () => {
      try {

        const data = await foodItemsApi.getAll();

        const categoryToFilter = category === 'carbs' ? 'carb' : category;

        // Filter by mapped category
        const filteredFoods = data.filter(food => food.category === categoryToFilter);
        setFoods(filteredFoods);

      } catch (err) {
        setError('Failed to fetch foods');
        console.error('Failed to fetch foods:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFoods();
  }, [category]);

  const handleFoodSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedFood = foods.find(food => food.id === e.target.value);
    if (selectedFood) onFoodSelect(selectedFood);
    e.target.value = '';
  };

  const categoryLabel = category === 'protein' ? 'Protein' : category === 'carbs' ? 'Carbs' : 'Food';

  return (
    <div className="relative">
      <select
        className="w-full p-2 pr-8 border border-gray-300 rounded-lg appearance-none bg-white cursor-pointer hover:border-gray-400 transition-colors duration-200"
        onChange={handleFoodSelect}
        defaultValue=""
        disabled={loading || !!error}
      >
        <option value="" disabled>Add {categoryLabel}</option>
        {foods.map(food => (
          <option
            key={food.id}
            value={food.id}
            className={assignedFoods.includes(food.id) ? 'text-gray-400' : ''}
            style={assignedFoods.includes(food.id) ? { backgroundColor: '#f3f4f6' } : {}}
          >
            {food.name} {assignedFoods.includes(food.id) ? '(Assigned)' : ''}
          </option>
        ))}
      </select>
      <div className="absolute left-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
        <Plus className="h-4 w-4 text-gray-400" />
      </div>
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  );
};

