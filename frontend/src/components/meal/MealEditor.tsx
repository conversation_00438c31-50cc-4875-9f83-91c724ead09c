import React, { useState, useEffect, Dispatch, SetStateAction } from "react";
import {
  FoodItem,
  MealCategory,
  MacroRange,
  MacroNutrients,
} from "../../types/food";
import { FoodSelector } from "./FoodSelector";
import { FoodCategorySelector } from "./FoodCategorySelector";
import { MacroDisplay } from "./MacroDisplay";
import { Utensils, Edit2, Check, X, Save } from "lucide-react";
import { useMealPlan } from "../../contexts/MealPlanContext";
import { foodItemsApi } from "../meal/services/food-items.api";
import { mealsApi, MealType } from "../meal/services/meals.api";

interface MealWithFood {
  id: string;
  name: string;
  categories: MealCategory[];
  macroRange: MacroRange;
  foodItems?: FoodItem[];
}

interface MealEditorProps {
  mealId: string;
  userId: string;
  traineeId: string;
  mealName: string;
  defaultServing: string;
  maxServing: string;
  minServing: string;
  initialCategories: MealCategory[];
  initialFoodItems?: FoodItem[];
  meals: MealWithFood[];
  onUpdate: (
    categories: MealCategory[],
    macroRange: MacroRange,
    foodItems?: FoodItem[]
  ) => void;
  onDelete: () => void;
  isNewMeal?: boolean;
  setShowOptions: Dispatch<SetStateAction<boolean>>;
  showOptions?: boolean;
  isDarkMode?: boolean;
}

const MealEditor: React.FC<MealEditorProps> = ({
  mealId,
  userId,
  traineeId,
  mealName,
  defaultServing,
  maxServing,
  minServing,
  initialCategories,
  onUpdate,
  isNewMeal = false,
  setShowOptions,
  showOptions,
  meals,
  isDarkMode = false,
}) => {
  console.log({ meals });

  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(mealName);

  const [categories, setCategories] =
    useState<MealCategory[]>(initialCategories);

  const [showSaveSuccess, setShowSaveSuccess] = useState(false);

  const [foodItems, setFoodItems] = useState<Record<string, FoodItem>>({});
  const [selectedFoods, setSelectedFoods] = useState<Record<string, FoodItem>>(
    {}
  );

  const [isLoading, setIsLoading] = useState(false);
  const [selectedFoodItemId, setSelectedFoodItemId] = useState<string | null>(
    null
  );

  const [hasChanges, setHasChanges] = useState(false);
  const [currentDefaultServing, setCurrentDefaultServing] = useState(
    defaultServing || "0"
  );

  const { updateMealPlan } = useMealPlan();

  const hasFoodsSelected =
    categories.length > 0 &&
    categories.some((category) => category.options.length > 0);

  useEffect(() => {
    const loadFoodItems = async () => {
      setIsLoading(true);
      const foodIds = categories.flatMap((category) =>
        category.options.map((option) => option.foodId)
      );

      const uniqueFoodIds = [...new Set(foodIds)];

      try {
        const foodItemsMap: Record<string, FoodItem> = {};
        await Promise.all(
          uniqueFoodIds.map(async (foodId) => {
            try {
              const food = await foodItemsApi.getOne(foodId);
              if (food) {
                foodItemsMap[foodId] = food;
              }
            } catch (error) {
              console.error(`Error fetching food item ${foodId}:`, error);
            }
          })
        );

        setFoodItems(foodItemsMap);
      } catch (error) {
        console.error("Error loading food items:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (categories.length > 0) {
      loadFoodItems();
    }
  }, [categories]);

  const calculateMacroRange = (categories: MealCategory[]): MacroRange => {
    const minMacros: MacroNutrients = {
      protein: Number.MAX_VALUE,
      carbs: Number.MAX_VALUE,
      fats: Number.MAX_VALUE,
      calories: Number.MAX_VALUE,
    };

    const maxMacros: MacroNutrients = {
      protein: 0,
      carbs: 0,
      fats: 0,
      calories: 0,
    };

    let hasItems = false;

    categories.forEach((category) => {
      category.options.forEach((option) => {
        const food = foodItems[option.foodId];
        if (!food) return;

        hasItems = true;
        const amount = option.amount;

        const proteinValue =
          food.macroRange?.protein ?? food.macrosPer100g.protein;
        const carbsValue = food.macroRange?.carbs ?? food.macrosPer100g.carbs;
        const fatsValue = food.macroRange?.fats ?? food.macrosPer100g.fats;
        const caloriesValue =
          food.macroRange?.calories ?? food.macrosPer100g.calories;

        const itemMacros = {
          protein: (proteinValue * amount) / 100,
          carbs: (carbsValue * amount) / 100,
          fats: (fatsValue * amount) / 100,
          calories: (caloriesValue * amount) / 100,
        };

        minMacros.protein = Math.min(minMacros.protein, itemMacros.protein);
        minMacros.carbs = Math.min(minMacros.carbs, itemMacros.carbs);
        minMacros.fats = Math.min(minMacros.fats, itemMacros.fats);
        minMacros.calories = Math.min(minMacros.calories, itemMacros.calories);

        maxMacros.protein = Math.max(maxMacros.protein, itemMacros.protein);
        maxMacros.carbs = Math.max(maxMacros.carbs, itemMacros.carbs);
        maxMacros.fats = Math.max(maxMacros.fats, itemMacros.fats);
        maxMacros.calories = Math.max(maxMacros.calories, itemMacros.calories);
      });
    });

    if (!hasItems) {
      minMacros.protein = 0;
      minMacros.carbs = 0;
      minMacros.fats = 0;
      minMacros.calories = 0;
    }

    return { min: minMacros, max: maxMacros };
  };

  const handleFoodAdd = async (category: string, food: FoodItem) => {
    setSelectedFoodItemId(food.id);

    const newCategories = [...categories];
    const categoryIndex = newCategories.findIndex(
      (c) => c.name === (category === "protein" ? "Protein" : "Carb")
    );

    setSelectedFoods((prev) => ({
      ...prev,
      [food.id]: food,
    }));

    const amount = food.defaultServing || 100;
    const val = Number(currentDefaultServing) + amount;
    console.log({ val });

    setCurrentDefaultServing(val.toString());

    if (categoryIndex === -1) {
      newCategories.push({
        name: category === "protein" ? "Protein" : "Carb",
        options: [
          {
            foodId: food.id,
            amount: amount,
          },
        ],
      });
    } else {
      newCategories[categoryIndex].options.push({
        foodId: food.id,
        amount: amount,
      });
    }

    setCategories(newCategories);
    setHasChanges(true);
    const macroRange = calculateMacroRange(newCategories);
    onUpdate(newCategories, macroRange);

    updateMealPlan(
      userId,
      mealId,
      newCategories,
      undefined,
      amount.toString(),
      (amount * 1.2).toString(),
      (amount * 0.8).toString()
    );
  };

  const handleFoodUpdate = (
    categoryIndex: number,
    optionIndex: number,
    amount: number
  ) => {
    const newCategories = [...categories];

    newCategories[categoryIndex].options[optionIndex].amount = amount;
    const foodId = newCategories[categoryIndex].options[optionIndex].foodId;

    const newDefaultServing = calculateDefaultServings(categories);
    console.log("handleFoodUpdate", { newDefaultServing });

    setCurrentDefaultServing(newDefaultServing);

    if (foodItems[foodId]) {
      setFoodItems((prev) => ({
        ...prev,
        [foodId]: {
          ...prev[foodId],
          defaultServing: amount,
          minServing: amount * 0.8,
          maxServing: amount * 1.2,
        },
      }));
    }

    setCategories(newCategories);
    setHasChanges(true);
    const macroRange = calculateMacroRange(newCategories);
    onUpdate(newCategories, macroRange);

    updateMealPlan(
      userId,
      mealId,
      newCategories,
      undefined,
      amount.toString(),
      (amount * 1.2).toString(),
      (amount * 0.8).toString()
    );
  };

  const handleFoodRemove = (categoryIndex: number, optionIndex: number) => {
    const newCategories = [...categories];
    const removedFoodId =
      newCategories[categoryIndex].options[optionIndex].foodId;

    setSelectedFoods((prev) => {
      const updated = { ...prev };
      delete updated[removedFoodId];
      return updated;
    });

    newCategories[categoryIndex].options.splice(optionIndex, 1);
    if (newCategories[categoryIndex].options.length === 0) {
      newCategories.splice(categoryIndex, 1);
    }

    // if (newCategories.length > 0 && newCategories[0].options.length > 0) {
    //   const amount = newCategories[0].options[0].amount;
    //   setCurrentDefaultServing(amount.toString());
    // } else {
    //   setCurrentDefaultServing("");
    // }

    setCategories(newCategories);
    setHasChanges(true);
    const macroRange = calculateMacroRange(newCategories);
    onUpdate(newCategories, macroRange);

    updateMealPlan(
      userId,
      mealId,
      newCategories,
      undefined,
      currentDefaultServing,
      newCategories.length > 0 && newCategories[0].options.length > 0
        ? (newCategories[0].options[0].amount * 1.2).toString()
        : "",
      newCategories.length > 0 && newCategories[0].options.length > 0
        ? (newCategories[0].options[0].amount * 0.8).toString()
        : ""
    );
  };

  const handleNameSave = () => {
    if (editedName.trim()) {
      setIsEditing(false);
      setHasChanges(true);
    }
  };

  const handleSave = async () => {
    if (!traineeId) {
      console.error("TraineeId is missing:", traineeId);
      return;
    }

    if (!hasFoodsSelected) {
      console.error("No food items selected");
      return;
    }

    const foodItemIds = Object.keys(selectedFoods);

    const calculatedDefaultServing =
      categories.length > 0 && categories[0].options.length > 0
        ? categories[0].options
            .reduce((sum, item) => sum + item.amount, 0)
            .toString()
        : currentDefaultServing || "100";

    try {
      const mealData: MealType = {
        name: editedName,
        description: `Meal plan for ${editedName}`,
        mealTime: "lunch",
        defaultServing: calculatedDefaultServing,
        maxServing: (parseFloat(calculatedDefaultServing) * 1.2).toString(),
        minServing: (parseFloat(calculatedDefaultServing) * 0.8).toString(),
        traineeId: traineeId,
        isFeatured: false,
        macroRange: calculateMacroRange(categories),
        categories: categories.map((category) => ({
          id: crypto.randomUUID(),
          name: category.name,
          options: category.options.map((option) => ({
            foodId: option.foodId,
            amount: option.amount,
          })),
        })),
        foodItemIds: foodItemIds,
      };

      await mealsApi.create(mealData);

      updateMealPlan(
        userId,
        mealId,
        categories,
        undefined,
        calculatedDefaultServing,
        (parseFloat(calculatedDefaultServing) * 1.2).toString(),
        (parseFloat(calculatedDefaultServing) * 0.8).toString()
      );

      setShowSaveSuccess(true);
      setHasChanges(false);
      setShowOptions(false);
      setTimeout(() => setShowSaveSuccess(false), 3000);
    } catch (error) {
      console.error("Error saving meal:", error);
    }
  };

  useEffect(() => {
    if (categories.length > 0 && categories[0].options.length > 0) {
      const newDefaultServing = calculateDefaultServings(categories);

      console.log("useEffect", { newDefaultServing });
      setCurrentDefaultServing(calculateDefaultServings(categories));
    }
  }, []);

  useEffect(() => {
    if (Object.keys(foodItems).length > 0) {
      const macroRange = calculateMacroRange(categories);
      onUpdate(categories, macroRange);
    }
  }, [foodItems]);

  useEffect(() => {
    if (
      categories.length > 0 &&
      categories.some((cat) => cat.options.length > 0)
    ) {
      for (const category of categories) {
        if (category.options.length > 0) {
          const newDefaultServing = calculateDefaultServings(categories);

          console.log("Categories useeffect", { newDefaultServing });

          if (newDefaultServing !== currentDefaultServing) {
            setCurrentDefaultServing(newDefaultServing);

            updateMealPlan(
              userId,
              mealId,
              categories,
              undefined,
              newDefaultServing,
              (parseFloat(newDefaultServing) * 1.2).toString(),
              (parseFloat(newDefaultServing) * 0.8).toString()
            );
          }
          break;
        }
      }
    }
  }, [categories]);

  const calculateDefaultServings = (categories: MealCategory[]) => {
    console.log({ categories });

    const total = categories
      .map((item) => item.options.reduce((sum, item) => sum + item.amount, 0))
      .reduce((sum, item) => sum + item, 0)
      .toString();

    return total;
  };

  const getAssignedFoods = (categoryType: "protein" | "carbs"): string[] => {
    return categories
      .filter(
        (category) =>
          (categoryType === "protein" && category.name === "Protein") ||
          (categoryType === "carbs" && category.name === "Carb")
      )
      .flatMap((category) => category.options.map((option) => option.foodId));
  };

  return (
    <div className={`overflow-hidden transition-all duration-300 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border shadow-lg rounded-xl hover:shadow-xl`}>
      {/* Meal Editor Header */}
      <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700 bg-gradient-to-r from-purple-900/50 to-gray-800' : 'border-gray-200 bg-gradient-to-r from-indigo-50 to-white'}`}>
        {/* Meal Header */}
        <div className="flex items-center justify-between">
          {/* Meal Name Block */}
          <div className="flex items-center flex-1 gap-3">
            <div className={`p-2 transition-transform duration-300 transform ${isDarkMode ? 'bg-purple-900' : 'bg-indigo-100'} rounded-lg group-hover:rotate-12`}>
              <Utensils className={`w-5 h-5 ${isDarkMode ? 'text-purple-300' : 'text-indigo-600'}`} />
            </div>

            {isEditing ? (
              <div className="flex items-center flex-1 gap-2">
                <input
                  type="text"
                  value={editedName}
                  onChange={(e) => setEditedName(e.target.value)}
                  className={`flex-1 px-2 py-1 border ${isDarkMode ? 'bg-gray-700 border-gray-600 text-gray-200' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 ${isDarkMode ? 'focus:ring-purple-500' : 'focus:ring-indigo-500'}`}
                  autoFocus
                />
                <button
                  onClick={handleNameSave}
                  className={`p-1 transition-colors duration-200 rounded-full ${isDarkMode ? 'hover:bg-green-900' : 'hover:bg-green-100'}`}
                >
                  <Check className={`w-4 h-4 ${isDarkMode ? 'text-green-400' : 'text-green-600'}`} />
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setEditedName(mealName);
                  }}
                  className={`p-1 transition-colors duration-200 rounded-full ${isDarkMode ? 'hover:bg-red-900' : 'hover:bg-red-100'}`}
                >
                  <X className={`w-4 h-4 ${isDarkMode ? 'text-red-400' : 'text-red-600'}`} />
                </button>
              </div>
            ) : (
              <div className="flex items-center flex-1 gap-2">
                <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                  {editedName}
                </h3>
                <button
                  onClick={() => setIsEditing(true)}
                  className={`p-1 transition-colors duration-200 rounded-full ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
                >
                  <Edit2 className={`w-4 h-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-400'}`} />
                </button>
              </div>
            )}
          </div>
          {/* Meal Name Block */}

          {/* ----------------------------------------------------------------------------------------------------- */}

          {/* Save Changes Button */}
          {hasChanges && hasFoodsSelected && (
            <div className="flex items-center gap-2">
              <button
                onClick={handleSave}
                className={`flex items-center gap-2 px-4 py-2 text-white transition-colors duration-200 ${isDarkMode ? 'bg-purple-700 hover:bg-purple-600' : 'bg-green-600 hover:bg-green-700'} rounded-lg`}
              >
                <Save className="w-5 h-5" />
                <span>Save Changes</span>
              </button>
            </div>
          )}
          {/* Save Changes Button */}
        </div>

        {/* ----------------------------------------------------------------------------------------------------- */}

        {/* save changes alert */}
        {showSaveSuccess && (
          <div className={`p-2 mt-2 text-sm ${isDarkMode ? 'text-green-400 bg-green-900/30 border border-green-700' : 'text-green-600 bg-green-50'} rounded-lg`}>
            Changes saved successfully
          </div>
        )}
        {/* save changes alert */}

        {/* ----------------------------------------------------------------------------------------------------- */}

        {/* total servings block */}

        {currentDefaultServing && (
          <div className={`mt-2 text-sm ${isDarkMode ? 'text-purple-300' : 'text-indigo-600'}`}>
            Serving: {currentDefaultServing}g
          </div>
        )}
        {/* total servings block */}
      </div>
      {/* Meal Editor Header */}

      {/* ------------------------------------------------------------------- */}

      {/* Meal Editor Content */}
      {isExpanded && (
        <div className="p-4 space-y-6">
          {/* Loader */}
          {isLoading && (
            <div className="py-4 text-center">
              <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>Loading food data...</p>
            </div>
          )}
          {/* Loader */}

          {/* ------------------------------------------------------------------- */}

          {/* Food Selectors */}
          {isNewMeal && showOptions && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className={`mb-2 text-sm font-medium text-right ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Proteins
                </h4>

                <FoodCategorySelector
                  category="protein"
                  onFoodSelect={(food) => handleFoodAdd("protein", food)}
                  assignedFoods={getAssignedFoods("protein")}
                />
              </div>

              <div>
                <h4 className={`mb-2 text-sm font-medium text-right ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Carbs
                </h4>
                <FoodCategorySelector
                  category="carbs"
                  onFoodSelect={(food) => handleFoodAdd("carbs", food)}
                  assignedFoods={getAssignedFoods("carbs")}
                />
              </div>
            </div>
          )}
          {/* Food Selectors */}

          {/* ------------------------------------------------------------------- */}

          <div className="space-y-4">
            {categories.map((category, categoryIndex) => (
              <div key={categoryIndex} className="space-y-2">
                <h4 className={`text-sm font-medium text-right ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {category.name === "Protein" ? "Proteins" : "Carb"}
                </h4>

                {category.options.map((option, optionIndex) => {
                  const food = foodItems[option.foodId];
                  if (!food)
                    return (
                      <div
                        key={`${categoryIndex}-${optionIndex}-loading`}
                        className={`p-3 border rounded-lg animate-pulse ${isDarkMode ? 'border-gray-700 bg-gray-700' : 'border-gray-200 bg-gray-50'}`}
                      >
                        <p className={`${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>Loading food data...</p>
                      </div>
                    );

                  return (
                    <FoodSelector
                      key={`${categoryIndex}-${optionIndex}`}
                      food={food}
                      amount={option.amount}
                      onAmountChange={(amount) =>
                        handleFoodUpdate(categoryIndex, optionIndex, amount)
                      }
                      onRemove={() =>
                        handleFoodRemove(categoryIndex, optionIndex)
                      }
                      isNewMeal={isNewMeal}
                      showOptions={showOptions}
                    />
                  );
                })}
              </div>
            ))}
          </div>

          {/* ------------------------------------------------------------------- */}

          {/* Meal Macro Range Display */}
          <MacroDisplay
            macroRange={calculateMacroRange(categories)}
            title="Meal Macro Range"
            isDarkMode={isDarkMode}
          />
          {/* Meal Macro Range Display */}
        </div>
      )}
      {/* Meal Editor Content */}
    </div>
  );
};

export default MealEditor;
