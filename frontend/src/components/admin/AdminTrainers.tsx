import React, { useEffect, useState } from "react";
import {
  UserPlus,
  Search,
  Filter,
  MoreVertical,
  Edit2,
  Trash2,
  X,
  <PERSON>ertCircle,
  Phone,
} from "lucide-react";
import { TrainerAnalyticsDashboard } from "./TraninerAnalyticsDashboard";
import { useAdmin } from "../../contexts/AdminContext";
import { calculateTrainerAnalytics } from "../../utils/trainerAnalytics";
import { adminApi } from "../../service/admin.api";
import defaultProfile from "../../default-profile-image.png";

type TrainerRole = "senior_trainer" | "junior_trainer" | "nutrition_specialist";

interface Trainer {
  id: string;
  name: string;
  email: string;
  mobileNumber?: string;
  trainerRole: TrainerRole;
  status: "active" | "inactive" | "pending";
  avatar: string;
  specializations?: string[];
  yearsOfExperience?: number;
  certifications?: string[];
  bio?: string;
  hourlyRate?: number;
}

interface AdminTrainersProps {
  trainerTabClicked: boolean;
  onTrainerSelect: (trainerId: string) => void;
}

export const AdminTrainers: React.FC<AdminTrainersProps> = ({
  trainerTabClicked,
}) => {
  const [showNewTrainerForm, setShowNewTrainerForm] = useState(false);
  const [showEditTrainerForm, setShowEditTrainerForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedTrainerId, setSelectedTrainerId] = useState<string | null>(
    null
  );
  const [trainers, setTrainers] = useState<Trainer[]>([]);
  const [newTrainer, setNewTrainer] = useState<
    Omit<Trainer, "id" | "avatar" | "status">
  >({
    name: "",
    email: "",
    mobileNumber: "",
    trainerRole: "senior_trainer",
  });
  const [countryCode, setCountryCode] = useState("+972"); // Default to Israel country code
  const [editTrainer, setEditTrainer] = useState<Trainer | null>(null);
  const [trainerToDelete, setTrainerToDelete] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // New states for search functionality
  const [searchQuery, setSearchQuery] = useState("");
  const [activeFilters, setActiveFilters] = useState<{
    status?: string;
    role?: string;
  }>({});
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [totalTrainers, setTotalTrainers] = useState(0);
  const [showFilters, setShowFilters] = useState(false);

  const { users, workoutLogs } = useAdmin();

  async function getTrainers() {
    try {
      setIsLoading(true);
      setSearchError(null);
      const response = await adminApi.getAllTrainers();

      // Set trainers state with the response data
      if (response.data && Array.isArray(response.data)) {
        // Map response to ensure avatar is set for UI display
        const formattedTrainers = response.data.map((trainer: any) => ({
          ...trainer,
          // Use placeholder avatar if trainer.avatar is null
          avatar: trainer.avatar || defaultProfile,
          // Ensure name is not null for display purposes
          name: trainer.name || trainer.email.split("@")[0],
        }));
        setTrainers(formattedTrainers);
        setTotalTrainers(formattedTrainers.length);
      }
    } catch (error) {
      setSearchError("Failed to load trainers. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  const handleSearch = async () => {
    try {
      setIsSearching(true);
      setSearchError(null);

      const searchParams = {
        search: searchQuery || undefined,
        email: searchQuery.includes("@") ? searchQuery : undefined,
        trainerRole: activeFilters.role,
        status: activeFilters.status,
        limit: 50, // You can adjust this or make it configurable
        offset: 0,
      };

      const response = await adminApi.searchTrainers(searchParams);

      if (response.success && Array.isArray(response.data.data)) {
        const formattedTrainers = response.data.data.map((trainer: any) => ({
          ...trainer,
          avatar: trainer.avatar || defaultProfile,
          name: trainer.name || trainer.email.split("@")[0],
        }));
        setTrainers(formattedTrainers);
        setTotalTrainers(response.total || formattedTrainers.length);
      } else {
        setSearchError("Unable to retrieve search results.");
      }
    } catch (error) {
      console.error("Error searching trainers:", error);
      setSearchError("Failed to search trainers. Please try again.");
    } finally {
      setIsSearching(false);
    }
  };

  // Debounce search for better UX
  useEffect(() => {
    if (searchQuery.length === 0 && Object.keys(activeFilters).length === 0) {
      getTrainers();
      return;
    }

    const searchTimer = setTimeout(() => {
      handleSearch();
    }, 500);

    return () => clearTimeout(searchTimer);
  }, [searchQuery, activeFilters]);

  // Load initial trainers data
  useEffect(() => {
    getTrainers();
  }, [trainerTabClicked]);

  const handleTrainerClick = (trainerId: string) => {
    setSelectedTrainerId(trainerId);
    // onTrainerSelect(trainerId)
  };

  const handleAddTrainer = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      setIsLoading(true);
      const newId = `${Date.now()}`;

      // Combine country code with mobile number if mobile number is provided
      const fullMobileNumber = newTrainer.mobileNumber
        ? `${countryCode}${
            newTrainer.mobileNumber.startsWith("0")
              ? newTrainer.mobileNumber.substring(1)
              : newTrainer.mobileNumber
          }`
        : "";

      const trainerData: Trainer = {
        id: newId,
        name: newTrainer.name,
        email: newTrainer.email,
        mobileNumber: fullMobileNumber,
        trainerRole: newTrainer.trainerRole,
        avatar: "/api/placeholder/40/40",
        status: "active",
      };

      await adminApi.createTrainer(trainerData);

      getTrainers();

      setNewTrainer({
        name: "",
        email: "",
        mobileNumber: "",
        trainerRole: "senior_trainer",
      });
      setCountryCode("+972"); // Reset country code
      setShowNewTrainerForm(false);
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditClick = (e: React.MouseEvent, trainer: Trainer) => {
    e.stopPropagation();
    setEditTrainer(trainer);
    setShowEditTrainerForm(true);
  };

  const handleDeleteClick = (e: React.MouseEvent, trainerId: string) => {
    e.stopPropagation();
    setTrainerToDelete(trainerId);
    setShowDeleteConfirm(true);
  };

  const handleUpdateTrainer = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editTrainer) return;

    try {
      setIsLoading(true);

      // Get the current mobile number and country code from the edit form
      const mobileNumberInput = document.getElementById(
        "edit-mobile-number"
      ) as HTMLInputElement;
      const countryCodeSelect = document.getElementById(
        "edit-country-code"
      ) as HTMLSelectElement;

      // Combine country code with mobile number if mobile number is provided
      let fullMobileNumber = editTrainer.mobileNumber || "";

      if (mobileNumberInput && countryCodeSelect && mobileNumberInput.value) {
        const mobileValue = mobileNumberInput.value;
        const countryValue = countryCodeSelect.value;

        fullMobileNumber = `${countryValue}${
          mobileValue.startsWith("0") ? mobileValue.substring(1) : mobileValue
        }`;
      }

      const updateData = {
        name: editTrainer.name,
        email: editTrainer.email,
        mobileNumber: fullMobileNumber,
        trainerRole: editTrainer.trainerRole,
        status: editTrainer.status,
      };

      const response = await adminApi.updateTrainer(editTrainer.id, updateData);

      // Check if response data exists and has the expected structure
      if (response && response.success && response.data) {
        // Update the trainer in the local state
        setTrainers((prevTrainers) =>
          prevTrainers.map((trainer) =>
            trainer.id === response.data.id
              ? {
                  ...trainer,
                  ...response.data,
                  // Ensure avatar is maintained or fallback to default
                  avatar:
                    response.data.avatar || trainer.avatar || defaultProfile,
                }
              : trainer
          )
        );
      } else {
        // Fallback to refreshing the entire list if response is unexpected
        getTrainers();
      }

      setShowEditTrainerForm(false);
      setEditTrainer(null);
    } catch (error) {
      console.error("Error updating trainer:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteTrainer = async () => {
    if (!trainerToDelete) return;

    try {
      setIsLoading(true);
      await adminApi.deleteTrainer(trainerToDelete);

      // Remove trainer from local state
      setTrainers(trainers.filter((trainer) => trainer.id !== trainerToDelete));
      setShowDeleteConfirm(false);
      setTrainerToDelete(null);
    } catch (error) {
      console.error("Error deleting trainer:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handler to clear search
  const clearSearch = () => {
    setSearchQuery("");
    setActiveFilters({});
    getTrainers();
  };

  // Handler to remove a specific filter
  const removeFilter = (filterKey: string) => {
    setActiveFilters((prev) => {
      const newFilters = { ...prev };
      delete newFilters[filterKey as keyof typeof newFilters];
      return newFilters;
    });
  };

  if (selectedTrainerId) {
    return (
      <TrainerAnalyticsDashboard
        trainerId={selectedTrainerId}
        onBack={() => setSelectedTrainerId(null)}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-800">
          Trainers Management
        </h2>
        <button
          onClick={() => setShowNewTrainerForm(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
        >
          <UserPlus className="h-5 w-5" />
          <span>Add New Trainer</span>
        </button>
      </div>

      <div className="flex gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search trainers by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-10 py-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          />
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`px-4 py-2 border-2 rounded-lg transition-colors duration-200 flex items-center gap-2 ${
            Object.keys(activeFilters).length > 0
              ? "bg-blue-50 border-blue-200 text-blue-700"
              : "bg-white border-gray-200 hover:bg-gray-50"
          }`}
        >
          <Filter className="h-5 w-5" />
          <span>
            Filters{" "}
            {Object.keys(activeFilters).length > 0 &&
              `(${Object.keys(activeFilters).length})`}
          </span>
        </button>
      </div>

      {/* Active filters display */}
      {Object.keys(activeFilters).length > 0 && (
        <div className="flex flex-wrap gap-2">
          {activeFilters.status && (
            <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-sm">
              <span>Status: {activeFilters.status}</span>
              <button
                onClick={() => removeFilter("status")}
                className="hover:bg-blue-100 rounded-full p-1"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
          {activeFilters.role && (
            <div className="flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-sm">
              <span>
                Role:{" "}
                {activeFilters.role === "senior_trainer"
                  ? "Senior Trainer"
                  : activeFilters.role === "junior_trainer"
                  ? "Junior Trainer"
                  : "Nutrition Specialist"}
              </span>
              <button
                onClick={() => removeFilter("role")}
                className="hover:bg-blue-100 rounded-full p-1"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          )}
          <button
            onClick={clearSearch}
            className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
          >
            Clear all
          </button>
        </div>
      )}

      {/* Filter dropdown */}
      {showFilters && (
        <div className="bg-white p-4 rounded-lg border-2 border-gray-200 shadow-md">
          <h4 className="font-medium text-gray-700 mb-3">Filter Trainers</h4>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={activeFilters.status || ""}
                onChange={(e) =>
                  setActiveFilters({
                    ...activeFilters,
                    status: e.target.value || undefined,
                  })
                }
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="pending">Pending</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Role
              </label>
              <select
                value={activeFilters.role || ""}
                onChange={(e) =>
                  setActiveFilters({
                    ...activeFilters,
                    role: e.target.value || undefined,
                  })
                }
                className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500"
              >
                <option value="">All Roles</option>
                <option value="senior_trainer">Senior Trainer</option>
                <option value="junior_trainer">Junior Trainer</option>
                <option value="nutrition_specialist">
                  Nutrition Specialist
                </option>
              </select>
            </div>
          </div>
        </div>
      )}

      {searchError && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          <span>{searchError}</span>
        </div>
      )}

      {isLoading || isSearching ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : trainers.length === 0 ? (
        <div className="bg-white rounded-xl shadow-lg overflow-hidden border-2 border-gray-200 p-8 text-center">
          <p className="text-gray-500">
            No trainers found. Try adjusting your search or filters.
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-lg overflow-hidden border-2 border-gray-200">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trainer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trainees
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {trainers.map((trainer) => {
                const analytics = calculateTrainerAnalytics(
                  trainer,
                  users,
                  workoutLogs
                );
                return (
                  <tr
                    key={trainer.id}
                    className="hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleTrainerClick(trainer.id)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <img
                          src={trainer.avatar || "/api/placeholder/40/40"}
                          alt={trainer.name || "Trainer"}
                          className="h-10 w-10 rounded-full object-cover"
                        />
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {trainer.name || "Unknown"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {trainer.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {trainer.trainerRole === "senior_trainer"
                        ? "Senior Trainer"
                        : trainer.trainerRole === "junior_trainer"
                        ? "Junior Trainer"
                        : "Nutrition Specialist"}
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          trainer.status === "active"
                            ? "bg-green-100 text-green-800"
                            : trainer.status === "inactive"
                            ? "bg-red-100 text-red-800"
                            : "bg-yellow-100 text-yellow-800"
                        }`}
                      >
                        {trainer.status.charAt(0).toUpperCase() +
                          trainer.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {analytics.activeClients} active
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-2">
                        <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-blue-600 rounded-full"
                            style={{ width: `${analytics.avgClientProgress}%` }}
                          />
                        </div>
                        <span className="text-sm text-gray-600">
                          {Math.round(analytics.avgClientProgress)}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-right text-sm font-medium">
                      <div className="flex items-center justify-end gap-2">
                        <button
                          onClick={(e) => handleEditClick(e, trainer)}
                          className="p-1 hover:bg-gray-100 rounded-full"
                          title="Edit trainer"
                        >
                          <Edit2 className="h-4 w-4 text-gray-500" />
                        </button>
                        <button
                          onClick={(e) => handleDeleteClick(e, trainer.id)}
                          className="p-1 hover:bg-gray-100 rounded-full"
                          title="Delete trainer"
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </button>
                        <button
                          onClick={(e) => e.stopPropagation()}
                          className="p-1 hover:bg-gray-100 rounded-full"
                        >
                          <MoreVertical className="h-4 w-4 text-gray-500" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="px-6 py-3 bg-gray-50 text-sm text-gray-500">
            Showing {trainers.length} of {totalTrainers} trainers
          </div>
        </div>
      )}

      {/* Add New Trainer Modal */}
      {showNewTrainerForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              Add New Trainer
            </h3>
            <form className="space-y-4" onSubmit={handleAddTrainer}>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    value={newTrainer.name}
                    onChange={(e) =>
                      setNewTrainer({ ...newTrainer, name: e.target.value })
                    }
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={newTrainer.email}
                    onChange={(e) =>
                      setNewTrainer({ ...newTrainer, email: e.target.value })
                    }
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    required
                  />
                </div>
              </div>

              <div className="rounded-lg">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                      <Phone className="h-4 w-4 text-gray-500 mr-2" />
                      Mobile Number <span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="flex">
                      {/* Country Code Selector */}
                      <select
                        value={countryCode}
                        onChange={(e) => setCountryCode(e.target.value)}
                        className="w-24 h-10 px-2 border-2 border-gray-200 rounded-l-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 appearance-none"
                      >
                        <option value="+972">+972 🇮🇱</option>
                        <option value="+1">+1 🇺🇸</option>
                        <option value="+44">+44 🇬🇧</option>
                        <option value="+33">+33 🇫🇷</option>
                        <option value="+49">+49 🇩🇪</option>
                        <option value="+7">+7 🇷🇺</option>
                        <option value="+86">+86 🇨🇳</option>
                        <option value="+91">+91 🇮🇳</option>
                      </select>
                      {/* Phone Number Input */}
                      <input
                        type="tel"
                        required
                        value={newTrainer.mobileNumber}
                        onChange={(e) =>
                          setNewTrainer({
                            ...newTrainer,
                            mobileNumber: e.target.value,
                          })
                        }
                        className="flex-1 h-10 px-3 border-2 border-gray-200 rounded-r-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        placeholder="50-1234567"
                        autoComplete="tel"
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Enter a WhatsApp-enabled number to receive updates and
                      stay connected with their fitness journey.
                    </p>
                  </div>
                  {/* Empty div to maintain grid layout */}
                  <div></div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <select
                  value={newTrainer.trainerRole}
                  onChange={(e) =>
                    setNewTrainer({
                      ...newTrainer,
                      trainerRole: e.target.value as TrainerRole,
                    })
                  }
                  className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                >
                  <option value="senior_trainer">Senior Trainer</option>
                  <option value="junior_trainer">Junior Trainer</option>
                  <option value="nutrition_specialist">
                    Nutrition Specialist
                  </option>
                </select>
              </div>
              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  onClick={() => setShowNewTrainerForm(false)}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  disabled={isLoading}
                >
                  {isLoading ? "Adding..." : "Add Trainer"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Trainer Modal */}
      {showEditTrainerForm && editTrainer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">
              Edit Trainer
            </h3>
            <form className="space-y-4" onSubmit={handleUpdateTrainer}>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    value={editTrainer.name}
                    onChange={(e) =>
                      setEditTrainer({ ...editTrainer, name: e.target.value })
                    }
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={editTrainer.email}
                    onChange={(e) =>
                      setEditTrainer({ ...editTrainer, email: e.target.value })
                    }
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    required
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mobile Number (optional)
                </label>
                <div className="flex">
                  {/* Country Code Selector */}
                  <div className="relative w-24">
                    <select
                      id="edit-country-code"
                      defaultValue="+972"
                      className="w-full p-2 border-2 border-gray-200 rounded-l-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    >
                      <option value="+972">+972 🇮🇱</option>
                      <option value="+1">+1 🇺🇸</option>
                      <option value="+44">+44 🇬🇧</option>
                      <option value="+33">+33 🇫🇷</option>
                      <option value="+49">+49 🇩🇪</option>
                      <option value="+7">+7 🇷🇺</option>
                      <option value="+86">+86 🇨🇳</option>
                      <option value="+91">+91 🇮🇳</option>
                    </select>
                  </div>
                  {/* Phone Number Input */}
                  <div className="relative flex-1">
                    <input
                      id="edit-mobile-number"
                      type="tel"
                      defaultValue={
                        editTrainer.mobileNumber
                          ? editTrainer.mobileNumber.replace(/^\+\d+/, "")
                          : ""
                      }
                      className="w-full p-2 border-2 border-gray-200 rounded-r-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      placeholder="50-1234567"
                    />
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Enter a WhatsApp-enabled number to receive updates and stay
                  connected.
                </p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Role
                  </label>
                  <select
                    value={editTrainer.trainerRole}
                    onChange={(e) =>
                      setEditTrainer({
                        ...editTrainer,
                        trainerRole: e.target.value as TrainerRole,
                      })
                    }
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  >
                    <option value="senior_trainer">Senior Trainer</option>
                    <option value="junior_trainer">Junior Trainer</option>
                    <option value="nutrition_specialist">
                      Nutrition Specialist
                    </option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={editTrainer.status}
                    onChange={(e) =>
                      setEditTrainer({
                        ...editTrainer,
                        status: e.target.value as
                          | "active"
                          | "inactive"
                          | "pending",
                      })
                    }
                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="pending">Pending</option>
                  </select>
                </div>
              </div>
              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditTrainerForm(false);
                    setEditTrainer(null);
                  }}
                  className="px-4 py-2 text-gray-700 hover:text-gray-900"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
                  disabled={isLoading}
                >
                  {isLoading ? "Updating..." : "Update Trainer"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-gray-800 mb-2">
              Confirm Delete
            </h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this trainer? This action cannot
              be undone.
            </p>
            <div className="flex justify-end gap-4">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 text-gray-700 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteTrainer}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200"
                disabled={isLoading}
              >
                {isLoading ? "Deleting..." : "Delete Trainer"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
