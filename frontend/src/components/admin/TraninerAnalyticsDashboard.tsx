import React, { useEffect, useState } from 'react';
import {
    ChevronLeft, 
    Users, 
    Target, 
    Clock, 
    Award, 
    TrendingUp, 
    AlertTriangle, 
    CheckCircle, 
    XCircle
} from 'lucide-react';
import { useAdmin } from '../../contexts/AdminContext';
import { calculateTrainerAnalytics } from '../../utils/trainerAnalytics';
import { adminApi } from '../../service/admin.api';

// Extended trainer interface to match API response
interface TrainerDetailResponse {
    id: string;
    name: string;
    email: string;
    avatar: string | null;
    trainerRole: 'senior_trainer' | 'junior_trainer' | 'nutrition_specialist';
    status: 'active' | 'inactive' | 'pending';
    specializations: string[];
    yearsOfExperience: number;
    certifications: string;
    bio: string;
    hourlyRate: number;
}

interface TrainerAnalyticsDashboardProps {
    trainerId: string;
    onBack: () => void;
}   

export const TrainerAnalyticsDashboard: React.FC<TrainerAnalyticsDashboardProps> = ({
    trainerId, onBack
}) => {
    const { users, workoutLogs } = useAdmin();
    const [trainer, setTrainer] = useState<TrainerDetailResponse | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        async function fetchTrainer(id: string) {
            try {
                setLoading(true);
                const response = await adminApi.getTrainerById(id);
                setTrainer(response.data);
                setError(null);
            } catch (err) {
                console.error("Error fetching trainer:", err);
                setError("Failed to load trainer data");
                setTrainer(null);
            } finally {
                setLoading(false);
            }
        }

        fetchTrainer(trainerId);
    }, [trainerId]);
    
    if (loading) {
        return (
            <div className="flex items-center gap-4">
                <button
                    onClick={onBack}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                >
                    <ChevronLeft className="h-6 w-6 text-gray-600" />
                </button>
                <div>Loading trainer data...</div>
            </div>
        );
    }

    if (error || !trainer) {
        return (
            <div>
                <div className="flex items-center gap-4">
                    <button
                        onClick={onBack}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    >
                        <ChevronLeft className="h-6 w-6 text-gray-600" />
                    </button>
                    <div>{error || "Trainer not found"}</div>
                </div>
            </div>
        );
    }
    
    // Make sure we have the required fields with fallbacks
    const trainerStatus = trainer.status || 'pending';
    const trainerRole = trainer.trainerRole || 'junior_trainer';
    
    const trainerForAnalytics = {
        id: trainer.id,
        name: trainer.name,
        email: trainer.email,
        avatar: trainer.avatar || '',
        trainerRole: trainerRole,
        status: trainerStatus
    };
    
    const analytics = calculateTrainerAnalytics(trainerForAnalytics, users, workoutLogs);
    
    // Helper function to safely get status display
    const getStatusDisplay = (status: string): string => {
        return status ? status.charAt(0).toUpperCase() + status.slice(1) : 'Unknown';
    };
    
    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between bg-white rounded-xl p-6 shadow-lg border-2 border-blue-200">
                <div className="flex items-center gap-4">
                    <button
                        onClick={onBack}
                        className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                    >
                        <ChevronLeft className="h-6 w-6 text-gray-600" />
                    </button>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-800">{trainer.name || 'Unnamed Trainer'}</h1>
                        <p className="text-gray-600">
                            {trainerRole === 'senior_trainer' ? 'Senior Trainer' :
                             trainerRole === 'junior_trainer' ? 'Junior Trainer' :
                             'Nutrition Specialist'}
                        </p>
                    </div>
                </div>
                <div className="flex items-center gap-3">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        trainerStatus === 'active' ? 'bg-green-100 text-green-800' :
                        trainerStatus === 'inactive' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                    }`}>
                        {getStatusDisplay(trainerStatus)}
                    </span>
                </div>
            </div>

            {/* Trainer Details */}
            <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Trainer Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-gray-600">Email</h4>
                            <p className="text-gray-800">{trainer.email || 'No email provided'}</p>
                        </div>
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-gray-600">Experience</h4>
                            <p className="text-gray-800">{trainer.yearsOfExperience || 0} years</p>
                        </div>
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-gray-600">Hourly Rate</h4>
                            <p className="text-gray-800">${trainer.hourlyRate || 0}</p>
                        </div>
                    </div>
                    <div>
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-gray-600">Specializations</h4>
                            {trainer.specializations && Array.isArray(trainer.specializations) && trainer.specializations.length > 0 ? (
                                <div className="flex flex-wrap gap-2 mt-1">
                                    {trainer.specializations.map((spec, index) => (
                                        <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">
                                            {spec}
                                        </span>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500">No specializations listed</p>
                            )}
                        </div>
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-gray-600">Certifications</h4>
                            <p className="text-gray-800">{trainer.certifications || 'None'}</p>
                        </div>
                        <div>
                            <h4 className="text-sm font-medium text-gray-600">Bio</h4>
                            <p className="text-gray-800">{trainer.bio || 'No bio available'}</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-blue-200">
                    <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 bg-blue-100 rounded-lg">
                            <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-600">Active Clients</h3>
                    </div>
                    <div className="mt-2">
                        <div className="text-2xl font-bold text-gray-900">{analytics?.activeClients || 0}</div>
                        <div className="text-sm text-gray-500">out of {analytics?.totalClients || 0} total</div>
                    </div>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-purple-200">
                    <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 bg-purple-100 rounded-lg">
                            <Target className="h-5 w-5 text-purple-600" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-600">Client Progress</h3>
                    </div>
                    <div className="mt-2">
                        <div className="text-2xl font-bold text-gray-900">
                            {Math.round(analytics?.avgClientProgress || 0)}%
                        </div>
                        <div className="text-sm text-gray-500">average progress rate</div>
                    </div>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-emerald-200">
                    <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 bg-emerald-100 rounded-lg">
                            <Clock className="h-5 w-5 text-emerald-600" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-600">Client Retention</h3>
                    </div>
                    <div className="mt-2">
                        <div className="text-2xl font-bold text-gray-900">
                            {Math.round(analytics?.clientRetention || 0)}%
                        </div>
                        <div className="text-sm text-gray-500">retention rate</div>
                    </div>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-yellow-200">
                    <div className="flex items-center gap-3 mb-2">
                        <div className="p-2 bg-yellow-100 rounded-lg">
                            <Award className="h-5 w-5 text-yellow-600" />
                        </div>
                        <h3 className="text-sm font-medium text-gray-600">Goals Achieved</h3>
                    </div>
                    <div className="mt-2">
                        <div className="text-2xl font-bold text-gray-900">
                            {analytics?.weeklyMetrics?.goalsAchieved || 0}
                        </div>
                        <div className="text-sm text-gray-500">this week</div>
                    </div>
                </div>
            </div>

            {/* Weekly Performance */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Weekly Overview</h3>
                    <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div className="flex items-center gap-3">
                                <Users className="h-5 w-5 text-green-600" />
                                <span className="font-medium text-green-900">Active Clients</span>
                            </div>
                            <span className="text-lg font-semibold text-green-900">{analytics?.activeClients || 0}</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div className="flex items-center gap-3">
                                <CheckCircle className="h-5 w-5 text-blue-600" />
                                <span className="font-medium text-blue-900">Completed Sessions</span>
                            </div>
                            <span className="text-lg font-semibold text-blue-900">
                                {analytics?.weeklyMetrics?.completedSessions || 0}
                            </span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                            <div className="flex items-center gap-3">
                                <XCircle className="h-5 w-5 text-red-600" />
                                <span className="font-medium text-red-900">Missed Sessions</span>
                            </div>
                            <span className="text-lg font-semibold text-red-900">
                                {analytics?.weeklyMetrics?.missedSessions || 0}
                            </span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                            <div className="flex items-center gap-3">
                                <Target className="h-5 w-5 text-purple-600" />
                                <span className="font-medium text-purple-900">Goals Achieved</span>
                            </div>
                            <span className="text-lg font-semibold text-purple-900">
                                {analytics?.weeklyMetrics?.goalsAchieved || 0}
                            </span>
                        </div>
                    </div>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Client Progress Overview</h3>
                    {analytics?.clientProgress && analytics.clientProgress.length > 0 ? (
                        <div className="space-y-4">
                            {analytics.clientProgress.map((client: any, index: number) => (
                                <div key={index} className="bg-gray-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between mb-2">
                                        <div className="flex items-center gap-3">
                                            <div className={`p-2 rounded-lg ${
                                                client.status === 'ahead' ? 'bg-green-100' :
                                                client.status === 'on_track' ? 'bg-blue-100' :
                                                'bg-yellow-100'
                                            }`}>
                                                {client.status === 'ahead' ? (
                                                    <TrendingUp className="h-4 w-4 text-green-600" />
                                                ) : client.status === 'on_track' ? (
                                                    <Target className="h-4 w-4 text-blue-600" />
                                                ) : (
                                                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                                                )}
                                            </div>
                                            <div>
                                                <div className="font-medium text-gray-900">{client.name || 'Unnamed Client'}</div>
                                                <div className="text-sm text-gray-500">
                                                    {client.goal === 'fat_loss' ? 'Fat Loss' : 
                                                     client.goal === 'muscle_gain' ? 'Muscle Gain' : 'General Fitness'}
                                                </div>
                                            </div>
                                        </div>
                                        <span className="text-sm font-medium text-gray-900">
                                            {Math.round(client.progress || 0)}%
                                        </span>
                                    </div>
                                    <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                                        <div
                                            className={`h-full rounded-full ${
                                                client.status === 'ahead' ? 'bg-green-600' :
                                                client.status === 'on_track' ? 'bg-blue-600' :
                                                'bg-yellow-600'
                                            }`}
                                            style={{ width: `${client.progress || 0}%` }}
                                        />
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500">No client progress data available</p>
                    )}
                </div>
            </div>

            {/* Performance Trends */}
            {analytics?.performanceMetrics && (
                <div className="bg-white rounded-xl p-6 shadow-lg border-2 border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Performance Trends</h3>
                    <div className="grid grid-cols-2 gap-6">
                        <div>
                            <h4 className="text-sm font-medium text-gray-600 mb-2">Client Retention Rate</h4>
                            {analytics.performanceMetrics.clientRetention && 
                             analytics.performanceMetrics.clientRetention.length > 0 ? (
                                <div className="h-40 flex items-end gap-2">
                                    {analytics.performanceMetrics.clientRetention.map((rate: number, index: number) => (
                                        <div
                                            key={index}
                                            className="flex-1 bg-blue-600 rounded-t-lg hover:bg-blue-700 transition-colors duration-200"
                                            style={{ height: `${rate || 0}%` }}
                                        >
                                            <div className="text-xs text-white text-center mt-2 transform -rotate-90">
                                                {Math.round(rate || 0)}%
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500">No retention data available</p>
                            )}
                            <div className="flex justify-between mt-2 text-xs text-gray-500">
                                <span>Mon</span>
                                <span>Tue</span>
                                <span>Wed</span>
                                <span>Thu</span>
                                <span>Fri</span>
                                <span>Sat</span>
                                <span>Sun</span>
                            </div>
                        </div>
                        <div>
                            <h4 className="text-sm font-medium text-gray-600 mb-2">Client Progress Rate</h4>
                            {(analytics.performanceMetrics as any).clientProgress && 
                             (analytics.performanceMetrics as any).clientProgress.length > 0 ? (
                                <div className="h-40 flex items-end gap-2">
                                    {(analytics.performanceMetrics as any).clientProgress.map((rate: number, index: number) => (
                                        <div
                                            key={index}
                                            className="flex-1 bg-green-600 rounded-t-lg hover:bg-green-700 transition-colors duration-200"
                                            style={{ height: `${rate || 0}%` }}
                                        >
                                            <div className="text-xs text-white text-center mt-2 transform -rotate-90">
                                                {Math.round(rate || 0)}%
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <p className="text-gray-500">No progress data available</p>
                            )}
                            <div className="flex justify-between mt-2 text-xs text-gray-500">
                                <span>Mon</span>
                                <span>Tue</span>
                                <span>Wed</span>
                                <span>Thu</span>
                                <span>Fri</span>
                                <span>Sat</span>
                                <span>Sun</span>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};