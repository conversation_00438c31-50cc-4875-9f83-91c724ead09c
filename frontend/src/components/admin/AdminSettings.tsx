import React from 'react';
import { Setting<PERSON>, Bell, Shield, Database, Mail } from 'lucide-react';

export const AdminSettings: React.FC = () => {
    return (
        <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
                <div className="flex items-center gap-3 mb-6">
                    <div className="p-3 rounded-xl bg-blue-100">
                        <Settings className="h-6 w-6 text-blue-600" />
                    </div>
                    <h2 className="text-xl font-semibold text-gray-800">System Settings</h2>
                </div>

                <div className="space-y-6">
                    {/* Notifications */}
                    <div className="border-b border-gray-200 pb-6">
                        <div className="flex items-center gap-3 mb-4">
                            <Bell className="h-5 w-5 text-gray-500" />
                            <h3 className="text-lg font-medium text-gray-800">Notifications</h3>
                        </div>

                        <div className="space-y-4">
                            {/* Email Notifications */}
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium text-gray-700">Email Notifications</div>
                                    <div className="text-sm text-gray-500">Receive system alerts via email</div>
                                </div>
                                <ToggleSwitch defaultChecked />
                            </div>

                            {/* System Alerts */}
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium text-gray-700">System Alerts</div>
                                    <div className="text-sm text-gray-500">Important system notifications</div>
                                </div>
                                <ToggleSwitch defaultChecked />
                            </div>
                        </div>
                    </div>

                    {/* Security */}
                    <div className="border-b border-gray-200 pb-6">
                        <div className="flex items-center gap-3 mb-4">
                            <Shield className="h-5 w-5 text-gray-500" />
                            <h3 className="text-lg font-medium text-gray-800">Security</h3>
                        </div>

                        <div className="space-y-4">
                            {/* Two-Factor Authentication */}
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium text-gray-700">Two-Factor Authentication</div>
                                    <div className="text-sm text-gray-500">Additional security for your account</div>
                                </div>
                                <ToggleSwitch />
                            </div>

                            {/* API Access */}
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium text-gray-700">API Access</div>
                                    <div className="text-sm text-gray-500">Enable API access for integrations</div>
                                </div>
                                <ToggleSwitch defaultChecked />
                            </div>
                        </div>
                    </div>

                    {/* Data Management */}
                    <div className="border-b border-gray-200 pb-6">
                        <div className="flex items-center gap-3 mb-4">
                            <Database className="h-5 w-5 text-gray-500" />
                            <h3 className="text-lg font-medium text-gray-800">Data Management</h3>
                        </div>

                        <div className="space-y-4">
                            {/* Automatic Backups */}
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium text-gray-700">Automatic Backups</div>
                                    <div className="text-sm text-gray-500">Daily system backups</div>
                                </div>
                                <ToggleSwitch defaultChecked />
                            </div>

                            {/* Data Analytics */}
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium text-gray-700">Data Analytics</div>
                                    <div className="text-sm text-gray-500">Collect anonymous usage data</div>
                                </div>
                                <ToggleSwitch defaultChecked />
                            </div>
                        </div>
                    </div>

                    {/* Email Settings */}
                    <div>
                        <div className="flex items-center gap-3 mb-4">
                            <Mail className="h-5 w-5 text-gray-500" />
                            <h3 className="text-lg font-medium text-gray-800">Email Settings</h3>
                        </div>

                        <div className="space-y-4">
                            {/* System Email Address */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    System Email Address
                                </label>
                                <input
                                    type="email"
                                    defaultValue="<EMAIL>"
                                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                />
                            </div>

                            {/* Email Template Directory */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Email Template Directory
                                </label>
                                <input
                                    type="text"
                                    defaultValue="/templates/email/"
                                    className="w-full p-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

const ToggleSwitch: React.FC<{ defaultChecked?: boolean }> = ({ defaultChecked }) => (
    <label className="relative inline-flex items-center cursor-pointer">
        <input type="checkbox" className="sr-only peer" defaultChecked={defaultChecked} />
        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600" />
    </label>
);
