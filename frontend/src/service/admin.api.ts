import axios from "axios";
import { API } from "../utils/api";

interface CreateTrainerData {
  id: string;
  name: string;
  email: string;
  mobileNumber?: string;
  trainerRole: string;
  avatar: string;
  status: string;
}

interface SearchTrainersParams {
  search?: string;
  email?: string;
  trainerRole?: string;
  status?: string;
  limit?: number;
  offset?: number;
}

interface UpdateTrainerData extends Partial<CreateTrainerData> {}

interface WorkoutLogData {
  trainerId: string;
  description: string;
  // Add more workout log fields as needed
}

export const adminApi = {
  // ───── Trainers ─────
  getAllTrainers: async (): Promise<any> => {
    try {
      const response = await axios.get(`${API}/api/admin/trainers`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching trainers:", error);
      throw error;
    }
  },

  createTrainer: async (data: CreateTrainerData): Promise<any> => {
    try {
      const response = await axios.post(`${API}/api/admin/trainer`, data, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error creating trainer:", error);
      throw error;
    }
  },

  updateTrainer: async (id: string, data: UpdateTrainerData): Promise<any> => {
    try {
      const response = await axios.put(`${API}/api/admin/trainer/${id}`, data, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error updating trainer:", error);
      throw error;
    }
  },

  deleteTrainer: async (id: string): Promise<any> => {
    try {
      const response = await axios.delete(`${API}/api/admin/trainer/${id}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error deleting trainer:", error);
      throw error;
    }
  },

  // ───── Workout Logs ─────
  getWorkoutLogs: async (): Promise<any> => {
    try {
      const response = await axios.get(`${API}/admin/workoutlog`);
      return response.data;
    } catch (error) {
      console.error("Error fetching workout logs:", error);
      throw error;
    }
  },

  createWorkoutLog: async (data: WorkoutLogData): Promise<any> => {
    try {
      const response = await axios.post(`${API}/admin/workoutlog`, data, {
        headers: { "Content-Type": "application/json" },
      });
      return response.data;
    } catch (error) {
      console.error("Error creating workout log:", error);
      throw error;
    }
  },

  getTrainerById: async (id: string): Promise<any> => {
    try {
      const response = await axios.get(`${API}/api/admin/trainer/${id}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("access_token")}`,
        },
      });
      return response.data;
    } catch (error) {
      console.error("Error creating workout log:", error);
      throw error;
    }
  },

  searchTrainers: async (params: SearchTrainersParams) => {
    try {
      const queryParams = new URLSearchParams();

      if (params.search) queryParams.append("search", params.search);
      if (params.email) queryParams.append("email", params.email);
      if (params.trainerRole)
        queryParams.append("trainerRole", params.trainerRole);
      if (params.status) queryParams.append("status", params.status);
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.offset) queryParams.append("offset", params.offset.toString());

      const response = await axios.get(
        `${API}/api/admin/trainers/search?${queryParams.toString()}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("access_token")}`,
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error("Error searching trainers:", error);
      throw error;
    }
  },
};
