import axios from 'axios';
import { API } from '../utils/api';
import { WorkoutSet } from '../types/workout';


export const workoutSetsApi = {
    getAll: async (): Promise<WorkoutSet[]> => {
      let config = {
        method: 'get',
        url: `${API}/workout-sets`,
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      };
  
      try {
        const response = await axios.request(config);
        return response.data;
      } catch (error) {
        console.error(error);
        throw error;
      }
    },
  
    getByUser: async (userId: string): Promise<WorkoutSet[]> => {
      let config = {
        method: 'get',
        url: `${API}/workout-sets/user/${userId}`,
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      };
  
      try {
        const response = await axios.request(config);
        return response.data;
      } catch (error) {
        console.error(error);
        throw error;
      }
    },
  
    create: async (data: any): Promise<WorkoutSet> => {
      const token = localStorage.getItem('access_token');
      return axios.post(`${API}/workout-sets`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }).then(res => res.data);
    },
  
    update: async (id: string, data: any): Promise<WorkoutSet> => {
      const token = localStorage.getItem('access_token');
      return axios.put(`${API}/workout-sets/${id}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }).then(res => res.data);
    },
  
    remove: async (id: string): Promise<void> => {
      const token = localStorage.getItem('access_token');
      return axios.delete(`${API}/workout-sets/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }).then(res => res.data);
    }
  };