import axios from 'axios';
import { API } from '../utils/api';

interface ForgotPasswordData {
    email: string;
}

interface VerifyOtpData {
    email: string;
    otp: string;
}

interface ResetPasswordData {
    email: string;
    otp: string;
    newPassword: string;
}

export const otpApi = {
    forgotPassword: async (data: ForgotPasswordData): Promise<any> => {
        try {
            const response = await axios.post(`${API}/otp/forgot-password`, data, {
                headers: { 'Content-Type': 'application/json' },
            });
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },

    verifyOtp: async (data: VerifyOtpData): Promise<any> => {
        try {
            const response = await axios.post(`${API}/otp/verify-otp`, data, {
                headers: { 'Content-Type': 'application/json' },
            });
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },

    resetPassword: async (data: ResetPasswordData): Promise<any> => {
        try {
            const response = await axios.post(`${API}/otp/reset-password`, data, {
                headers: { 'Content-Type': 'application/json' },
            });
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },
};