import axios from 'axios';
import { API } from '../utils/api';

interface RegisterData {
    name?: string;
    email: string;
    password?: string;
    role: 'trainer' | 'trainee';
    mobileNumber?: string;
}

export const registerApi = {
    register: async (userData: RegisterData): Promise<any> => {
        let config = {
            method: 'post',
            url: `${API}/auth/register`,
            headers: {
                'Content-Type': 'application/json',
            },
            data: userData,
        };

        try {
            const response = await axios.request(config);
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },
};
