import axios from "axios";
import { API } from "../utils/api";

interface TrainerAssignmentData {
  trainerId: string;
  trainee_name: string;
  trainee_email: string;
  trainee_mobileNumber?: string;
  isActive: boolean;
  startDate: string;
  endDate: string;
  approved: boolean;
  trainee_age?: number;
  trainee_profileImageUrl?: string;
  trainee_level?: string;
  trainee_fitnessGoal?: string;
  trainee_weight?: number;
  trainee_bodyFatPercentage?: number;
  trainee_gender?: string;
  trainee_height?: number;
  trainee_activityLevel?: string;
}

export const trainerAssignmentApi = {
  assignTrainer: async (
    assignmentData: TrainerAssignmentData
  ): Promise<any> => {
    const config = {
      method: "post",
      url: `${API}/trainer-assignments`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
      },
      data: assignmentData,
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  getAssignmentsByTrainerId: async (trainerId: string) => {
    const config = {
      method: "get",
      url: `${API}/trainer-assignments/trainer/${trainerId}`,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
      },
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  getAssignmentsByTraineeId: async (trainerId: string) => {
    const config = {
      method: "get",
      url: `${API}/trainer-assignments/trainee/${trainerId}`,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
      },
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  getAssignmentsById: async (assignmentId: string) => {
    const config = {
      method: "get",
      url: `${API}/trainer-assignments/${assignmentId}`,
      headers: {
        Authorization: `Bearer ${localStorage.getItem("access_token")}`,
      },
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },
};
