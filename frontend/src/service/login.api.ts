import axios from 'axios';
import { API } from '../utils/api';

interface LoginData {
    email: string;
    password?: string;
}

export const loginApi = {
    login: async (userData: LoginData): Promise<any> => { 
        let config = {
            method: 'post',
            url: `${API}/auth/login`,
            headers: {
                'Content-Type': 'application/json',
            },
            data: userData,
        };

        try {
            const response = await axios.request(config);
            return response.data;
        } catch (error) {
            console.error(error);
            throw error;
        }
    },
};

