import axios from 'axios';
import { API } from '../utils/api';
import { WorkoutExercise } from '../types/workout';

export const workoutExercisesApi = {
    getAll: async (): Promise<WorkoutExercise[]> => {
      let config = {
        method: 'get',
        url: `${API}/workout-exercises`,
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      };
  
      try {
        const response = await axios.request(config);
        return response.data;
      } catch (error) {
        console.error(error);
        throw error;
      }
    },
  
    getByWorkout: async (workoutId: string): Promise<WorkoutExercise[]> => {
      let config = {
        method: 'get',
        url: `${API}/workout-exercises/workout/${workoutId}`,
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`
        }
      };
  
      try {
        const response = await axios.request(config);
        return response.data;
      } catch (error) {
        console.error(error);
        throw error;
      }
    },
  
    create: async (data: any): Promise<WorkoutExercise> => {
      const token = localStorage.getItem('access_token');
      return axios.post(`${API}/workout-exercises`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }).then(res => res.data);
    },
  
    update: async (id: string, data: any): Promise<WorkoutExercise> => {
      const token = localStorage.getItem('access_token');
      return axios.put(`${API}/workout-exercises/${id}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }).then(res => res.data);
    },
  
    remove: async (id: string): Promise<void> => {
      const token = localStorage.getItem('access_token');
      return axios.delete(`${API}/workout-exercises/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }).then(res => res.data);
    }
  };