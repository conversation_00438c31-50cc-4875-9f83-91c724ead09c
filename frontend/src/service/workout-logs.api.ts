import axios from 'axios';
import { API } from '../utils/api';
import { WorkoutLog, WorkoutExercise, WorkoutSet } from '../types/workout';

export const workoutLogsApi = {
  getAll: async (): Promise<WorkoutLog[]> => {
    let config = {
      method: 'get',
      url: `${API}/workout-logs`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  getByUser: async (userId: string): Promise<WorkoutLog[]> => {

    let config = {
      method: 'get',
      url: `${API}/workout-logs/user/${userId}`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },


  create: async (data: any): Promise<WorkoutLog> => {
    const token = localStorage.getItem('access_token');
    return axios.post(`${API}/workout-logs`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  update: async (id: string, data: any): Promise<WorkoutLog> => {
    const token = localStorage.getItem('access_token');
    return axios.put(`${API}/workout-logs/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  complete: async (id: string): Promise<WorkoutLog> => {
    const token = localStorage.getItem('access_token');
    return axios.put(`${API}/workout-logs/${id}/complete`, {}, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  remove: async (id: string): Promise<void> => {
    const token = localStorage.getItem('access_token');
    return axios.delete(`${API}/workout-logs/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  getLastWeekByUser: async (userId: string): Promise<WorkoutLog[]> => {
    const token = localStorage.getItem('access_token');
    return axios.get(`${API}/workout-logs/user/${userId}/last-week`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  }
};



export const workoutExercisesApi = {
  getAll: async (): Promise<WorkoutExercise[]> => {
    let config = {
      method: 'get',
      url: `${API}/workout-exercises`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  getByWorkout: async (workoutId: string): Promise<WorkoutExercise[]> => {
    let config = {
      method: 'get',
      url: `${API}/workout-exercises/workout/${workoutId}`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  create: async (data: any): Promise<WorkoutExercise> => {
    const token = localStorage.getItem('access_token');
    return axios.post(`${API}/workout-exercises`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  update: async (id: string, data: any): Promise<WorkoutExercise> => {
    const token = localStorage.getItem('access_token');
    return axios.put(`${API}/workout-exercises/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  remove: async (id: string): Promise<void> => {
    const token = localStorage.getItem('access_token');
    return axios.delete(`${API}/workout-exercises/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  }
};


export const workoutSetsApi = {
  getAll: async (): Promise<WorkoutSet[]> => {
    let config = {
      method: 'get',
      url: `${API}/workout-sets`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  getByUser: async (userId: string): Promise<WorkoutSet[]> => {
    let config = {
      method: 'get',
      url: `${API}/workout-sets/user/${userId}`,
      headers: { 
        'Content-Type': 'application/json', 
        'Authorization': `Bearer ${localStorage.getItem('access_token')}`
      }
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error(error);
      throw error;
    }
  },

  create: async (data: any): Promise<WorkoutSet> => {
    const token = localStorage.getItem('access_token');
    return axios.post(`${API}/workout-sets`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  update: async (id: string, data: any): Promise<WorkoutSet> => {
    const token = localStorage.getItem('access_token');
    return axios.put(`${API}/workout-sets/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  },

  remove: async (id: string): Promise<void> => {
    const token = localStorage.getItem('access_token');
    return axios.delete(`${API}/workout-sets/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }).then(res => res.data);
  }
};
