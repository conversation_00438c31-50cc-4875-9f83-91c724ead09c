import axios from 'axios';
import { API } from '../utils/api';

interface LogoutData {
  token: string;
}

export const authApi = {
  logout: async (logoutData: LogoutData): Promise<any> => {
    const config = {
      method: 'post',
      url: `${API}/auth/logout`,
      headers: {
        'Content-Type': 'application/json',
      },
      data: logoutData,
    };

    try {
      const response = await axios.request(config);
      return response.data;
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  },
};
