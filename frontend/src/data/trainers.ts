import { Trainer } from '../contexts/AdminContext';

// Mock trainer data
const trainersData: Trainer[] = [
  {
    id: 'trainer-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainer<PERSON><PERSON>: 'senior_trainer',
    status: 'active'
  },
  {
    id: 'trainer-002',
    name: '<PERSON>',
    email: 'micha<PERSON>.<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainer<PERSON><PERSON>: 'senior_trainer',
    status: 'active'
  },
  {
    id: 'trainer-003',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    
    trainer<PERSON><PERSON>: 'junior_trainer',
    status: 'active'
  },
  {
    id: 'trainer-004',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    
    trainer<PERSON>ole: 'nutrition_specialist',
    status: 'active'
  },
  {
    id: 'trainer-005',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    
    trainer<PERSON><PERSON>: 'junior_trainer',
    status: 'inactive'
  },
  {
    id: 'trainer-006',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerRole: 'senior_trainer',
    status: 'pending'
  }
];

/**
 * Get all trainers
 */
export const getTrainers = async (): Promise<Trainer[]> => {
  // Simulate API call delay
  return new Promise(resolve => {
    setTimeout(() => resolve(trainersData), 300);
  });
};

/**
 * Get a specific trainer by ID
 */
export const getTrainerById = (id: string): Trainer | undefined => {
  return trainersData.find(trainer => trainer.id === id);
};