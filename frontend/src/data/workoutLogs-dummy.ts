import { WorkoutLog } from '../contexts/AdminContext';

// Helper to generate dates within the last 60 days
const generateRecentDate = () => {
  const date = new Date();
  const daysAgo = Math.floor(Math.random() * 60);
  date.setDate(date.getDate() - daysAgo);
  return date.toISOString().split('T')[0];
};

// Mock workout log data
const workoutLogsData: WorkoutLog[] = [
  // User 1 logs (<PERSON>)
  ...Array.from({ length: 15 }, (_, i) => ({
    id: `log-${1000 + i}`,
    userId: 'user-001',
    trainerId: 'trainer-001',
    date: generateRecentDate(),
    type: ['strength', 'cardio', 'hiit', 'flexibility'][Math.floor(Math.random() * 4)],
    completed: Math.random() > 0.2, // 80% completion rate
    metrics: {
      duration: Math.floor(Math.random() * 60) + 30, // 30-90 minutes
      calories: Math.floor(Math.random() * 400) + 200, // 200-600 calories
      intensity: Math.floor(Math.random() * 5) + 3 // 3-8 intensity
    }
  })),
  
  // User 2 logs (<PERSON>)
  ...Array.from({ length: 12 }, (_, i) => ({
    id: `log-${2000 + i}`,
    userId: 'user-002',
    trainerId: 'trainer-001',
    date: generateRecentDate(),
    type: ['strength', 'cardio', 'hiit', 'flexibility'][Math.floor(Math.random() * 4)],
    completed: Math.random() > 0.1, // 90% completion rate
    metrics: {
      duration: Math.floor(Math.random() * 60) + 30,
      calories: Math.floor(Math.random() * 400) + 200,
      intensity: Math.floor(Math.random() * 5) + 3
    }
  })),
  
  // User 3 logs (Robert Lee)
  ...Array.from({ length: 10 }, (_, i) => ({
    id: `log-${3000 + i}`,
    userId: 'user-003',
    trainerId: 'trainer-002',
    date: generateRecentDate(),
    type: ['strength', 'cardio', 'hiit', 'flexibility'][Math.floor(Math.random() * 4)],
    completed: Math.random() > 0.15, // 85% completion rate
    metrics: {
      duration: Math.floor(Math.random() * 60) + 30,
      calories: Math.floor(Math.random() * 400) + 200,
      intensity: Math.floor(Math.random() * 5) + 3
    }
  })),
  
  // Additional logs for other users
  ...Array.from({ length: 50 }, (_, i) => {
    const userId = `user-00${Math.floor(Math.random() * 10) + 1}`;
    const trainerId = userId === 'user-001' || userId === 'user-002' || userId === 'user-008' 
      ? 'trainer-001' 
      : userId === 'user-003' || userId === 'user-004' || userId === 'user-010'
        ? 'trainer-002'
        : userId === 'user-005' || userId === 'user-006'
          ? 'trainer-003'
          : 'trainer-004';
    
    return {
      id: `log-${4000 + i}`,
      userId,
      trainerId,
      date: generateRecentDate(),
      type: ['strength', 'cardio', 'hiit', 'flexibility'][Math.floor(Math.random() * 4)],
      completed: Math.random() > 0.2,
      metrics: {
        duration: Math.floor(Math.random() * 60) + 30,
        calories: Math.floor(Math.random() * 400) + 200,
        intensity: Math.floor(Math.random() * 5) + 3
      }
    };
  })
];

/**
 * Get all workout logs
 */
export const getWorkoutLogs = async (): Promise<WorkoutLog[]> => {
  // Simulate API call delay
  return new Promise(resolve => {
    setTimeout(() => resolve(workoutLogsData), 300);
  });
};

/**
 * Get workout logs by user ID
 */
export const getWorkoutLogsByUserId = (userId: string): WorkoutLog[] => {
  return workoutLogsData.filter(log => log.userId === userId);
};

/**
 * Get workout logs by trainer ID
 */
export const getWorkoutLogsByTrainerId = (trainerId: string): WorkoutLog[] => {
  return workoutLogsData.filter(log => log.trainerId === trainerId);
};

/**
 * Get recent workout logs (within last 7 days)
 */
export const getRecentWorkoutLogs = (): WorkoutLog[] => {
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  
  return workoutLogsData.filter(log => {
    const logDate = new Date(log.date);
    return logDate >= sevenDaysAgo;
  });
};