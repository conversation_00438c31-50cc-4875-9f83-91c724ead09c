// Default user profile template with minimal attributes
const defaultUserProfile = {
  userName: "New User",
  userId: "",
  progressOverview: "No progress data available",
  membershipStartDate: "",
  membershipEndDate: "",
  startingWeight: 0,
  currentWeight: 0,
  startingFatPercentage: 0,
  currentFatPercentage: 0,
  engagementLevel: 0,
  progressLevel: 0,
  latestChanges: "No recent changes",
  latestEvents: "No recent events",
  goal: "fat_loss" as const,
  weeklyIntensity: 0,
};

// Function to get overview by user ID that works with any data source
export const getOverviewById = (userId: string) => {
  try {
    // First, try to get user from the current app state or session storage
    const currentUserData = sessionStorage.getItem(`user_${userId}`);

    if (currentUserData) {
      const userData = JSON.parse(currentUserData);
      return createUserProfile(userData);
    }

    // If not in session storage, try localStorage or other potential sources
    const localUser =
      localStorage.getItem(`user_${userId}`) ||
      localStorage.getItem("currentUser");

    if (localUser) {
      const userData = JSON.parse(localUser);
      if (userData.id === userId) {
        return createUserProfile(userData);
      }
    }

    // As a fallback, check if we have users array stored
    const usersData =
      localStorage.getItem("users") || sessionStorage.getItem("users");

    if (usersData) {
      const users = JSON.parse(usersData);
      const user = users.find((u: any) => u.id === userId);

      if (user) {
        return createUserProfile(user);
      }
    }

    // If we still don't have data, create a minimal profile with the userId
    return {
      ...defaultUserProfile,
      userId: userId,
      userName: `User ${userId.substring(0, 5)}`,
    };
  } catch (error) {
    console.error("Error retrieving user data:", error);
    return {
      ...defaultUserProfile,
      userId: userId,
    };
  }
};

// Helper function to create a user profile from any user data structure
function createUserProfile(userData: any) {
  // Map dynamic user properties to our expected overview format
  return {
    ...defaultUserProfile,
    userId: userData.id || "",
    userName: userData.name || userData.userName || "User",
    progressOverview:
      userData.progressOverview ||
      `${userData.name || "User"}'s training progress`,
    membershipStartDate: userData.membershipStartDate || "",
    membershipEndDate: userData.membershipEndDate || "",
    startingWeight: userData.startingWeight || 0,
    currentWeight: userData.currentWeight || 0,
    startingFatPercentage: userData.startingFatPercentage || 0,
    currentFatPercentage: userData.currentFatPercentage || 0,
    engagementLevel: userData.engagementLevel || userData.performance || 0,
    progressLevel:
      userData.progressLevel || Math.round((userData.performance || 0) * 0.8),
    latestChanges:
      userData.latestChanges || userData.attentionNote || "No recent changes",
    latestEvents:
      userData.latestEvents ||
      `Last active on ${userData.lastActive || "unknown date"}`,
    goal: userData.goal || "fat_loss",
    weeklyIntensity: userData.weeklyIntensity || 0,
  };
}

export const weeklyProgress = { intensity: "0/10", adherence: "0%" };
export const recommendations = ["No recommendations available"];
