import { User } from '../contexts/AdminContext';

// Mock user data
const usersData: User[] = [
  {
    id: 'user-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-001',
    status: 'active',
    goal: 'fat_loss',
    startDate: '2023-12-15',
    lastActive: '2025-04-01'
  },
  {
    id: 'user-002',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-001',
    status: 'active',
    goal: 'muscle_gain',
    startDate: '2024-01-05',
    lastActive: '2025-04-03'
  },
  {
    id: 'user-003',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-002',
    status: 'active',
    goal: 'general_fitness',
    startDate: '2023-11-20',
    lastActive: '2025-04-02'
  },
  {
    id: 'user-004',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-002',
    status: 'inactive',
    goal: 'fat_loss',
    startDate: '2023-10-10',
    lastActive: '2025-03-15'
  },
  {
    id: 'user-005',
    name: 'Thomas Wright',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-003',
    status: 'active',
    goal: 'muscle_gain',
    startDate: '2024-02-01',
    lastActive: '2025-04-03'
  },
  {
    id: 'user-006',
    name: 'Susan Martin',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-003',
    status: 'active',
    goal: 'fat_loss',
    startDate: '2024-01-15',
    lastActive: '2025-04-02'
  },
  {
    id: 'user-007',
    name: 'Daniel Lopez',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-004',
    status: 'active',
    goal: 'general_fitness',
    startDate: '2023-12-05',
    lastActive: '2025-04-01'
  },
  {
    id: 'user-008',
    name: 'Linda Miller',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-001',
    status: 'active',
    goal: 'fat_loss',
    startDate: '2024-02-10',
    lastActive: '2025-04-03'
  },
  {
    id: 'user-009',
    name: 'William King',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-004',
    status: 'inactive',
    goal: 'muscle_gain',
    startDate: '2023-11-01',
    lastActive: '2025-03-20'
  },
  {
    id: 'user-010',
    name: 'Jessica Moore',
    email: '<EMAIL>',
    avatar: '/api/placeholder/40/40',
    trainerId: 'trainer-002',
    status: 'active',
    goal: 'general_fitness',
    startDate: '2024-01-20',
    lastActive: '2025-04-02'
  }
];

/**
 * Get all users
 */
export const getUsers = async (): Promise<User[]> => {
  // Simulate API call delay
  return new Promise(resolve => {
    setTimeout(() => resolve(usersData), 300);
  });
};

/**
 * Get a specific user by ID
 */
export const getUserById = (id: string): User | undefined => {
  return usersData.find(user => user.id === id);
};

/**
 * Get users by trainer ID
 */
export const getUsersByTrainerId = (trainerId: string): User[] => {
  return usersData.filter(user => user.trainerId === trainerId);
};