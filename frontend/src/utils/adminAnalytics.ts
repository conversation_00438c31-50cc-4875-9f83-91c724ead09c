import { User, WorkoutLog } from '../contexts/AdminContext';

interface TrainerPerformance {
  name: string;
  trainees: number;
  success: number;
}

interface CategoryAchievement {
  category: string;
  rate: number;
}

interface UserActivity {
  type: string;
  count: number;
  time: string;
}

interface SystemAnalytics {
  totalUsers: number;
  activeUsers: number;
  userGrowth: number;
  churnRate: number;
  averageEngagement: number;
  goalAchievement: number;
  trainerPerformance: TrainerPerformance[];
  categoryAchievement: CategoryAchievement[];
  userActivity: UserActivity[];
}

/**
 * Calculate system-wide analytics for admin dashboard
 */
export const calculateSystemAnalytics = (
  users: User[],
  workoutLogs: WorkoutLog[]
): SystemAnalytics => {
  // Calculate active users
  const totalUsers = users.length;
  const activeUsers = users.filter(user => user.status === 'active').length;
  
  // Calculate user growth (mock data assuming 8% growth)
  const userGrowth = 8.3;
  
  // Calculate churn rate (mock data)
  const churnRate = 3.7;
  
  // Calculate average engagement rate
  const today = new Date();
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - 30);
  
  const recentLogs = workoutLogs.filter(log => {
    const logDate = new Date(log.date);
    return logDate >= thirtyDaysAgo;
  });
  
  const averageLogsPerUser = recentLogs.length / totalUsers;
  const averageEngagement = (averageLogsPerUser / 12) * 100; // Assuming expected 12 logs per month is 100%
  
  // Calculate goal achievement
  const goalAchievement = 76.4; // Mock data
  
  // Generate trainer performance data
  const trainerPerformance: TrainerPerformance[] = [
    { name: "Sarah Johnson", trainees: 24, success: 89 },
    { name: "Michael Chen", trainees: 18, success: 92 },
    { name: "David Wilson", trainees: 31, success: 78 },
    { name: "Lisa Rodriguez", trainees: 15, success: 85 }
  ];
  
  // Generate category achievement data
  const categoryAchievement: CategoryAchievement[] = [
    { category: "Weight Loss", rate: 82 },
    { category: "Muscle Building", rate: 76 },
    { category: "Cardio Fitness", rate: 91 },
    { category: "Flexibility", rate: 68 }
  ];
  
  // Generate user activity data
  const userActivity: UserActivity[] = [
    { type: "Workouts Logged", count: 286, time: "Today" },
    { type: "Meal Plans Updated", count: 174, time: "Yesterday" },
    { type: "Goals Achieved", count: 92, time: "This Week" }
  ];
  
  return {
    totalUsers,
    activeUsers,
    userGrowth,
    churnRate,
    averageEngagement,
    goalAchievement,
    trainerPerformance,
    categoryAchievement,
    userActivity
  };
};