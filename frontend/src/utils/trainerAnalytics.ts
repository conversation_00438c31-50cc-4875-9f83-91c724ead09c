import { User, Trainer, WorkoutLog } from '../contexts/AdminContext';

interface ClientProgress {
  name: string;
  goal: string;
  progress: number;
  status: 'ahead' | 'on_track' | 'behind';
}

interface WeeklyMetrics {
  completedSessions: number;
  missedSessions: number;
  goalsAchieved: number;
}

interface PerformanceMetrics {
  clientRetention: number[];
  clientProgressMetrics: number[]; // Renamed to avoid redeclaration error
}

interface TrainerAnalytics {
  activeClients: number;
  totalClients: number;
  avgClientProgress: number;
  clientRetention: number;
  weeklyMetrics: WeeklyMetrics;
  clientProgress: ClientProgress[];
  performanceMetrics: PerformanceMetrics;
}

/**
 * Calculate analytics for a specific trainer
 */
export const calculateTrainerAnalytics = (
  trainer: Trainer,
  users: User[],
  workoutLogs: WorkoutLog[]
): TrainerAnalytics => {
  // Find clients assigned to this trainer
  const trainerClients = users.filter(user => user.trainerId === trainer.id);
  const activeClients = trainerClients.filter(client => client.status === 'active');
  
  // Calculate client progress (mocked for now)
  const clientProgress: ClientProgress[] = trainerClients.slice(0, 5).map((client) => {
    // For demo purposes, generate varied progress metrics
    const progress = 40 + Math.random() * 50;
    let status: 'ahead' | 'on_track' | 'behind';
    
    if (progress > 75) {
      status = 'ahead';
    } else if (progress > 50) {
      status = 'on_track';
    } else {
      status = 'behind';
    }
    
    return {
      name: client.name,
      goal: client.goal,
      progress,
      status
    };
  });
  
  // Calculate weekly metrics
  const today = new Date();
  const weekStart = new Date(today);
  weekStart.setDate(today.getDate() - 7);
  
  const recentLogs = workoutLogs.filter(log => {
    const logDate = new Date(log.date);
    return log.trainerId === trainer.id && logDate >= weekStart;
  });
  
  const completedSessions = recentLogs.filter(log => log.completed).length;
  const missedSessions = recentLogs.filter(log => !log.completed).length;
  
  // Generate mock performance metrics for visualization
  const clientRetention = Array.from({ length: 7 }, () => 60 + Math.random() * 30);
  const clientProgressMetrics = Array.from({ length: 7 }, () => 40 + Math.random() * 50); // Renamed to avoid conflict
  
  return {
    activeClients: activeClients.length,
    totalClients: trainerClients.length,
    avgClientProgress: trainerClients.length > 0 
      ? clientProgressMetrics.reduce((sum, curr) => sum + curr, 0) / clientProgressMetrics.length 
      : 0,
    clientRetention: 85 + Math.random() * 10, // Mock retention rate
    weeklyMetrics: {
      completedSessions,
      missedSessions,
      goalsAchieved: Math.floor(Math.random() * 10) + 5 // Mock goals achieved
    },
    clientProgress,
    performanceMetrics: {
      clientRetention,
      clientProgressMetrics // Updated to match the renamed variable
    }
  };
};
