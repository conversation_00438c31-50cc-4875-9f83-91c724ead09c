import { Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "../contexts/AuthContext";
import { MealPlanProvider } from "../contexts/MealPlanContext";
import { WorkoutLogProvider } from "../contexts/WorkoutLogContext";
import {Login} from "../pages/LoginPage";
import { Register } from "../pages/RegisterPage";
import {HomePage} from "../pages/HomePage";
import { ForgetPassword } from "../pages/ForgetPassword";
import { ResetPassword } from "../pages/ResetPassword";

// Admin Components
import { AdminDashboard } from "../views/AdminView";
import { AdminTrainers } from "../components/admin/AdminTrainers";
import { AdminAnalytics } from "../components/admin/AdminAnalytics";
import { AdminSettings } from "../components/admin/AdminSettings";


// Private Route Component
const PrivateRoute = ({ children }: { children: JSX.Element }) => {
  const { user } = useAuth();
  return user ? children : <Navigate to="/login" />;
};


// Admin Route Component (requires admin role)
const AdminRoute = ({ children }: { children: JSX.Element }) => {
  const { user } = useAuth();

  return user && user.role?.name === 'ROLE_ADMIN' ? children : <Navigate to="/" />;
};


const AuthRoutes: React.FC = () => {
  return (
    <AuthProvider>
      <Routes>
        <Route path="/login" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/forgot-password" element={<ForgetPassword />} />
        <Route path="/reset-password" element={<ResetPassword />} />
        <Route
          path="/homepage"
          element={
            <PrivateRoute>
              <MealPlanProvider>
                <WorkoutLogProvider>
                  <HomePage />
                </WorkoutLogProvider>
              </MealPlanProvider>
            </PrivateRoute>
          }
        />

        {/* Admin Routes */}
      <Route path="/admin" element={
        <AdminRoute>
          <AdminDashboard />
        </AdminRoute>
      } />
      <Route path="/admin/trainers" element={
        <AdminRoute>
          <AdminTrainers trainerTabClicked={false} onTrainerSelect={()=>{}}/>
        </AdminRoute>
      } />
      <Route path="/admin/analytics" element={
        <AdminRoute>
          <AdminAnalytics />
        </AdminRoute>
      } />
      <Route path="/admin/settings" element={
        <AdminRoute>
          <AdminSettings />
        </AdminRoute>
      } />

        <Route path="*" element={<Navigate to="/login" />} />
      </Routes>
    </AuthProvider>
  );
};

export default AuthRoutes;
