import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { getTrainers } from "../data/trainers";
import { getUsers } from "../data/user";
import { getWorkoutLogs } from "../data/workoutLogs-dummy";

// Define the types for our data structures
export interface Trainer {
  id: string;
  name: string;
  email: string;
  avatar: string;
  trainerRole: "senior_trainer" | "junior_trainer" | "nutrition_specialist";
  status: "active" | "inactive" | "pending";
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  trainerId: string;
  status: "active" | "inactive";
  goal: "fat_loss" | "muscle_gain" | "general_fitness";
  startDate: string;
  lastActive: string;
}

export interface WorkoutLog {
  id: string;
  userId: string;
  trainerId: string;
  date: string;
  type: string;
  completed: boolean;
  metrics: {
    duration: number;
    calories: number;
    intensity: number;
  };
}

// Define the context interface with addTrainer method
interface AdminContextType {
  trainers: Trainer[];
  users: User[];
  workoutLogs: WorkoutLog[];
  loading: boolean;
  error: string | null;
  refreshData: () => void;
  addTrainer: (trainer: Omit<Trainer, "id">) => void;
  setTrainers: React.Dispatch<React.SetStateAction<Trainer[]>>;
}

// Create the context with a default value
const AdminContext = createContext<AdminContextType>({
  trainers: [],
  users: [],
  workoutLogs: [],
  loading: true,
  error: null,
  refreshData: () => {},
  addTrainer: () => {},
  setTrainers: () => {},
});

// Create a provider component
interface AdminProviderProps {
  children: ReactNode;
}

export const AdminProvider: React.FC<AdminProviderProps> = ({ children }) => {
  const [trainers, setTrainers] = useState<Trainer[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [workoutLogs, setWorkoutLogs] = useState<WorkoutLog[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const loadData = async () => {
    try {
      setLoading(true);
      // Get data from our data functions
      const trainersData = await getTrainers();
      const usersData = await getUsers();
      const workoutLogsData = await getWorkoutLogs();

      setTrainers(trainersData);
      setUsers(usersData);
      setWorkoutLogs(workoutLogsData);
      setError(null);
    } catch (err) {
      setError("Failed to load admin data");
      console.error("AdminContext error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  const refreshData = () => {
    loadData();
  };

  // Add a new trainer to the state
  const addTrainer = (trainerData: Omit<Trainer, "id">) => {
    const newTrainer: Trainer = {
      ...trainerData,
      id: `trainer-${Date.now()}`,
    };

    setTrainers((prevTrainers) => [...prevTrainers, newTrainer]);

    // In a real app, you might also want to persist this to your API
    // saveTrainerToAPI(newTrainer).then(() => refreshData());
  };

  return (
    <AdminContext.Provider
      value={{
        trainers,
        users,
        workoutLogs,
        loading,
        error,
        refreshData,
        addTrainer,
        setTrainers,
      }}
    >
      {children}
    </AdminContext.Provider>
  );
};

// Create a custom hook to use the context
export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error("useAdmin must be used within an AdminProvider");
  }
  return context;
};
