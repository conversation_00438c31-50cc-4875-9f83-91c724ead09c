import axios from 'axios';
import React, { createContext, useContext, useState } from 'react';
import { WorkoutLog } from '../types/workout';
import { API } from '../utils/api';
import { workoutLogsApi } from '../service/workout-logs.api';

interface WorkoutLogContextType {
  workoutLogs: { [userId: string]: WorkoutLog[] };
  addWorkoutLog: (userId: string, log: WorkoutLog) => Promise<void>;
  getWorkoutLogs: (userId: string) => Promise<void>;
}

const WorkoutLogContext = createContext<WorkoutLogContextType | undefined>(undefined);

export const WorkoutLogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [workoutLogs, setWorkoutLogs] = useState<{ [userId: string]: WorkoutLog[] }>({});

  const addWorkoutLog = async (userId: string, log: WorkoutLog) => {
    try {
      const res = await workoutLogsApi.update(userId as string, {
        duration: log.duration, // Only passing duration
    });

      // Update local state with the newly created log
      setWorkoutLogs(prev => ({
        ...prev,
        [userId]: [...(prev[userId] || []), res].sort((a, b) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        )
      }));
    } catch (error) {
      console.error('Error adding workout log:', error);
    }
  };

  const getWorkoutLogs = async (userId: string) => {
    try {
      const res = await axios.get(`${API}/workout-logs/user/${userId}`, {
        headers: { Authorization: `Bearer ${localStorage.getItem('access_token')}` }
      });
      setWorkoutLogs(prev => ({ ...prev, [userId]: res.data }));
    } catch (error) {
      console.error('Error fetching workout logs:', error);
    }
  };

  return (
    <WorkoutLogContext.Provider value={{ workoutLogs, addWorkoutLog, getWorkoutLogs }}>
      {children}
    </WorkoutLogContext.Provider>
  );
};

export const useWorkoutLog = () => {
  const context = useContext(WorkoutLogContext);
  if (context === undefined) {
    throw new Error('useWorkoutLog must be used within a WorkoutLogProvider');
  }
  return context;
};
