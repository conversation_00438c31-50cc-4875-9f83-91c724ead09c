import React, { createContext, useContext, useState } from "react";
import { MealCategory } from "../types/food";

interface MealPlan {
  id: string;
  userId: string;
  meals: {
    id: string;
    meal: string;
    categories: MealCategory[];
    macroRange?: {
      protein?: number | { max: number; min: number };
      carbs?: number | { max: number; min: number };
      fats?: number | { max: number; min: number };
      calories?: number | { max: number; min: number };
    };
    defaultServing?: string;
    maxServing?: string;
    minServing?: string;
  }[];
}

interface MealPlanContextType {
  mealPlans: MealPlan[];
  updateMealPlan: (
    userId: string,
    mealId: string,
    categories: MealCategory[],
    macroRange?: {
      protein?: number | { max: number; min: number };
      carbs?: number | { max: number; min: number };
      fats?: number | { max: number; min: number };
      calories?: number | { max: number; min: number };
    },
    defaultServing?: string,
    maxServing?: string,
    minServing?: string
  ) => void;
  getMealPlanForUser: (userId: string) => MealPlan | null;
  initializeMealPlan: (userId: string) => void;
}

const MealPlanContext = createContext<MealPlanContextType | undefined>(
  undefined
);

export const MealPlanProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [mealPlans, setMealPlans] = useState<MealPlan[]>([]);

  const initializeMealPlan = (userId: string) => {
    setMealPlans((prevPlans) => {
      // Check if plan already exists for this user
      if (prevPlans.some((plan) => plan.userId === userId)) {
        return prevPlans;
      }

      // Create a new empty meal plan for the user
      const newPlan: MealPlan = {
        id: `plan-${userId}`,
        userId,
        meals: [],
      };
      return [...prevPlans, newPlan];
    });
  };

  const updateMealPlan = (
    userId: string,
    mealId: string,
    categories: MealCategory[],
    macroRange?: {
      protein?: number | { max: number; min: number };
      carbs?: number | { max: number; min: number };
      fats?: number | { max: number; min: number };
      calories?: number | { max: number; min: number };
    },
    defaultServing?: string,
    maxServing?: string,
    minServing?: string
  ) => {
    setMealPlans((prevPlans) => {
      const existingPlanIndex = prevPlans.findIndex(
        (plan) => plan.userId === userId
      );

      if (existingPlanIndex >= 0) {
        const updatedPlan = { ...prevPlans[existingPlanIndex] };
        const mealIndex = updatedPlan.meals.findIndex(
          (meal) => meal.id === mealId
        );

        if (mealIndex >= 0) {
          // Update existing meal, preserving serving values if not provided
          updatedPlan.meals[mealIndex] = {
            ...updatedPlan.meals[mealIndex],
            categories,
            macroRange: macroRange ?? updatedPlan.meals[mealIndex].macroRange,
            // Only update these if they are provided
            ...(defaultServing !== undefined && { defaultServing }),
            ...(maxServing !== undefined && { maxServing }),
            ...(minServing !== undefined && { minServing }),
          };
        } else {
          // Add new meal
          updatedPlan.meals.push({
            id: mealId,
            meal: `ארוחה ${updatedPlan.meals.length + 1}`,
            categories,
            ...(macroRange ? { macroRange } : {}),
            ...(defaultServing !== undefined && { defaultServing }),
            ...(maxServing !== undefined && { maxServing }),
            ...(minServing !== undefined && { minServing }),
          });
        }

        const newPlans = [...prevPlans];
        newPlans[existingPlanIndex] = updatedPlan;
        return newPlans;
      }

      // If no plan exists, create a new one
      const newPlan: MealPlan = {
        id: `plan-${userId}`,
        userId,
        meals: [
          {
            id: mealId,
            meal: "ארוחה 1",
            categories,
            ...(macroRange ? { macroRange } : {}),
            ...(defaultServing !== undefined && { defaultServing }),
            ...(maxServing !== undefined && { maxServing }),
            ...(minServing !== undefined && { minServing }),
          },
        ],
      };

      return [...prevPlans, newPlan];
    });
  };

  const getMealPlanForUser = (userId: string): MealPlan | null => {
    return mealPlans.find((plan) => plan.userId === userId) || null;
  };

  return (
    <MealPlanContext.Provider
      value={{
        mealPlans,
        updateMealPlan,
        getMealPlanForUser,
        initializeMealPlan,
      }}
    >
      {children}
    </MealPlanContext.Provider>
  );
};

export const useMealPlan = () => {
  const context = useContext(MealPlanContext);

  if (context === undefined) {
    throw new Error("useMealPlan must be used within a MealPlanProvider");
  }

  return context;
};
