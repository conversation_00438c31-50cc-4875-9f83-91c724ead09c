import React from "react";
import { Users, TrendingUp, Activity, Target } from "lucide-react";
import { useAdminGetAllTrainersQuery } from "../../api/services/Admin/AdminService";

export const AdminDashboard: React.FC = () => {
  const { data } = useAdminGetAllTrainersQuery({});

  return (
    <div className="space-y-6" dir="rtl">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-blue-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-blue-100">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div className="text-right">
              <h3 className="text-lg font-semibold text-gray-800">
                סך כל המאמנים
              </h3>
              <p className="text-3xl font-bold text-blue-600">
                {data?.data?.length || 0}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm text-green-600">
            <TrendingUp className="h-4 w-4" />
            <span>מנהלים 10 מתאמנים</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-purple-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-purple-100">
              <Activity className="h-6 w-6 text-purple-600" />
            </div>
            <div className="text-right">
              <h3 className="text-lg font-semibold text-gray-800">
                מתאמנים פעילים
              </h3>
              <p className="text-3xl font-bold text-purple-600">10</p>
            </div>
          </div>
          <div className="flex items-center gap-2 text-sm text-green-600">
            <TrendingUp className="h-4 w-4" />
            <span>שיעור גידול של 10%</span>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-emerald-200">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 rounded-xl bg-emerald-100">
              <Target className="h-6 w-6 text-emerald-600" />
            </div>
            <div className="text-right">
              <h3 className="text-lg font-semibold text-gray-800">
                התקדמות מטרה
              </h3>
              <p className="text-3xl font-bold text-emerald-600">10%</p>
            </div>
          </div>
          <div className="text-sm text-gray-600 text-right">
            השלמת אבני דרך ממוצעת של מתאמנים
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      {/* <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">
          Recent Activity
        </h3>
        <div className="space-y-4">
          {analytics.userActivity.map((activity, index) => (
            <div
              key={index}
              className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg"
            >
              <div className="p-2 bg-blue-100 rounded-lg">
                {activity.type === "Workouts Logged" ? (
                  <Activity className="h-5 w-5 text-blue-600" />
                ) : activity.type === "Meal Plans Updated" ? (
                  <UserPlus className="h-5 w-5 text-blue-600" />
                ) : (
                  <Target className="h-5 w-5 text-blue-600" />
                )}
              </div>
              <div>
                <p className="font-medium text-gray-800">{activity.type}</p>
                <p className="text-sm text-gray-500">
                  {activity.count} activities recorded
                </p>
              </div>
              <span className="ml-auto text-sm text-gray-500">
                {activity.time}
              </span>
            </div>
          ))}
        </div>
      </div> */}

      {/* Performance Metrics */}
      {/* <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Trainer Performance
          </h3>
          <div className="space-y-4">
            {analytics.trainerPerformance.map((trainer, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium text-gray-700">
                    {trainer.name}
                  </span>
                  <span className="text-sm text-gray-500">
                    {trainer.trainees} trainees
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-600 rounded-full"
                      style={{ width: `${trainer.success}%` }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-600">
                    {trainer.success}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 border-2 border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Goal Achievement by Category
          </h3>
          <div className="space-y-4">
            {analytics.categoryAchievement.map((category, index) => (
              <div key={index} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-700">
                    {category.category}
                  </span>
                  <span className="text-sm font-medium text-gray-600">
                    {category.rate}%
                  </span>
                </div>
                <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-green-600 rounded-full"
                    style={{ width: `${category.rate}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div> */}
    </div>
  );
};
