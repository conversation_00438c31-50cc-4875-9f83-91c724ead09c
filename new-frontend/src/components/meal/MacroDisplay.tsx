import React from "react";
import { <PERSON><PERSON><PERSON>ang<PERSON> } from "../../types/food";
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";

interface MacroDisplayProps {
  macroRange: MacroRange;
  title?: string;
  showTitle?: boolean;
}

export const MacroDisplay: React.FC<MacroDisplayProps> = ({
  macroRange,
  title = "טווח מאקרו",
  showTitle = true,
}) => {
  const formatNumber = (num: number) => Math.round(num);

  return (
    <div className="bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-700">
      {showTitle && (
        <h3 className="text-lg font-semibold text-gray-200 mb-3 text-center">
          {title}
        </h3>
      )}

      <div className="flex flex-wrap gap-2 justify-center">
        <span className="flex items-center text-white bg-purple-700 rounded-full px-3 py-1.5 font-medium shadow-sm">
          <Beef className="h-4 w-4 mr-1.5" />
          חלבון: {formatNumber(macroRange.min.protein)}-
          {formatNumber(macroRange.max.protein)}ג׳
        </span>

        <span className="flex items-center text-white bg-amber-700 rounded-full px-3 py-1.5 font-medium shadow-sm">
          <Cookie className="h-4 w-4 mr-1.5" />
          פחמימות: {formatNumber(macroRange.min.carbs)}-
          {formatNumber(macroRange.max.carbs)}ג׳
        </span>

        <span className="flex items-center text-white bg-green-700 rounded-full px-3 py-1.5 font-medium shadow-sm">
          <Scale className="h-4 w-4 mr-1.5" />
          שומן: {formatNumber(macroRange.min.fats)}-
          {formatNumber(macroRange.max.fats)}ג׳
        </span>

        <span className="flex items-center text-white bg-red-700 rounded-full px-3 py-1.5 font-medium shadow-sm">
          <Flame className="h-4 w-4 mr-1.5" />
          קלוריות: {formatNumber(macroRange.min.calories)}-
          {formatNumber(macroRange.max.calories)}
        </span>
      </div>
    </div>
  );
};
