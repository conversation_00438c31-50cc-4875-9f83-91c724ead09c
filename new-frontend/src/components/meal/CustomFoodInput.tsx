import React, { useState } from 'react';
import { Coffee, Save, X, Check } from 'lucide-react';

interface CustomFoodInputProps {
  onSave: (food: {
    name: string;
    category: 'protein' | 'carbs' | 'other';
    macros: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
    amount: number;
  }) => void;
  onClose: () => void;
}

export const CustomFoodInput: React.FC<CustomFoodInputProps> = ({ onSave, onClose }) => {
  const [name, setName] = useState('');
  const [category, setCategory] = useState<'protein' | 'carbs' | 'other'>('other');
  const [amount, setAmount] = useState(100);
  const [macros, setMacros] = useState({
    protein: 0,
    carbs: 0,
    fats: 0,
    calories: 0
  });
  const [showSuccess, setShowSuccess] = useState(false);

  const handleSave = () => {
    if (!name.trim()) return;
    
    onSave({
      name,
      category,
      macros,
      amount
    });
    
    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
      onClose();
    }, 1500);
  };

  return (
    <div className="bg-gray-900 rounded-xl p-6 shadow-lg border-2 border-purple-800">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-purple-900">
            <Coffee className="h-5 w-5 text-purple-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-200">הוספת מזון מותאם אישית</h3>
        </div>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-800 rounded-full transition-colors duration-200"
        >
          <X className="h-5 w-5 text-gray-400" />
        </button>
      </div>

      {showSuccess && (
        <div className="mb-4 p-3 bg-green-900 text-green-300 rounded-lg flex items-center gap-2">
          <Check className="h-5 w-5" />
          <span>המזון נוסף בהצלחה!</span>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">שם המזון</label>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            placeholder="הזן שם..."
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">קטגוריה</label>
          <select
            value={category}
            onChange={(e) => setCategory(e.target.value as 'protein' | 'carbs' | 'other')}
            className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
          >
            <option value="protein">חלבון</option>
            <option value="carbs">פחמימה</option>
            <option value="other">אחר</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">כמות (גרם)</label>
          <input
            type="number"
            value={amount}
            onChange={(e) => setAmount(Number(e.target.value))}
            className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">חלבון (גרם)</label>
            <input
              type="number"
              value={macros.protein}
              onChange={(e) => setMacros({ ...macros, protein: Number(e.target.value) })}
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">פחמימות (גרם)</label>
            <input
              type="number"
              value={macros.carbs}
              onChange={(e) => setMacros({ ...macros, carbs: Number(e.target.value) })}
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">שומן (גרם)</label>
            <input
              type="number"
              value={macros.fats}
              onChange={(e) => setMacros({ ...macros, fats: Number(e.target.value) })}
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">קלוריות</label>
            <input
              type="number"
              value={macros.calories}
              onChange={(e) => setMacros({ ...macros, calories: Number(e.target.value) })}
              className="w-full p-2 border-2 border-gray-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 text-gray-200"
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors duration-200"
          >
            ביטול
          </button>
          <button
            onClick={handleSave}
            disabled={!name.trim()}
            className="flex items-center gap-2 px-4 py-2 bg-purple-700 text-gray-200 rounded-lg hover:bg-purple-600 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-5 w-5" />
            <span>שמור מזון</span>
          </button>
        </div>
      </div>
    </div>
  );
};