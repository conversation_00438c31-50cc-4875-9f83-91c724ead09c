import React, { useState } from "react";
import { FoodSelector } from "./FoodSelector";
import { FoodCategorySelector } from "./FoodCategorySelector";
import {
  Utensils,
  Edit2,
  Check,
  X,
  ChevronDown,
  ChevronUp,
  Info,
} from "lucide-react";
import { FoodItem, MacroRange, MealCategory } from "../../types/food";

interface MealEditorProps {
  mealName: string;
  initialCategories: MealCategory[];
  onUpdate: ({
    categories,
    macroRange,
    name,
  }: {
    categories: MealCategory[];
    macroRange: MacroRange;
    name: string;
  }) => void;
  foodItems: FoodItem[];
}

const MealEditor: React.FC<MealEditorProps> = ({
  mealName,
  initialCategories,
  onUpdate,
  foodItems,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(mealName);
  const [categories, setCategories] =
    useState<MealCategory[]>(initialCategories);

  const calculateMacroRange = (categories: MealCategory[]): MacroRange => {
    const initialMacros = {
      protein: 0,
      carbs: 0,
      fats: 0,
      calories: 0,
    };

    const minMacros = { ...initialMacros };
    let maxMacros = { ...initialMacros };

    categories.forEach((category) => {
      if (category.options.length === 0) return;

      const categoryMacros = category.options
        .map((option) => {
          const food = option.food;

          if (!food) return null;

          const amount = option.amount;
          return {
            protein: (food.macrosPer100g.protein * amount) / 100,
            carbs: (food.macrosPer100g.carbs * amount) / 100,
            fats: (food.macrosPer100g.fats * amount) / 100,
            calories: (food.macrosPer100g.calories * amount) / 100,
          };
        })
        .filter((m): m is NonNullable<typeof m> => m !== null);

      if (categoryMacros.length === 0) return;

      const categoryMin = {
        protein: Math.min(...categoryMacros.map((m) => m.protein)),
        carbs: Math.min(...categoryMacros.map((m) => m.carbs)),
        fats: Math.min(...categoryMacros.map((m) => m.fats)),
        calories: Math.min(...categoryMacros.map((m) => m.calories)),
      };

      const categoryMax = {
        protein: Math.max(...categoryMacros.map((m) => m.protein)),
        carbs: Math.max(...categoryMacros.map((m) => m.carbs)),
        fats: Math.max(...categoryMacros.map((m) => m.fats)),
        calories: Math.max(...categoryMacros.map((m) => m.calories)),
      };

      minMacros.protein += categoryMin.protein;
      minMacros.carbs += categoryMin.carbs;
      minMacros.fats += categoryMin.fats;
      minMacros.calories += categoryMin.calories;

      maxMacros.protein += categoryMax.protein;
      maxMacros.carbs += categoryMax.carbs;
      maxMacros.fats += categoryMax.fats;
      maxMacros.calories += categoryMax.calories;
    });

    const buffer = 1.05;
    maxMacros = {
      protein: maxMacros.protein * buffer,
      carbs: maxMacros.carbs * buffer,
      fats: maxMacros.fats * buffer,
      calories: maxMacros.calories * buffer,
    };

    return { min: minMacros, max: maxMacros };
  };

  const handleFoodAdd = (category: string, food: FoodItem) => {
    const newCategories = [...categories];
    const categoryName =
      category === "protein"
        ? "Protein"
        : category === "carbs"
        ? "Carbs"
        : "Other";

    const categoryIndex = newCategories.findIndex(
      (c) => c.name === categoryName
    );

    if (categoryIndex === -1) {
      newCategories.push({
        name: categoryName,
        options: [
          {
            foodId: food.id,
            amount: food.defaultServing,
            food,
          },
        ],
      });
    } else {
      // Create a new options array instead of modifying the existing one
      const newOptions = [
        ...newCategories[categoryIndex].options,
        {
          foodId: food.id,
          amount: food.defaultServing,
          food,
        },
      ];
      newCategories[categoryIndex] = {
        ...newCategories[categoryIndex],
        options: newOptions,
      };
    }

    setCategories(newCategories);
    const macroRange = calculateMacroRange(newCategories);
    onUpdate({ categories: newCategories, macroRange, name: editedName });
  };

  const handleFoodUpdate = (
    categoryIndex: number,
    optionIndex: number,
    amount: number
  ) => {
    const newCategories = [...categories];

    // Create a new options array with the updated amount
    const newOptions = newCategories[categoryIndex].options.map(
      (option, index) =>
        index === optionIndex ? { ...option, amount } : option
    );

    // Update the category with the new options array
    newCategories[categoryIndex] = {
      ...newCategories[categoryIndex],
      options: newOptions,
    };

    setCategories(newCategories);
    const macroRange = calculateMacroRange(newCategories);
    onUpdate({ categories: newCategories, macroRange, name: editedName });
    // updateMealPlan(userId, mealId, newCategories);
  };

  const handleFoodRemove = (categoryIndex: number, optionIndex: number) => {
    const newCategories = [...categories];

    // Create a new options array without the removed item
    const newOptions = [
      ...newCategories[categoryIndex].options.slice(0, optionIndex),
      ...newCategories[categoryIndex].options.slice(optionIndex + 1),
    ];

    // Update the category with the new options array
    newCategories[categoryIndex] = {
      ...newCategories[categoryIndex],
      options: newOptions,
    };

    // If the category is now empty, remove it
    if (newOptions.length === 0) {
      newCategories.splice(categoryIndex, 1);
    }

    setCategories(newCategories);
    const macroRange = calculateMacroRange(newCategories);
    onUpdate({ categories: newCategories, macroRange, name: editedName });
    // updateMealPlan(userId, mealId, newCategories);
  };

  const handleNameSave = () => {
    if (editedName.trim()) {
      const newCategories = [...categories];
      const macroRange = calculateMacroRange(newCategories);
      onUpdate({ categories: newCategories, macroRange, name: editedName });

      setIsEditing(false);
    }
  };

  const getAssignedFoods = (
    categoryType: "protein" | "carbs" | "other"
  ): string[] => {
    return categories
      .filter(
        (category) =>
          (categoryType === "protein" && category.name === "Protein") ||
          (categoryType === "carbs" && category.name === "Carbs") ||
          (categoryType === "other" && category.name === "Other")
      )
      .flatMap((category) => category.options.map((option) => option.foodId));
  };

  // Get category counts
  const proteinOptions =
    categories.find((cat) => cat.name === "Protein")?.options || [];
  const carbOptions =
    categories.find((cat) => cat.name === "Carbs")?.options || [];
  const otherOptions =
    categories.find((cat) => cat.name === "Other")?.options || [];

  const totalFoodCount =
    proteinOptions.length + carbOptions.length + otherOptions.length;

  return (
    <div className="bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-700">
      {/* Compact Header */}
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          {/* Meal Name */}
          {isEditing ? (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={editedName}
                onChange={(e) => setEditedName(e.target.value)}
                className="px-3 py-2 border-2 border-purple-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-purple-500 text-lg font-semibold bg-gray-800 text-gray-200"
                placeholder="שם הארוחה"
              />
              <button
                onClick={handleNameSave}
                className="p-2 rounded-lg hover:bg-green-900 transition-colors duration-200"
                title="שמור"
              >
                <Check className="h-4 w-4 text-green-400" />
              </button>
              <button
                onClick={() => {
                  setIsEditing(false);
                  setEditedName(mealName);
                }}
                className="p-2 rounded-lg hover:bg-red-900 transition-colors duration-200"
                title="בטל"
              >
                <X className="h-4 w-4 text-red-400" />
              </button>
            </div>
          ) : (
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-900 rounded-lg">
                <Utensils className="h-5 w-5 text-purple-300" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-200">
                  {editedName}
                </h3>
                <div className="text-xs text-gray-400 flex items-center gap-1 mt-1">
                  <Info className="h-3 w-3" />
                  <span>{totalFoodCount} פריטי מזון</span>
                </div>
              </div>
              <button
                onClick={() => setIsEditing(true)}
                className="p-1 rounded-lg hover:bg-gray-700 transition-colors duration-200 ml-2"
                title="ערוך"
              >
                <Edit2 className="h-4 w-4 text-gray-400" />
              </button>
            </div>
          )}

          {/* Expand/Collapse Button */}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-2 bg-purple-900 hover:bg-purple-800 rounded-lg transition-colors duration-200"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 text-purple-300" />
            ) : (
              <ChevronDown className="h-4 w-4 text-purple-300" />
            )}
          </button>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="p-4 bg-gradient-to-t from-gray-800 to-gray-900">
          {/* Food Selectors */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div>
              <h4 className="text-xs font-medium text-gray-300 mb-2 text-center">
                חלבונים{" "}
                {proteinOptions.length > 0 && `(${proteinOptions.length})`}
              </h4>
              <FoodCategorySelector
                category="protein"
                onFoodSelect={(food) => handleFoodAdd("protein", food)}
                assignedFoods={getAssignedFoods("protein")}
                foodItems={foodItems}
              />
            </div>
            <div>
              <h4 className="text-xs font-medium text-gray-300 mb-2 text-center">
                פחמימות {carbOptions.length > 0 && `(${carbOptions.length})`}
              </h4>
              <FoodCategorySelector
                category="carbs"
                onFoodSelect={(food) => handleFoodAdd("carbs", food)}
                assignedFoods={getAssignedFoods("carbs")}
                foodItems={foodItems}
              />
            </div>
            <div>
              <h4 className="text-xs font-medium text-gray-300 mb-2 text-center">
                אחר {otherOptions.length > 0 && `(${otherOptions.length})`}
              </h4>
              <FoodCategorySelector
                category="other"
                onFoodSelect={(food) => handleFoodAdd("other", food)}
                assignedFoods={getAssignedFoods("other")}
                foodItems={foodItems}
              />
            </div>
          </div>

          {/* Food Lists */}
          <div className="space-y-3 mb-4">
            {/* Protein Foods */}
            {proteinOptions.length > 0 && (
              <div>
                <div className="text-sm font-medium text-gray-300 px-2 py-1 bg-purple-900 rounded-t-lg mb-1">
                  חלבונים
                </div>
                <div className="space-y-1">
                  {categories
                    .filter((category) => category.name === "Protein")
                    .map((category) => {
                      // Find the actual index in the original array
                      const actualCategoryIndex = categories.findIndex(
                        (c) => c.name === "Protein"
                      );

                      return category.options.map((option, optionIndex) => {
                        const food = option.food;
                        if (!food) return null;

                        return (
                          <FoodSelector
                            key={`carb-${optionIndex}`}
                            food={food}
                            amount={option.amount}
                            onAmountChange={(amount) =>
                              handleFoodUpdate(
                                actualCategoryIndex, // Use the actual index
                                optionIndex,
                                amount
                              )
                            }
                            onRemove={() =>
                              handleFoodRemove(actualCategoryIndex, optionIndex)
                            }
                          />
                        );
                      });
                    })}
                </div>
              </div>
            )}

            {/* Carb Foods */}
            {carbOptions.length > 0 && (
              <div>
                <div className="text-sm font-medium text-gray-300 px-2 py-1 bg-amber-800 rounded-t-lg mb-1">
                  פחמימות
                </div>
                <div className="space-y-1">
                  {categories
                    .filter((category) => category.name === "Carbs")
                    .map((category) => {
                      // Find the actual index in the original array
                      const actualCategoryIndex = categories.findIndex(
                        (c) => c.name === "Carbs"
                      );

                      return category.options.map((option, optionIndex) => {
                        const food = option.food;
                        if (!food) return null;

                        return (
                          <FoodSelector
                            key={`carb-${optionIndex}`}
                            food={food}
                            amount={option.amount}
                            onAmountChange={(amount) =>
                              handleFoodUpdate(
                                actualCategoryIndex, // Use the actual index
                                optionIndex,
                                amount
                              )
                            }
                            onRemove={() =>
                              handleFoodRemove(actualCategoryIndex, optionIndex)
                            }
                          />
                        );
                      });
                    })}
                </div>
              </div>
            )}

            {/* Other Foods */}
            {otherOptions.length > 0 && (
              <div>
                <div className="text-sm font-medium text-gray-300 px-2 py-1 bg-green-900 rounded-t-lg mb-1">
                  אחר
                </div>
                <div className="space-y-1">
                  {categories
                    .filter((category) => category.name === "Other")
                    .map((category) => {
                      // Find the actual index in the original array
                      const actualCategoryIndex = categories.findIndex(
                        (c) => c.name === "Other"
                      );

                      return category.options.map((option, optionIndex) => {
                        const food = option.food;
                        if (!food) return null;

                        return (
                          <FoodSelector
                            key={`carb-${optionIndex}`}
                            food={food}
                            amount={option.amount}
                            onAmountChange={(amount) =>
                              handleFoodUpdate(
                                actualCategoryIndex, // Use the actual index
                                optionIndex,
                                amount
                              )
                            }
                            onRemove={() =>
                              handleFoodRemove(actualCategoryIndex, optionIndex)
                            }
                          />
                        );
                      });
                    })}
                </div>
              </div>
            )}
          </div>

          {/* Compact Macro Display */}
          <div className="bg-gray-800 rounded-lg p-3 border border-gray-700">
            <div className="flex flex-wrap gap-2 justify-center">
              <span className="text-sm text-white bg-purple-900 rounded-full px-2 py-1 font-medium">
                חלבון: {Math.round(calculateMacroRange(categories).min.protein)}
                -{Math.round(calculateMacroRange(categories).max.protein)}g
              </span>

              <span className="text-sm text-white bg-amber-800 rounded-full px-2 py-1 font-medium">
                פחמימות: {Math.round(calculateMacroRange(categories).min.carbs)}
                -{Math.round(calculateMacroRange(categories).max.carbs)}g
              </span>

              <span className="text-sm text-white bg-green-900 rounded-full px-2 py-1 font-medium">
                שומן: {Math.round(calculateMacroRange(categories).min.fats)}-
                {Math.round(calculateMacroRange(categories).max.fats)}g
              </span>

              <span className="text-sm text-white bg-red-900 rounded-full px-2 py-1 font-medium">
                קלוריות:{" "}
                {Math.round(calculateMacroRange(categories).min.calories)}-
                {Math.round(calculateMacroRange(categories).max.calories)}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MealEditor;
