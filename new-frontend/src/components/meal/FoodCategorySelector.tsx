import React, { useState } from "react";
import { FoodItem } from "../../types/food";
import { Plus } from "lucide-react";
import { CustomFoodInput } from "./CustomFoodInput";

interface FoodCategorySelectorProps {
  category: string;
  onFoodSelect: (food: FoodItem) => void;
  assignedFoods?: string[];
  foodItems: FoodItem[];
}

export const FoodCategorySelector: React.FC<FoodCategorySelectorProps> = ({
  category,
  onFoodSelect,
  assignedFoods = [],
  foodItems,
}) => {
  const [showCustomInput, setShowCustomInput] = useState(false);

  const getFoodsByCategory = (category: string): FoodItem[] | null => {
    if (foodItems && Array.isArray(foodItems) && foodItems.length > 0) {
      const standardFoods = foodItems.filter(
        (food) => food.category === category
      );
      return [...standardFoods].sort((a, b) => a.name.localeCompare(b.name));
    } else {
      return null;
    }
  };

  const foods = getFoodsByCategory(category);

  const handleCustomFoodSave = (customFood: {
    name: string;
    category: "protein" | "carbs" | "other";
    macros: {
      protein: number;
      carbs: number;
      fats: number;
      calories: number;
    };
    amount: number;
  }) => {
    // const newFood: FoodItem = {
    //   id: `custom-${Date.now()}`,
    //   name: customFood.name,
    //   category: customFood.category,
    //   macrosPer100g: {
    //     protein: (customFood.macros.protein * 100) / customFood.amount,
    //     carbs: (customFood.macros.carbs * 100) / customFood.amount,
    //     fats: (customFood.macros.fats * 100) / customFood.amount,
    //     calories: (customFood.macros.calories * 100) / customFood.amount,
    //   },
    //   defaultServing: customFood.amount,
    //   minServing: Math.max(10, customFood.amount - 50),
    //   maxServing: customFood.amount + 50,
    // };

    // // Add to database and get the saved food with proper ID
    // const savedFood = addCustomFood("1", newFood); // Using '1' as default user ID
    // onFoodSelect(savedFood);
    setShowCustomInput(false);
  };

  return (
    <div className="relative">
      <div className="flex items-center gap-2">
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <Plus className="h-4 w-4 text-purple-400" />
        </div>
        <select
          className="w-full p-2 pr-8 pl-4 border border-gray-700 rounded-lg appearance-none bg-gray-800 text-gray-300 cursor-pointer hover:border-purple-600 transition-colors duration-200 focus:border-purple-500 focus:ring focus:ring-purple-700 focus:ring-opacity-50"
          onChange={(e) => {
            if (e.target.value === "custom") {
              setShowCustomInput(true);
            } else {
              const food = foods && foods.find((f) => f.id === e.target.value);
              if (food) onFoodSelect(food);
            }
            e.target.value = "";
          }}
          defaultValue=""
        >
          <option value="" disabled className="bg-gray-800 text-gray-400">
            {category === "protein"
              ? "הוסף חלבון"
              : category === "carbs"
              ? "הוסף פחמימה"
              : "הוסף מזון"}
          </option>
          <option value="custom" className="text-purple-400 bg-gray-800">
            ➕ הוסף מזון מותאם אישית
          </option>
          <optgroup label="מזונות קיימים" className="bg-gray-800 text-gray-300">
            {foods &&
              foods.map((food) => {
                const isAssigned = assignedFoods.includes(food.id);
                return (
                  <option
                    key={food.id}
                    value={food.id}
                    disabled={isAssigned}
                    className={
                      isAssigned
                        ? "text-gray-500 bg-gray-700"
                        : "text-gray-300 bg-gray-800"
                    }
                  >
                    {food.name} {isAssigned ? "(מוקצה)" : ""}
                  </option>
                );
              })}
          </optgroup>
        </select>
      </div>

      {showCustomInput && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="max-w-2xl w-full">
            <CustomFoodInput
              onSave={handleCustomFoodSave}
              onClose={() => setShowCustomInput(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
};
