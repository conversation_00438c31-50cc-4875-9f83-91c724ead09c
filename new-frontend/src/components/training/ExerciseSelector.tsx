import React from "react";
import { ExerciseOption, exerciseCategories } from "../../types/training";
import { Dumbbell } from "lucide-react";
import { CustomExerciseType } from "../../types/exercise";

interface ExerciseSelectorProps {
  onExerciseSelect: (exercise: ExerciseOption) => void;
  exercises: CustomExerciseType[];
}

export const ExerciseSelector: React.FC<ExerciseSelectorProps> = ({
  onExerciseSelect,
  exercises,
}) => {
  const hebrewCategories = {
    chest: "חזה",
    back: "גב",
    legs: "רגליים",
    shoulders: "כתפיים",
    arms: "ידיים",
    abs: "בטן",
    cardio: "קרדיו",
  };

  const categoryIcons = {
    chest: "💪",
    back: "🏋️",
    legs: "🦵",
    shoulders: "🏆",
    arms: "💪",
    abs: "🏅",
    cardio: "🏃",
  };

  const getExercisesByCategory = (category: string): CustomExerciseType[] => {
    return exercises.filter((exercise) => exercise.category === category);
  };

  return (
    <div className="max-h-[60vh] overflow-y-auto pr-1 text-gray-200">
      <div className="grid grid-cols-3 sm:grid-cols-4 gap-2 mb-3">
        {Object.entries(exerciseCategories).map(([category, _]) => (
          <a
            key={category}
            href={`#category-${category}`}
            className="flex items-center justify-center p-2 bg-purple-900 hover:bg-purple-800 rounded-lg transition-colors duration-200 no-underline"
          >
            <div className="flex flex-col items-center text-center text-gray-200">
              <span className="text-xl mb-1">
                {categoryIcons[category as keyof typeof categoryIcons]}
              </span>
              <span className="text-xs font-medium">
                {hebrewCategories[category as keyof typeof hebrewCategories]}
              </span>
            </div>
          </a>
        ))}
      </div>

      <div className="space-y-3">
        {Object.entries(exerciseCategories).map(([category, _]) => (
          <div
            key={category}
            id={`category-${category}`}
            className="scroll-mt-3"
          >
            <div className="flex items-center gap-1 bg-purple-900 text-gray-200 px-2 py-1 rounded-lg mb-2 sticky top-0 z-10">
              <span className="text-base mr-1">
                {categoryIcons[category as keyof typeof categoryIcons]}
              </span>
              <h4 className="font-bold text-sm">
                {hebrewCategories[category as keyof typeof hebrewCategories]}
              </h4>
            </div>

            <div className="grid grid-cols-2 gap-2">
              {getExercisesByCategory(category).map((exercise) => (
                <button
                  key={exercise.id}
                  className="flex items-center gap-1 p-2 bg-gray-800 rounded-lg border border-gray-700 hover:border-purple-700 hover:bg-gray-700 text-left text-sm"
                  onClick={() => onExerciseSelect(exercise)}
                >
                  <Dumbbell className="h-3 w-3 text-purple-400 flex-shrink-0" />
                  <span className="line-clamp-2 text-gray-300">
                    {exercise.name}
                  </span>
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
