import React, { useEffect, useState } from "react";
import { TrainingDay, Exercise, ExerciseOption } from "../../types/training";
import { ExerciseEditor } from "./ExerciseEditor";
import { ExerciseSelector } from "./ExerciseSelector";
import {
  Calendar,
  ChevronDown,
  ChevronUp,
  Edit2,
  Check,
  X,
  Plus,
  Trash2,
  Save,
  FolderOpen,
  Info,
  Clock,
} from "lucide-react";
import { WorkoutTemplateManager } from "./WorkoutTemplateManager";
import { CustomExerciseType } from "../../types/exercise";

interface TrainingDayEditorProps {
  day: TrainingDay;
  onUpdate: (updatedDay: TrainingDay) => void;
  onDelete: () => void;
  onAddDay?: () => void;
  exercises: CustomExerciseType[];
}

export const TrainingDayEditor: React.FC<TrainingDayEditorProps> = ({
  day,
  onUpdate,
  onDelete,
  onAddDay,
  exercises,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedDay, setEditedDay] = useState("");
  const [editedFocus, setEditedFocus] = useState("");
  const [showExerciseSelector, setShowExerciseSelector] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showSaveTemplateModal, setShowSaveTemplateModal] = useState(false);
  const [showTemplatesManager, setShowTemplatesManager] = useState(false);
  const [templateName, setTemplateName] = useState("");
  const [templateDescription, setTemplateDescription] = useState("");
  const [showTemplateSuccess, setShowTemplateSuccess] = useState(false);
  const [selectedExerciseIndex, setSelectedExerciseIndex] = useState<
    number | null
  >(null);
  const [expandedExerciseIndex, setExpandedExerciseIndex] = useState<
    number | null
  >(null);
  //   const { saveTemplate } = useWorkoutTemplate();

  useEffect(() => {
    setEditedDay(day.day);
    setEditedFocus(day.focus);
  }, [day.day, day.focus]);

  const handleExerciseAdd = (exercise: ExerciseOption) => {
    const newExercise: Exercise = {
      exerciseId: exercise.id,
      name: exercise.name,
      sets: exercise.defaultSets,
      reps: exercise.defaultReps,
      rest: exercise.defaultRest,
      instructions: exercise.instructions,
    };

    const newExercises = [...day.exercises];
    if (selectedExerciseIndex !== null) {
      newExercises.splice(selectedExerciseIndex + 1, 0, newExercise);
    } else {
      newExercises.push(newExercise);
    }

    onUpdate({
      ...day,
      exercises: newExercises,
    });

    setShowExerciseSelector(false);
    setSelectedExerciseIndex(null);
  };

  const handleExerciseUpdate = (index: number, updatedExercise: Exercise) => {
    const newExercises = [...day.exercises];
    newExercises[index] = updatedExercise;

    onUpdate({
      ...day,
      exercises: newExercises,
    });
  };

  const handleExerciseDelete = (index: number) => {
    onUpdate({
      ...day,
      exercises: day.exercises.filter((_, i) => i !== index),
    });
  };

  const handleSave = () => {
    onUpdate({
      ...day,
      day: editedDay,
      focus: editedFocus,
    });

    setIsEditing(false);
  };

  const handleSaveAsTemplate = () => {
    if (!templateName.trim()) return;

    // saveTemplate(templateName, {
    //   ...day,
    //   day: editedDay,
    //   focus: editedFocus,
    // });
    setShowSaveTemplateModal(false);
    setTemplateName("");
    setTemplateDescription("");
    setShowTemplateSuccess(true);

    setTimeout(() => {
      setShowTemplateSuccess(false);
    }, 3000);
  };

  const handleSelectTemplate = (template: {
    id: string;
    name: string;
    day: TrainingDay;
  }) => {
    onUpdate({
      ...template.day,
      day: day.day, // Preserve current day name
      focus: template.day.focus,
    });
    setShowTemplatesManager(false);
  };

  const toggleExerciseExpand = (index: number) => {
    if (expandedExerciseIndex === index) {
      setExpandedExerciseIndex(null);
    } else {
      setExpandedExerciseIndex(index);
    }
  };

  return (
    <div className="mb-10 relative">
      {/* Simplified header card */}
      <div className="bg-gray-900 rounded-xl shadow-md overflow-hidden border-2 border-purple-800 hover:border-purple-700 transition-all duration-200">
        {/* Success message */}
        {showTemplateSuccess && (
          <div className="absolute top-3 right-1/2 transform translate-x-1/2 z-50 bg-green-900 border border-green-700 text-green-200 px-4 py-2 rounded-lg shadow-md flex items-center gap-2">
            <Check className="h-4 w-4" />
            <span>התבנית נשמרה בהצלחה!</span>
          </div>
        )}

        {/* Simple day header */}
        <div className="bg-gray-800 p-4 border-b border-gray-700">
          <div className="flex items-center justify-between">
            {/* Day info */}
            {isEditing ? (
              <div className="flex gap-3 items-center">
                <div className="flex-1 flex gap-2 items-center">
                  <input
                    type="text"
                    value={editedDay}
                    onChange={(e) => setEditedDay(e.target.value)}
                    className="w-32 px-2 py-1 border border-purple-700 rounded-lg text-lg font-medium bg-gray-800 text-gray-200"
                    placeholder="יום"
                  />
                  <span className="text-lg text-gray-400">-</span>
                  <input
                    type="text"
                    value={editedFocus}
                    onChange={(e) => setEditedFocus(e.target.value)}
                    className="w-40 px-2 py-1 border border-purple-700 rounded-lg text-lg font-medium bg-gray-800 text-gray-200"
                    placeholder="מיקוד"
                  />
                  <div className="flex items-center ml-2">
                    <button
                      onClick={handleSave}
                      className="p-1 rounded-lg bg-green-900 text-green-400 hover:bg-green-800"
                    >
                      <Check className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => {
                        setIsEditing(false);
                        setEditedDay(day.day);
                        setEditedFocus(day.focus);
                      }}
                      className="p-1 rounded-lg bg-red-900 text-red-400 hover:bg-red-800 ml-1"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-900 rounded-lg">
                  <Calendar className="h-5 w-5 text-purple-300" />
                </div>
                <h3 className="text-xl font-bold text-gray-200">
                  {day.day} - {day.focus}
                </h3>
                <button
                  onClick={() => setIsEditing(true)}
                  className="p-1 rounded-lg bg-gray-700 hover:bg-gray-600"
                  title="ערוך יום"
                >
                  <Edit2 className="h-4 w-4 text-gray-300" />
                </button>
              </div>
            )}

            {/* Simple action buttons */}
            <div className="flex items-center gap-2">
              <div className="flex bg-gray-700 p-1 rounded-lg border border-gray-600">
                <button
                  onClick={() => setShowSaveTemplateModal(true)}
                  className="text-xs p-1 text-purple-300 hover:bg-gray-600 rounded"
                  title="שמור כתבנית"
                >
                  <Save className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setShowTemplatesManager(true)}
                  className="text-xs p-1 text-green-300 hover:bg-gray-600 rounded"
                  title="טען תבנית"
                >
                  <FolderOpen className="h-4 w-4" />
                </button>
              </div>

              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-2 bg-purple-900 hover:bg-purple-800 rounded-lg"
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4 text-purple-300" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-purple-300" />
                )}
              </button>
            </div>
          </div>

          {/* Exercise count indicator */}
          <div className="mt-2 flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-400">
              <Info className="h-3.5 w-3.5 mr-1" />
              <span>{day.exercises.length} תרגילים</span>
            </div>
            {day.exercises.length > 0 && (
              <div className="text-xs text-purple-400">
                {isExpanded
                  ? "לחץ על שם תרגיל כדי לראות פרטים נוספים"
                  : "לחץ על החץ למטה כדי לראות תרגילים"}
              </div>
            )}
          </div>
        </div>

        {/* Exercise List - COMPACT VERSION */}
        {isExpanded && (
          <div className="p-4 bg-gradient-to-t from-gray-800 to-gray-900">
            {day.exercises.length === 0 ? (
              <div className="py-6 bg-gray-800 rounded-lg border border-dashed border-purple-700 flex flex-col items-center justify-center gap-4">
                <div className="bg-purple-800 text-gray-200 p-2 rounded-full shadow-md">
                  <Plus className="h-5 w-5" />
                </div>
                <div className="text-center">
                  <h3 className="font-bold text-purple-300 mb-1">
                    אין תרגילים עדיין
                  </h3>
                  <button
                    onClick={() => {
                      setSelectedExerciseIndex(null);
                      setShowExerciseSelector(true);
                    }}
                    className="bg-purple-700 hover:bg-purple-600 text-gray-200 rounded-lg py-2 px-4 font-medium text-sm shadow-sm flex items-center gap-1 mx-auto mt-2"
                  >
                    <Plus className="h-4 w-4" />
                    <span>הוסף תרגיל ראשון</span>
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {/* Compact Exercise List */}
                <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-sm overflow-hidden">
                  <div className="divide-y divide-gray-700">
                    {day.exercises.map((exercise, index) => (
                      <div key={index} className="group/exercise">
                        <div
                          className={`p-2 hover:bg-gray-700 transition-colors duration-200 ${
                            expandedExerciseIndex === index ? "bg-gray-700" : ""
                          }`}
                        >
                          <div className="grid grid-cols-12 gap-2 items-center">
                            <div className="col-span-5 font-medium text-gray-200 flex items-center gap-1">
                              <button
                                onClick={() => toggleExerciseExpand(index)}
                                className="p-1 rounded-full hover:bg-purple-900"
                              >
                                {expandedExerciseIndex === index ? (
                                  <ChevronUp className="h-3 w-3 text-purple-300" />
                                ) : (
                                  <ChevronDown className="h-3 w-3 text-gray-400" />
                                )}
                              </button>
                              <span className="truncate">{exercise.name}</span>
                            </div>
                            <div className="col-span-2 text-center bg-purple-900 rounded p-0.5 text-purple-200 text-xs">
                              {exercise.sets}
                            </div>
                            <div className="col-span-2 text-center bg-green-900 rounded p-0.5 text-green-200 text-xs">
                              {exercise.reps}
                            </div>
                            <div className="col-span-2 text-center bg-blue-900 rounded p-0.5 text-blue-200 text-xs flex items-center justify-center">
                              <Clock className="h-3 w-3 mr-0.5" />
                              {exercise.rest.replace(" seconds rest", "")}
                            </div>
                            <div className="col-span-1 flex justify-end gap-1">
                              <button
                                onClick={() => setExpandedExerciseIndex(index)}
                                className="p-1 bg-gray-700 rounded hover:bg-gray-600"
                                title="ערוך"
                              >
                                <Edit2 className="h-3 w-3 text-gray-300" />
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Expanded Exercise View */}
                        {expandedExerciseIndex === index && (
                          <div className="p-4 bg-gray-800 border-t border-gray-700">
                            <ExerciseEditor
                              exercise={exercise}
                              onUpdate={(updatedExercise) =>
                                handleExerciseUpdate(index, updatedExercise)
                              }
                              onDelete={() => handleExerciseDelete(index)}
                            />
                          </div>
                        )}

                        {/* Add exercise between exercises button */}
                        <div className="h-0 relative flex justify-center opacity-0 group-hover/exercise:opacity-100 transition-opacity duration-300">
                          <button
                            onClick={() => {
                              setSelectedExerciseIndex(index);
                              setShowExerciseSelector(true);
                            }}
                            className="absolute -top-3 z-10 bg-purple-700 hover:bg-purple-600 text-gray-200 py-1 px-2 rounded-full
                              shadow-sm text-xs flex items-center gap-1"
                          >
                            <Plus className="h-2.5 w-2.5" />
                            <span>הוסף</span>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Add exercise at the end - simple button */}
                <button
                  onClick={() => {
                    setSelectedExerciseIndex(null);
                    setShowExerciseSelector(true);
                  }}
                  className="w-full py-2 bg-purple-800 hover:bg-purple-700 text-gray-200 rounded-lg text-sm shadow-sm flex items-center justify-center gap-1"
                >
                  <Plus className="h-4 w-4" />
                  <span>הוסף תרגיל חדש</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Add Workout Day Button - Simplified */}
      {onAddDay && (
        <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 z-10">
          <button
            onClick={onAddDay}
            className="flex items-center gap-1 px-4 py-1.5 bg-purple-700 text-gray-200 rounded-full shadow-sm hover:bg-purple-600 text-sm font-medium"
          >
            <Plus className="h-4 w-4" />
            <span>הוסף יום</span>
          </button>
        </div>
      )}

      {/* Delete Button - Simplified */}
      <button
        onClick={() => setShowDeleteConfirm(true)}
        className="absolute -top-2 -right-2 p-2 bg-red-700 text-gray-200 rounded-full shadow-sm hover:bg-red-600 z-10"
        title="מחק יום אימון"
      >
        <Trash2 className="h-4 w-4" />
      </button>

      {/* Exercise Selector Modal - Simplified */}
      {showExerciseSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-lg w-full mx-4 shadow-lg border-2 border-purple-800">
            <div className="p-4 border-b border-gray-700 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-900 rounded-lg">
                  <Plus className="h-5 w-5 text-purple-300" />
                </div>
                <h3 className="font-bold text-gray-200">בחר תרגיל</h3>
              </div>
              <button
                onClick={() => {
                  setShowExerciseSelector(false);
                  setSelectedExerciseIndex(null);
                }}
                className="p-1 hover:bg-red-900 rounded transition-colors duration-200"
              >
                <X className="h-5 w-5 text-red-400" />
              </button>
            </div>

            <div className="p-4">
              <ExerciseSelector
                exercises={exercises}
                onExerciseSelect={handleExerciseAdd}
              />
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal - Simplified */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg p-6 max-w-sm w-full mx-4 border-2 border-red-800">
            <div className="text-center mb-4">
              <div className="mx-auto bg-red-900 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-3">
                <Trash2 className="h-8 w-8 text-red-300" />
              </div>
              <h3 className="text-xl font-bold text-gray-200 mb-1">
                למחוק יום אימון?
              </h3>
              <p className="text-sm text-gray-400">
                האם אתה בטוח שברצונך למחוק את יום האימון? פעולה זו אינה הפיכה.
              </p>
            </div>

            <div className="flex justify-center gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 text-gray-300 hover:bg-gray-800 rounded-lg transition-colors font-medium"
              >
                ביטול
              </button>
              <button
                onClick={() => {
                  onDelete();
                  setShowDeleteConfirm(false);
                }}
                className="px-4 py-2 bg-red-700 text-gray-200 rounded-lg hover:bg-red-600 transition-colors font-medium"
              >
                כן, מחק
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Save As Template Modal - Simplified */}
      {showSaveTemplateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg p-6 max-w-md w-full mx-4 border-2 border-purple-800">
            <div className="text-center mb-4">
              <div className="mx-auto bg-purple-900 p-3 rounded-full w-16 h-16 flex items-center justify-center mb-3">
                <Save className="h-8 w-8 text-purple-300" />
              </div>
              <h3 className="text-xl font-bold text-gray-200 mb-1">
                שמור כתבנית
              </h3>
              <p className="text-sm text-gray-400">
                שמור את יום האימון כדי שתוכל להשתמש בו שוב בקלות
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  שם התבנית:
                </label>
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="תבנית חדשה"
                  className="w-full p-2 border border-gray-700 bg-gray-800 rounded-lg text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-700"
                  dir="rtl"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  תיאור (לא חובה):
                </label>
                <textarea
                  value={templateDescription}
                  onChange={(e) => setTemplateDescription(e.target.value)}
                  placeholder="תיאור קצר..."
                  className="w-full p-2 border border-gray-700 bg-gray-800 rounded-lg h-20 text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-700"
                  dir="rtl"
                ></textarea>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowSaveTemplateModal(false)}
                  className="px-4 py-2 text-gray-400 hover:bg-gray-800 rounded-lg transition-colors font-medium"
                >
                  ביטול
                </button>
                <button
                  onClick={handleSaveAsTemplate}
                  disabled={!templateName.trim()}
                  className="px-4 py-2 bg-purple-700 text-gray-200 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                >
                  שמור
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Template Manager Modal */}
      {showTemplatesManager && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="max-w-4xl w-full max-h-[90vh]">
            <WorkoutTemplateManager
              onClose={() => setShowTemplatesManager(false)}
              onSelectTemplate={handleSelectTemplate}
            />
          </div>
        </div>
      )}
    </div>
  );
};
