import React, { useEffect, useState } from "react";
import {
  X,
  Edit2,
  Trash2,
  Copy,
  Search,
  Clock,
  Calendar,
  Check,
  CalendarDays,
  Info,
} from "lucide-react";
import { WorkoutPlanTemplate } from "../../types/training";
import Loader_1 from "../Loader/Loader_1";
import {
  useDeleteTrainingPlanTemplateMutation,
  useGetAllTrainingPlanTemplateQuery,
  useUpdateTrainingPlanTemplateMutation,
} from "../../api/services/Trainer/TrainerService";

interface WorkoutPlanTemplateManagerProps {
  onClose: () => void;
  onSelectTemplate: (template: WorkoutPlanTemplate) => void;
}

export const WorkoutPlanTemplateManager: React.FC<
  WorkoutPlanTemplateManagerProps
> = ({ onClose, onSelectTemplate }) => {
  const [trainingPlanTemplates, setTrainingPlanTemplates] = useState<
    WorkoutPlanTemplate[]
  >([]);

  const {
    data: templatesListData,
    refetch,
    isLoading: isTemplateListLoading,
  } = useGetAllTrainingPlanTemplateQuery({});

  useEffect(() => {
    if (templatesListData) {
      setTrainingPlanTemplates(templatesListData);
    }
  }, [templatesListData]);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const [searchTerm, setSearchTerm] = useState("");
  const [editingTemplate, setEditingTemplate] = useState<{
    id: string;
    name: string;
    description?: string;
  } | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(
    null
  );
  const [expandedTemplateId, setExpandedTemplateId] = useState<string | null>(
    null
  );

  const [updateTrainingPlanTemplate] = useUpdateTrainingPlanTemplateMutation();

  const handleSaveEdit = async (
    id: string,
    newName: string,
    description?: string
  ) => {
    const template = trainingPlanTemplates.find((t) => t.id === id);
    if (!template) return;

    try {
      const updatedTemplate = {
        name: newName,
        description: description || template.description,
      };

      await updateTrainingPlanTemplate({
        body: updatedTemplate,
        templateId: id,
      }).unwrap();

      // Update local state
      setTrainingPlanTemplates((prev) =>
        prev.map((template) =>
          template.id === id
            ? {
                ...template,
                name: newName,
                description: description || template.description,
              }
            : template
        )
      );
      setEditingTemplate(null);
    } catch (error) {
      console.error("Failed to update template:", error);
    }
  };

  const [deleteTrainingPlanTemplate] = useDeleteTrainingPlanTemplateMutation();

  const handleDeleteConfirm = async (id: string) => {
    try {
      await deleteTrainingPlanTemplate(id).unwrap();

      setTrainingPlanTemplates((prev) =>
        prev.filter((template) => template.id !== id)
      );

      setShowDeleteConfirm(null);
    } catch (error) {
      console.error("Failed to delete template:", error);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("he-IL", {
      year: "numeric",
      month: "numeric",
      day: "numeric",
    });
  };

  const filteredTemplates = trainingPlanTemplates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (template.description &&
        template.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Fun emoji for each plan based on its id
  const getTemplateEmoji = (id: string) => {
    const emojis = ["🏋️", "💪", "🏆", "🥇", "⚡", "🔥", "🚀", "⭐"];
    const hash = id
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return emojis[hash % emojis.length];
  };

  return (
    <div className="bg-gray-900 rounded-xl shadow-lg overflow-hidden max-h-[85vh] flex flex-col border-2 border-purple-800">
      {/* Fun Header */}
      <div className="bg-gradient-to-r from-purple-900 via-gray-800 to-purple-900 p-6 border-b border-purple-800 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-full bg-purple-700 shadow-md">
            <CalendarDays className="h-8 w-8 text-purple-200" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-200">
              תבניות אימון שבועיות
            </h3>
            <p className="text-gray-400">בחר תוכנית אימונים מוכנה</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-700 rounded-full transition-colors duration-200"
        >
          <X className="h-6 w-6 text-gray-400" />
        </button>
      </div>

      {/* Search - Big and Clear */}
      <div className="p-6 border-b border-gray-800">
        <div className="relative">
          <Search className="absolute right-5 top-1/2 transform -translate-y-1/2 h-6 w-6 text-gray-500" />
          <input
            type="text"
            placeholder="חפש תבנית אימון..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-4 pr-14 py-3 border-2 border-gray-700 bg-gray-800 rounded-full focus:border-purple-600 focus:ring focus:ring-purple-800 focus:ring-opacity-50 text-lg text-gray-200"
            dir="rtl"
          />
        </div>
      </div>

      {/* Template Cards - Fun Design */}
      <div className="overflow-y-auto flex-grow p-6 bg-gray-900">
        {filteredTemplates.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-800 p-4 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-4">
              <Calendar className="h-12 w-12 text-gray-600" />
            </div>
            <h4 className="text-2xl font-bold text-gray-400 mb-2">
              אין תבניות עדיין
            </h4>
            <p className="text-gray-500 max-w-md mx-auto">
              {searchTerm
                ? "לא נמצאו תוכניות אימון התואמות לחיפוש"
                : "שמור תוכנית אימונים כדי שתופיע כאן לשימוש חוזר"}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredTemplates.map((template) => (
              <div
                key={template.id}
                className="bg-gray-800 rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-200 border-2 border-gray-700 hover:border-purple-700 relative"
              >
                {editingTemplate?.id === template.id ? (
                  // Edit mode - simplified
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-2">
                      <label className="text-base font-medium text-gray-300 w-24">
                        שם תבנית:
                      </label>
                      <input
                        type="text"
                        value={editingTemplate.name}
                        onChange={(e) =>
                          setEditingTemplate({
                            ...editingTemplate,
                            name: e.target.value,
                          })
                        }
                        className="flex-1 p-3 border-2 border-purple-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-800 focus:ring-opacity-50 text-lg font-medium text-gray-200"
                        autoFocus
                        dir="rtl"
                      />
                    </div>

                    <div className="flex items-start gap-2 mb-2">
                      <label className="text-base font-medium text-gray-300 w-24">
                        תיאור:
                      </label>
                      <input
                        value={editingTemplate.description || ""}
                        onChange={(e) =>
                          setEditingTemplate({
                            ...editingTemplate,
                            description: e.target.value,
                          })
                        }
                        className="flex-1 p-3 border-2 border-purple-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-800 focus:ring-opacity-50 text-lg font-medium text-gray-200"
                        dir="rtl"
                      />
                    </div>

                    <div className="flex justify-center gap-4">
                      <button
                        onClick={() =>
                          handleSaveEdit(
                            template.id,
                            editingTemplate.name,
                            editingTemplate.description
                          )
                        }
                        className="px-6 py-2 bg-green-800 text-gray-200 rounded-full hover:bg-green-700 transition-colors duration-200 shadow-sm hover:shadow-md font-medium flex items-center gap-2"
                      >
                        <Check className="h-5 w-5" />
                        <span>שמור</span>
                      </button>
                      <button
                        onClick={() => setEditingTemplate(null)}
                        className="px-6 py-2 bg-gray-700 text-gray-300 rounded-full hover:bg-gray-600 transition-colors duration-200 shadow-sm hover:shadow-md font-medium flex items-center gap-2"
                      >
                        <X className="h-5 w-5" />
                        <span>בטל</span>
                      </button>
                    </div>
                  </div>
                ) : (
                  // View mode - fun and friendly
                  <>
                    <div className="flex items-center gap-3 mb-3 w-100">
                      <div className="p-2 bg-purple-900 rounded-full w-12 h-12 flex items-center justify-center text-2xl">
                        {getTemplateEmoji(template.id)}
                      </div>

                      <div>
                        <div className="w-100 grid grid-cols-[7fr_2fr] gap-2">
                          <h4 className="text-xl font-bold text-gray-200">
                            {template.name}
                          </h4>

                          <div>
                            <div className=" bg-purple-900 px-3 py-1 rounded-full text-sm font-medium text-purple-200">
                              {template.templateData.length} ימים
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-400">
                          <Clock className="h-4 w-4" />
                          <span>{formatDate(template.createdAt)}</span>
                        </div>
                      </div>
                    </div>

                    {template.description && (
                      <div className="bg-gray-700 p-3 rounded-lg mb-3 text-gray-300 flex items-start gap-2">
                        <Info className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                        <p>{template.description}</p>
                      </div>
                    )}

                    {/* Plan Summary - shown on expand */}
                    <div
                      className={`space-y-2 mt-3 ${
                        expandedTemplateId === template.id ? "block" : "hidden"
                      }`}
                    >
                      <div className="text-sm font-medium text-gray-300 mb-1">
                        פירוט האימונים:
                      </div>
                      {template.templateData.map((day, idx) => (
                        <div
                          key={idx}
                          className="bg-gray-700 p-3 rounded-lg border border-gray-600 hover:border-purple-700 transition-all duration-200"
                        >
                          <div className="flex justify-between items-center">
                            <div className="font-medium text-gray-200">
                              {day.day}: {day.focus}
                            </div>
                            <div className="text-sm text-purple-300">
                              {day.exercises.length} תרגילים
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <div className="flex items-center gap-2">
                        {/* Edit button */}
                        <button
                          onClick={() =>
                            setEditingTemplate({
                              id: template.id,
                              name: template.name,
                              description: template.description,
                            })
                          }
                          className="p-2 rounded-full hover:bg-gray-700 transition-colors duration-200"
                          title="ערוך"
                        >
                          <Edit2 className="h-5 w-5 text-gray-400" />
                        </button>
                        {/* Delete button */}
                        <button
                          onClick={() => setShowDeleteConfirm(template.id)}
                          className="p-2 rounded-full hover:bg-gray-700 transition-colors duration-200"
                          title="מחק"
                        >
                          <Trash2 className="h-5 w-5 text-red-400" />
                        </button>
                        {/* Expand/Collapse */}
                        <button
                          onClick={() =>
                            setExpandedTemplateId(
                              expandedTemplateId === template.id
                                ? null
                                : template.id
                            )
                          }
                          className="text-sm text-purple-400 hover:text-purple-300 transition-colors duration-200"
                        >
                          {expandedTemplateId === template.id
                            ? "הסתר פרטים"
                            : "הצג פרטים"}
                        </button>
                      </div>

                      {/* Use Button */}
                      <button
                        onClick={() => onSelectTemplate(template)}
                        className="px-6 py-3 bg-purple-800 text-gray-200 rounded-full hover:bg-purple-700 transition-colors duration-200 shadow-sm hover:shadow-md flex items-center gap-2 font-medium"
                      >
                        <Copy className="h-5 w-5" />
                        <span>השתמש בתוכנית</span>
                      </button>
                    </div>
                  </>
                )}

                {/* Delete Confirmation - simple overlay */}
                {showDeleteConfirm === template.id && (
                  <div className="absolute inset-0 bg-gray-900 bg-opacity-95 rounded-xl flex flex-col items-center justify-center p-4 z-10 animate-fade-in-up">
                    <div className="p-3 bg-red-900 rounded-full mb-3">
                      <Trash2 className="h-8 w-8 text-red-300" />
                    </div>
                    <p className="text-center font-bold text-xl text-gray-200 mb-2">
                      למחוק את התוכנית?
                    </p>
                    <p className="text-center text-gray-400 mb-4">
                      פעולה זו אינה ניתנת לביטול
                    </p>
                    <div className="flex gap-4">
                      <button
                        onClick={() => handleDeleteConfirm(template.id)}
                        className="px-6 py-3 bg-red-700 text-gray-200 rounded-xl hover:bg-red-600 transition-colors duration-200 font-bold"
                      >
                        כן, מחק
                      </button>
                      <button
                        onClick={() => setShowDeleteConfirm(null)}
                        className="px-6 py-3 bg-gray-700 text-gray-300 rounded-xl hover:bg-gray-600 transition-colors duration-200 font-bold"
                      >
                        ביטול
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {isTemplateListLoading && <Loader_1 />}
      </div>
    </div>
  );
};
