import { useEffect, useState } from "react";

export const ErrorAlert = ({ message }: { message: string }) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setVisible(false), 3000);
    return () => clearTimeout(timer); // cleanup on unmount
  }, []);

  if (!visible) return null;

  return (
    <div className="fixed top-4 left-1/2 -translate-x-1/2 z-50 bg-red-900 text-red-200 px-6 py-3 rounded-xl shadow-lg flex items-center gap-3 border-2 border-red-700 transition-transform duration-300 ease-out">
      {message}
    </div>
  );
};
