import React, { useState } from 'react';
import { useWorkoutTemplate } from '../../contexts/WorkoutTemplateContext';
import { Save, X, Edit2, Trash2, Copy, Search, Clock, CalendarDays, Check, Star } from 'lucide-react';
import { TrainingDay } from '../../types/training';

interface WorkoutTemplateManagerProps {
  onClose: () => void;
  onSelectTemplate: (template: { id: string; name: string; day: TrainingDay }) => void;
}

export const WorkoutTemplateManager: React.FC<WorkoutTemplateManagerProps> = ({ 
  onClose,
  onSelectTemplate 
}) => {
  const { templates, deleteTemplate, updateTemplate } = useWorkoutTemplate();
  const [searchTerm, setSearchTerm] = useState('');
  const [editingTemplate, setEditingTemplate] = useState<{ id: string; name: string } | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  const handleSaveEdit = (id: string, newName: string) => {
    const template = templates.find(t => t.id === id);
    if (!template) return;
    
    updateTemplate(id, newName, template.day);
    setEditingTemplate(null);
  };

  const handleDeleteConfirm = (id: string) => {
    deleteTemplate(id);
    setShowDeleteConfirm(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('he-IL', { 
      year: 'numeric', 
      month: 'numeric', 
      day: 'numeric' 
    });
  };

  const filteredTemplates = templates.filter(template => 
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
    template.day.focus.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="bg-gray-900 rounded-xl shadow-lg overflow-hidden max-h-[80vh] flex flex-col border-2 border-purple-800">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900 via-gray-800 to-purple-900 p-6 border-b border-purple-800 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-full bg-purple-700 shadow-md">
            <CalendarDays className="h-8 w-8 text-purple-200" />
          </div>
          <div>
            <h3 className="text-2xl font-bold text-gray-200">תבניות אימון</h3>
            <p className="text-gray-400">בחר תבנית קיימת או צור חדשה</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="p-2 hover:bg-gray-700 rounded-full transition-colors duration-200"
        >
          <X className="h-6 w-6 text-gray-400" />
        </button>
      </div>

      {/* Search */}
      <div className="p-6 border-b border-gray-800">
        <div className="relative">
          <Search className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500" />
          <input
            type="text"
            placeholder="חפש תבנית..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-4 pr-12 py-3 border-2 border-gray-700 bg-gray-800 rounded-full focus:border-purple-600 focus:ring focus:ring-purple-800 focus:ring-opacity-50 text-lg text-gray-200"
            dir="rtl"
          />
        </div>
      </div>

      {/* Templates List */}
      <div className="overflow-y-auto flex-grow p-6 bg-gray-900">
        {filteredTemplates.length === 0 ? (
          <div className="text-center py-12">
            <div className="mx-auto bg-gray-800 p-4 rounded-full w-20 h-20 flex items-center justify-center mb-4">
              <CalendarDays className="h-10 w-10 text-gray-600" />
            </div>
            <h4 className="text-xl font-bold text-gray-400 mb-2">אין תבניות עדיין</h4>
            <p className="text-gray-500">
              {searchTerm ? 'לא נמצאו תבניות התואמות לחיפוש' : 'שמור אימון כתבנית כדי שיופיע כאן'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {filteredTemplates.map(template => (
              <div 
                key={template.id}
                className="bg-gray-800 rounded-xl p-5 shadow-sm hover:shadow-md transition-all duration-200 border-2 border-gray-700 hover:border-purple-700 relative"
              >
                {editingTemplate?.id === template.id ? (
                  // Edit mode - simplified
                  <div className="flex gap-2 items-center mb-4">
                    <input
                      type="text"
                      value={editingTemplate.name}
                      onChange={(e) => setEditingTemplate({ ...editingTemplate, name: e.target.value })}
                      className="flex-1 p-3 border-2 border-purple-700 bg-gray-800 rounded-lg focus:border-purple-600 focus:ring focus:ring-purple-800 focus:ring-opacity-50 text-lg font-medium text-gray-200"
                      autoFocus
                    />
                    <button
                      onClick={() => handleSaveEdit(template.id, editingTemplate.name)}
                      className="p-3 rounded-full bg-green-800 text-green-300 shadow-sm hover:bg-green-700 transition-colors duration-200"
                    >
                      <Check className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setEditingTemplate(null)}
                      className="p-3 rounded-full bg-red-800 text-red-300 shadow-sm hover:bg-red-700 transition-colors duration-200"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                ) : (
                  // View mode - fun and friendly
                  <>
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Star className="h-6 w-6 text-amber-400" />
                        <h4 className="text-xl font-bold text-gray-200">{template.name}</h4>
                      </div>
                      <div className="flex items-center gap-2">
                        <button 
                          onClick={() => setEditingTemplate({ id: template.id, name: template.name })}
                          className="p-2 rounded-full hover:bg-gray-700 transition-colors duration-200"
                          title="ערוך שם"
                        >
                          <Edit2 className="h-5 w-5 text-gray-400" />
                        </button>
                        <button 
                          onClick={() => setShowDeleteConfirm(template.id)}
                          className="p-2 rounded-full hover:bg-gray-700 transition-colors duration-200"
                          title="מחק"
                        >
                          <Trash2 className="h-5 w-5 text-red-400" />
                        </button>
                      </div>
                    </div>

                    <div className="bg-purple-900 px-4 py-2 rounded-full mb-3 inline-block">
                      <span className="text-sm font-medium text-purple-200">{template.day.focus}</span>
                    </div>

                    <div className="bg-gray-700 p-3 rounded-lg mb-3">
                      <div className="font-medium text-gray-300 mb-2">תרגילים:</div>
                      <div className="flex flex-wrap gap-2">
                        {template.day.exercises.slice(0, 3).map((exercise, idx) => (
                          <div 
                            key={idx}
                            className="bg-gray-800 text-sm px-3 py-1 rounded-full border border-gray-600 shadow-sm"
                          >
                            {exercise.name}
                          </div>
                        ))}
                        {template.day.exercises.length > 3 && (
                          <div className="bg-purple-900 text-sm px-3 py-1 rounded-full border border-purple-800 shadow-sm">
                            +{template.day.exercises.length - 3} נוספים
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1 text-sm text-gray-400">
                        <Clock className="h-4 w-4" />
                        <span>{formatDate(template.createdAt)}</span>
                      </div>
                      <button
                        onClick={() => onSelectTemplate(template)}
                        className="px-5 py-2 bg-green-800 text-gray-200 rounded-full hover:bg-green-700 transition-colors duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                      >
                        <Copy className="h-5 w-5" />
                        <span className="font-medium">השתמש בתבנית</span>
                      </button>
                    </div>
                  </>
                )}

                {/* Delete Confirmation - simple overlay */}
                {showDeleteConfirm === template.id && (
                  <div className="absolute inset-0 bg-gray-900 bg-opacity-95 rounded-xl flex flex-col items-center justify-center p-4 z-10">
                    <div className="p-3 bg-red-900 rounded-full mb-3">
                      <Trash2 className="h-6 w-6 text-red-300" />
                    </div>
                    <p className="text-center font-bold text-lg text-gray-200 mb-3">למחוק את התבנית?</p>
                    <div className="flex gap-3">
                      <button
                        onClick={() => handleDeleteConfirm(template.id)}
                        className="px-5 py-2 bg-red-700 text-gray-200 rounded-full hover:bg-red-600 transition-colors duration-200 font-medium"
                      >
                        כן, מחק
                      </button>
                      <button
                        onClick={() => setShowDeleteConfirm(null)}
                        className="px-5 py-2 bg-gray-700 text-gray-300 rounded-full hover:bg-gray-600 transition-colors duration-200 font-medium"
                      >
                        ביטול
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};