import React, { useEffect, useState } from "react";
import { Exercise } from "../../types/training";
import {
  <PERSON><PERSON><PERSON>,
  AlertCircle,
  X,
  Edit2,
  Check,
  Video,
  Play,
  EyeOff,
} from "lucide-react";
import { ErrorAlert } from "./ErrorAlert";

interface ExerciseEditorProps {
  exercise: Exercise;
  onUpdate: (updatedExercise: Exercise) => void;
  onDelete: () => void;
}

export const ExerciseEditor: React.FC<ExerciseEditorProps> = ({
  exercise,
  onUpdate,
  onDelete,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedExercise, setEditedExercise] = useState(exercise);
  const [showVideoInput, setShowVideoInput] = useState(false);
  const [videoUrl, setVideoUrl] = useState("");
  const [showVideo, setShowVideo] = useState(false);
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);

  useEffect(() => {
    if (exercise?.videoUrl) {
      setVideoUrl(exercise.videoUrl || "");
    }
  }, [exercise.videoUrl]);

  const handleSave = () => {
    onUpdate(editedExercise);
    setIsEditing(false);
  };

  const getEmbedUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      if (
        urlObj.hostname.includes("youtube.com") ||
        urlObj.hostname.includes("youtu.be") ||
        urlObj.hostname.includes("youtube.com/shorts") // Add support for shorts
      ) {
        const videoId = url.includes("youtu.be")
          ? url.split("/").pop()
          : url.includes("/shorts/") // Handle shorts URL
          ? url.split("/shorts/")[1]
          : new URLSearchParams(urlObj.search).get("v");
        return `https://www.youtube.com/embed/${videoId}`;
      }
      if (urlObj.hostname.includes("vimeo.com")) {
        const videoId = url.split("/").pop();
        return `https://player.vimeo.com/video/${videoId}`;
      }
    } catch (error) {
      console.error({ error });
      return null;
    }
    return null;
  };

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const handleVideoSave = () => {
    if (!videoUrl.trim()) return;

    const embedUrl = getEmbedUrl(videoUrl);
    if (!embedUrl) {
      setErrorMessage("נא להזין קישור תקין של YouTube או Vimeo");
      return;
    }

    onUpdate({ ...exercise, videoUrl: embedUrl });
    setShowVideoInput(false);
    setShowUploadSuccess(true);
    setErrorMessage(null);
    setTimeout(() => setShowUploadSuccess(false), 3000);
    setTimeout(() => setErrorMessage(""), 3000);
  };

  return (
    <div className="bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-700 hover:border-purple-800">
      {isEditing ? (
        /* Compact Editing View */
        <div className="space-y-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex-1">
              <input
                type="text"
                value={editedExercise.name}
                onChange={(e) =>
                  setEditedExercise({ ...editedExercise, name: e.target.value })
                }
                className="w-full px-3 py-2 border border-purple-700 rounded-lg font-medium bg-gray-800 text-gray-200"
                placeholder="שם התרגיל"
              />
            </div>
            <div className="flex items-center gap-2 ml-2">
              <button
                onClick={handleSave}
                className="p-1.5 rounded-lg bg-green-900 text-green-300 hover:bg-green-800 transition-colors duration-200"
              >
                <Check className="h-4 w-4" />
              </button>
              <button
                onClick={() => {
                  setIsEditing(false);
                  setEditedExercise(exercise);
                }}
                className="p-1.5 rounded-lg bg-red-900 text-red-300 hover:bg-red-800 transition-colors duration-200"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Compact inputs in a single row */}
          <div className="grid grid-cols-3 gap-2">
            <div>
              <label className="block text-xs text-gray-400 mb-1">סטים:</label>
              <input
                type="text"
                value={editedExercise.sets.replace(" sets", "")}
                onChange={(e) =>
                  setEditedExercise({
                    ...editedExercise,
                    sets: `${e.target.value} sets`,
                  })
                }
                className="w-full px-2 py-1 border border-gray-700 bg-gray-800 rounded-lg text-sm text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-800"
                placeholder="מספר"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-400 mb-1">חזרות:</label>
              <input
                type="text"
                value={editedExercise.reps.replace(" reps", "")}
                onChange={(e) =>
                  setEditedExercise({
                    ...editedExercise,
                    reps: `${e.target.value} reps`,
                  })
                }
                className="w-full px-2 py-1 border border-gray-700 bg-gray-800 rounded-lg text-sm text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-800"
                placeholder="טווח"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-400 mb-1">מנוחה:</label>
              <input
                type="text"
                value={editedExercise.rest.replace(" seconds rest", "")}
                onChange={(e) =>
                  setEditedExercise({
                    ...editedExercise,
                    rest: `${e.target.value} seconds rest`,
                  })
                }
                className="w-full px-2 py-1 border border-gray-700 bg-gray-800 rounded-lg text-sm text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-800"
                placeholder="שניות"
              />
            </div>
          </div>

          <div>
            <label className="block text-xs text-gray-400 mb-1">הנחיות:</label>
            <textarea
              value={editedExercise.instructions || ""}
              onChange={(e) =>
                setEditedExercise({
                  ...editedExercise,
                  instructions: e.target.value,
                })
              }
              className="w-full px-2 py-1 border border-gray-700 bg-gray-800 rounded-lg text-sm h-16 resize-none text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-800"
              placeholder="הוסף הנחיות (לא חובה)"
            />
          </div>
        </div>
      ) : (
        /* Compact View Mode */
        <div>
          <div className="flex justify-between items-start mb-2">
            <h4 className="font-bold text-gray-200 flex items-center gap-2">
              <Dumbbell className="h-4 w-4 text-purple-400" />
              {exercise.name}
            </h4>

            <div className="flex items-center gap-1">
              {exercise.videoUrl && (
                <button
                  onClick={() => setShowVideo(!showVideo)}
                  className={`p-1 rounded-lg ${
                    showVideo
                      ? "bg-red-900 text-red-300"
                      : "bg-purple-900 text-purple-300"
                  } hover:opacity-80 transition-colors duration-200 flex items-center gap-1 text-xs font-medium`}
                >
                  {showVideo ? (
                    <>
                      <EyeOff className="h-3 w-3" />
                      <span>הסתר</span>
                    </>
                  ) : (
                    <>
                      <Play className="h-3 w-3" />
                      <span>הצג</span>
                    </>
                  )}
                </button>
              )}

              <button
                onClick={() => setShowVideoInput(true)}
                className="p-1 rounded-lg bg-purple-900 text-purple-300 hover:bg-purple-800 transition-colors duration-200"
                title={exercise.videoUrl ? "ערוך סרטון" : "הוסף סרטון"}
              >
                <Video className="h-3 w-3" />
              </button>

              <button
                onClick={() => setIsEditing(true)}
                className="p-1 rounded-lg bg-amber-900 text-amber-300 hover:bg-amber-800 transition-colors duration-200"
                title="ערוך תרגיל"
              >
                <Edit2 className="h-3 w-3" />
              </button>

              <button
                onClick={onDelete}
                className="p-1 rounded-lg bg-red-900 text-red-300 hover:bg-red-800 transition-colors duration-200"
                title="מחק תרגיל"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>

          {/* Compact exercise details in a single row */}
          <div className="flex gap-2 text-sm">
            <div className="bg-purple-900 px-2 py-1 rounded flex-1 text-center">
              <span className="text-xs text-purple-300 block">סטים</span>
              <span className="font-medium text-purple-200">
                {exercise.sets.replace("sets", "")}
              </span>
            </div>

            <div className="bg-green-900 px-2 py-1 rounded flex-1 text-center">
              <span className="text-xs text-green-300 block">חזרות</span>
              <span className="font-medium text-green-200">
                {exercise.reps.replace("reps", "")}
              </span>
            </div>

            <div className="bg-blue-900 px-2 py-1 rounded flex-1 text-center">
              <span className="text-xs text-blue-300 block">מנוחה</span>
              <span className="font-medium text-blue-200">
                {exercise.rest.replace("seconds rest", "")}
              </span>
            </div>
          </div>

          {/* Instructions - compact */}
          {exercise.instructions && (
            <div className="mt-2 bg-amber-900 p-2 rounded text-xs text-amber-200 flex items-start gap-1">
              <AlertCircle className="h-3 w-3 mt-0.5 flex-shrink-0 text-amber-300" />
              <span>{exercise.instructions}</span>
            </div>
          )}

          {/* Success message */}
          {showUploadSuccess && (
            <div className="mt-2 flex items-center gap-1 bg-green-900 text-green-300 p-2 rounded text-xs">
              <Check className="h-3 w-3" />
              <span className="font-medium">הסרטון נוסף בהצלחה!</span>
            </div>
          )}

          {/* Video display */}
          {exercise.videoUrl && showVideo && (
            <div className="mt-2 rounded overflow-hidden border-2 border-purple-800">
              <div className="relative pt-[56.25%] bg-black">
                <iframe
                  src={exercise.videoUrl}
                  className="absolute inset-0 w-full h-full"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                />
              </div>
            </div>
          )}
        </div>
      )}

      {/* Video URL Input Modal */}
      {showVideoInput && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          {errorMessage && <ErrorAlert message={errorMessage} />}

          <div className="bg-gray-900 rounded-lg p-5 max-w-md w-full mx-4 border-2 border-purple-800">
            <div className="text-center mb-4">
              <div className="p-2 bg-purple-900 rounded-full w-12 h-12 inline-flex items-center justify-center mb-2">
                <Video className="h-6 w-6 text-purple-300" />
              </div>
              <h3 className="text-lg font-bold text-gray-200 mb-1">
                הוסף סרטון הדגמה
              </h3>
              <p className="text-xs text-gray-400">
                הוסף סרטון כדי להדגים כיצד לבצע את התרגיל
              </p>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  קישור לסרטון:
                </label>
                <input
                  type="text"
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                  placeholder="הכנס קישור YouTube או Vimeo..."
                  className="w-full p-2 border border-gray-700 bg-gray-800 rounded-lg text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-700"
                />
                <p className="mt-1 text-xs text-gray-500">
                  לדוגמה: https://www.youtube.com/watch?v=XXXX
                </p>
              </div>

              <div className="flex justify-end gap-2">
                <button
                  onClick={() => setShowVideoInput(false)}
                  className="px-3 py-1.5 text-sm bg-gray-700 text-gray-300 rounded-lg hover:bg-gray-600"
                >
                  ביטול
                </button>
                <button
                  onClick={handleVideoSave}
                  disabled={!videoUrl.trim()}
                  className="px-4 py-1.5 text-sm bg-purple-700 text-gray-200 rounded-lg hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  הוסף סרטון
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
