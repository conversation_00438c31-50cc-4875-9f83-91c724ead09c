import React from "react";
import {
  ArrowDownRight,
  ArrowUpRight,
  Award,
  Clock,
  Heart,
  Scale,
  Trophy,
} from "lucide-react";
import { Trainee } from "../../types/Trainee";
import { UserScopes } from "../user/UserScopes";
import Loader_1 from "../Loader/Loader_1";

interface OverviewProps {
  isLoading?: boolean;
  traineeData: Partial<Trainee>;
  isTrainerView?: boolean;
}

export const Overview: React.FC<OverviewProps> = ({
  isLoading = false,
  traineeData,
  isTrainerView = false,
}) => {
  const timeLeft = 0;
  const userPerformance = 20;
  const weightChange = 20;
  const fatChange = 100;

  if (isLoading) {
    return <Loader_1 />;
  }

  return (
    <div className="max-w-5xl mx-auto space-y-6" dir="rtl">
      <div className="bg-gray-900 rounded-xl shadow-lg p-6 border-2 border-purple-900">
        {/* <PERSON> Header */}
        <div className="flex flex-col items-center justify-center mb-6 pb-6 border-b border-gray-800">
          <div className="h-20 w-20 rounded-full bg-purple-600 flex items-center justify-center border-4 border-purple-300 mb-3">
            <span className="text-3xl font-bold text-white">
              {traineeData?.name?.charAt(0) || ""}
            </span>
          </div>
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-2">
              {traineeData?.name || ""}
            </h2>
            <div className="bg-purple-900 px-4 py-1.5 rounded-full border border-purple-700 inline-flex items-center gap-2">
              <Clock className="h-4 w-4 text-purple-300" />
              <span className="text-sm font-medium text-purple-200">
                {timeLeft}
              </span>
            </div>
          </div>
        </div>

        {/* SUPER SIMPLIFIED METRICS CARD - BORDER REMOVED */}
        <div className="bg-gray-800 rounded-xl p-4 mb-6">
          <div className="grid grid-cols-3 gap-4 items-center">
            {/* Weight Card - SUPER SIMPLE */}
            <div className="text-center">
              <div className="flex justify-center items-center mb-2">
                <div className="w-12 h-12 rounded-full bg-purple-900 flex items-center justify-center">
                  <Scale className="h-6 w-6 text-purple-300" />
                </div>
              </div>
              <h4 className="font-bold text-gray-200 mb-1">משקל</h4>
              <div className="text-2xl font-bold text-purple-300">
                {(traineeData as any)?.startingWeight || 10}{" "}
                <span className="text-sm">ק"ג</span>
              </div>
              <div className="flex items-center justify-center gap-1 mt-1">
                {weightChange < 0 ? (
                  <div className="bg-green-900 text-green-200 px-2 py-1 rounded-full text-sm font-medium flex items-center">
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                    <span>ירד {Math.abs(weightChange)}</span>
                  </div>
                ) : weightChange > 0 ? (
                  <div className="bg-red-900 text-red-200 px-2 py-1 rounded-full text-sm font-medium flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>עלה {weightChange}</span>
                  </div>
                ) : (
                  <div className="bg-gray-700 text-gray-300 px-2 py-1 rounded-full text-sm font-medium">
                    ללא שינוי
                  </div>
                )}
              </div>
            </div>

            {/* Body Fat Card - SUPER SIMPLE */}
            <div className="text-center">
              <div className="flex justify-center items-center mb-2">
                <div className="w-12 h-12 rounded-full bg-purple-900 flex items-center justify-center">
                  <Heart className="h-6 w-6 text-purple-300" />
                </div>
              </div>
              <h4 className="font-bold text-gray-200 mb-1">שומן גוף</h4>
              <div className="text-2xl font-bold text-purple-300">
                {traineeData?.startingFatPercentage || 10}
                <span className="text-sm">%</span>
              </div>
              <div className="flex items-center justify-center gap-1 mt-1">
                {fatChange < 0 ? (
                  <div className="bg-green-900 text-green-200 px-2 py-1 rounded-full text-sm font-medium flex items-center">
                    <ArrowDownRight className="h-3 w-3 mr-1" />
                    <span>ירד {Math.abs(fatChange)}%</span>
                  </div>
                ) : fatChange > 0 ? (
                  <div className="bg-red-900 text-red-200 px-2 py-1 rounded-full text-sm font-medium flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    <span>עלה {fatChange}%</span>
                  </div>
                ) : (
                  <div className="bg-gray-700 text-gray-300 px-2 py-1 rounded-full text-sm font-medium">
                    ללא שינוי
                  </div>
                )}
              </div>
            </div>

            {/* Score Card - SUPER SIMPLE */}
            <div className="text-center">
              <div className="flex justify-center items-center mb-2">
                <div className="w-12 h-12 rounded-full bg-purple-900 flex items-center justify-center">
                  <Award className="h-6 w-6 text-purple-300" />
                </div>
              </div>
              <h4 className="font-bold text-gray-200 mb-1">ציון</h4>
              <div className="text-2xl font-bold text-purple-300">
                {userPerformance}
              </div>
              <div className="flex items-center justify-center gap-1 mt-1">
                {userPerformance >= 85 ? (
                  <div className="bg-green-900 text-green-200 px-2 py-1 rounded-full text-sm font-medium">
                    🔥 מצוין!
                  </div>
                ) : userPerformance >= 70 ? (
                  <div className="bg-blue-900 text-blue-200 px-2 py-1 rounded-full text-sm font-medium">
                    👍 טוב
                  </div>
                ) : (
                  <div className="bg-yellow-900 text-yellow-200 px-2 py-1 rounded-full text-sm font-medium">
                    💪 בדרך
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Body Measurements */}
        <UserScopes userId={"1"} isTrainer={isTrainerView} />

        {/* Activity & Events */}
        <div className="grid grid-cols-2 gap-6 mt-8">
          {/* Updates */}
          <div>
            <h3 className="text-lg font-semibold text-gray-200 mb-4">
              עדכונים אחרונים
            </h3>
            <div className="space-y-4">
              <div className="bg-gray-800 rounded-lg p-4 border border-purple-800">
                <div className="flex items-center gap-2 mb-2">
                  <Trophy className="h-5 w-5 text-purple-400" />
                  <h4 className="font-medium text-gray-200">הישג אחרון</h4>
                </div>
                <p className="text-gray-300">
                  תוכנית האימונים הותאמה לצרכי ההתאוששות
                </p>{" "}
              </div>
              <div className="bg-gray-800 rounded-lg p-4 border border-purple-800">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-5 w-5 text-purple-400" />
                  <h4 className="font-medium text-gray-200">פעילות אחרונה</h4>
                </div>
                <p className="text-gray-300">השלים הערכת פיזיותרפיה</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
