import { useEffect, useState } from "react";
import { TrainingDay, WorkoutPlanTemplate } from "../../types/training";
import { TrainingDayEditor } from "../training/TrainingDayEditor";
import {
  Du<PERSON>bell,
  Save,
  FolderOpen,
  AlertCircle,
  Check,
  Plus,
} from "lucide-react";
import { WorkoutPlanTemplateManager } from "../training/WorkoutPlanTemplateManager";
import {
  useAddTrainingPlanMutation,
  useCreateTrainingPlanTemplateMutation,
  useDeleteTrainingPlanMutation,
  useGetAllExercisesQuery,
  useLazyGetTraineeTrainingPlansQuery,
  useLoadTrainingPlanFromTemplateMutation,
  useUpdateTrainingPlanMutation,
} from "../../api/services/Trainer/TrainerService";
import Loader_1 from "../Loader/Loader_1";
import { CustomExerciseType } from "../../types/exercise";
import { ErrorAlert } from "../training/ErrorAlert";

interface TrainingProps {
  traineeId: string;
}

export const Training = ({ traineeId }: TrainingProps) => {
  const { data: exerciseData } = useGetAllExercisesQuery({});
  const [getTraineeTrainingPlan, { isLoading }] =
    useLazyGetTraineeTrainingPlansQuery();
  const [
    addTrainingPlan,
    { error: addTrainingPlanError, isError: isAddTrainingPlanError },
  ] = useAddTrainingPlanMutation();

  const [
    updateTrainingPlan,
    { error: updateTrainingPlanError, isError: isUpdateTrainingPlanError },
  ] = useUpdateTrainingPlanMutation();

  const [
    deleteTrainingPlan,
    { error: deleteTrainingPlanError, isError: isDeleteTrainingPlanError },
  ] = useDeleteTrainingPlanMutation();

  const isAnyError =
    isAddTrainingPlanError ||
    isUpdateTrainingPlanError ||
    isDeleteTrainingPlanError;

  const backendToFrontendErrorMap: Record<string, string> = {
    "Only trainers can create training plans.":
      "רק מאמנים יכולים ליצור תוכניות אימון.",
    "Only trainers can update training plans.":
      "רק מאמנים יכולים לעדכן תוכניות אימון.",
    "Only trainers can delete training plans.":
      "רק מאמנים יכולים למחוק תוכניות אימון.",
    "Trainer is not assigned to this trainee or the assignment is not active/approved.":
      "אינך משויך למתאמן זה או שהשיוך אינו פעיל.",
    "Trainer is no longer assigned to this trainee or the assignment is not active/approved.":
      "אינך משויך למתאמן זה או שהשיוך אינו פעיל.",
    "Training plan not found": "תוכנית האימון לא נמצאה.",
    "Exercise with id": "אחד או יותר מהתרגילים בתוכנית אינם קיימים.",
  };

  const getTrainingPlanError = (backendMessage: string) => {
    console.log({ backendMessage });

    return (
      backendToFrontendErrorMap[backendMessage] ||
      "אירעה שגיאה בלתי צפויה. נסה שוב."
    );
  };

  const anyErrorMessage =
    (addTrainingPlanError as any)?.data?.message ||
    (updateTrainingPlanError as any)?.data?.message ||
    (deleteTrainingPlanError as any)?.data?.message ||
    "Something went wrong, please try again later!";

  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showTemplatesModal, setShowTemplatesModal] = useState(false);
  const [templateName, setTemplateName] = useState("");
  const [templateDescription, setTemplateDescription] = useState("");
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);

  const [trainingPlan, setTrainingPlan] = useState<TrainingDay[]>([]);
  const [exercises, setExercises] = useState<CustomExerciseType[]>([]);

  useEffect(() => {
    if (exerciseData) {
      setExercises(exerciseData);
    }
  }, [exerciseData]);

  useEffect(() => {
    const fetchData = async () => {
      if (traineeId) {
        const { data } = await getTraineeTrainingPlan(traineeId);
        setTrainingPlan(data);
      }
    };
    fetchData();
  }, [traineeId, getTraineeTrainingPlan]);

  const handleDayAdd = async (index?: number) => {
    const newDay: Omit<TrainingDay, "id"> = {
      day: "יום חדש",
      focus: "מיקוד חדש",
      exercises: [],
    };

    try {
      const newTrainingPlan = await addTrainingPlan({
        ...newDay,
        traineeId,
      }).unwrap();

      setTrainingPlan((prev) => {
        const newPlan = [...prev];
        if (typeof index === "number") {
          newPlan.splice(index + 1, 0, newTrainingPlan);
        } else {
          newPlan.push(newTrainingPlan);
        }
        return newPlan;
      });
    } catch (err) {
      console.error("Failed to add training plan:", err);
    }
  };

  const handleDayUpdate = async (index: number, updatedDay: TrainingDay) => {
    try {
      const { id, ...dayData } = updatedDay;

      const updatedPlan = await updateTrainingPlan({
        body: dayData,
        trainingPlanId: id,
      }).unwrap();

      setTrainingPlan((prev) => {
        const newPlan = [...prev];
        newPlan[index] = updatedPlan;
        return newPlan;
      });
    } catch (err) {
      console.error("Failed to update training day:", err);
    }
  };

  const handleDayDelete = async (index: number, trainingPlanId: string) => {
    try {
      await deleteTrainingPlan(trainingPlanId).unwrap();
      setTrainingPlan(trainingPlan.filter((_, i) => i !== index));
    } catch (error) {
      console.error({ error });
    }
  };

  const [createTrainingPlanTemplate] = useCreateTrainingPlanTemplateMutation();

  const handleSavePlanTemplate = async () => {
    if (!templateName.trim()) return;

    try {
      await createTrainingPlanTemplate({
        name: templateName,
        description: templateDescription,
        templateData: trainingPlan.map((plan) => {
          return {
            day: plan.day,
            focus: plan.focus,
            exercises: plan.exercises,
          };
        }),
      }).unwrap();

      setShowSaveModal(false);
      setTemplateName("");
      setTemplateDescription("");
      setShowSaveSuccess(true);

      setTimeout(() => {
        setShowSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error("Failed to save template:", error);
    }
  };

  const [loadTrainingPlanFromTemplate] =
    useLoadTrainingPlanFromTemplateMutation();

  const handleSelectTemplate = async (template: WorkoutPlanTemplate) => {
    try {
      const plansToLoad = await loadTrainingPlanFromTemplate({
        templateId: template.id,
        body: { traineeId },
      }).unwrap();

      setTrainingPlan([...trainingPlan, ...plansToLoad]);
      setShowTemplatesModal(false);
    } catch (error) {
      console.error("Failed to load template:", error);
    }
  };

  if (isLoading) {
    return <Loader_1 />;
  }

  return (
    <div className="max-w-4xl mx-auto p-6" dir="rtl">
      <div className="relative">
        {/* Success Message */}
        {showSaveSuccess && (
          <div className="fixed top-4 right-1/2 transform translate-x-1/2 z-50 bg-green-900 text-green-200 px-6 py-3 rounded-xl shadow-lg flex items-center gap-3 border-2 border-green-700 animate-fade-in-up">
            <Check className="h-6 w-6" />
            <span className="text-lg font-medium">
              התוכנית נשמרה בהצלחה! 🎉
            </span>
          </div>
        )}

        {isAnyError && (
          <ErrorAlert message={getTrainingPlanError(anyErrorMessage)} />
        )}

        {/* Compact Header */}
        <div className="bg-gray-900 rounded-xl shadow-sm p-4 mb-4 border-2 border-purple-900">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-purple-900">
                <Dumbbell className="h-6 w-6 text-purple-300" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-200">
                  תוכנית אימונים שבועית
                </h2>
                <p className="text-sm text-gray-400">ניהול תוכנית אימונים</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowSaveModal(true)}
                className="bg-green-800 hover:bg-green-700 text-gray-200 rounded-lg px-4 py-2 font-medium text-sm shadow-sm flex items-center gap-1"
              >
                <Save className="h-4 w-4" />
                <span>שמור</span>
              </button>

              <button
                onClick={() => setShowTemplatesModal(true)}
                className="bg-purple-800 hover:bg-purple-700 text-gray-200 rounded-lg px-4 py-2 font-medium text-sm shadow-sm flex items-center gap-1"
              >
                <FolderOpen className="h-4 w-4" />
                <span>תבניות</span>
              </button>
            </div>
          </div>

          {/* Compact guidance */}
          <div className="mt-3 bg-gray-800 rounded-lg p-2 border border-purple-800 flex items-center gap-2 text-sm">
            <AlertCircle className="h-4 w-4 text-purple-400 flex-shrink-0" />
            <p className="text-gray-300">
              <span className="font-medium text-purple-300">טיפ:</span> ניתן
              ללחוץ על "הוסף יום" כדי להוסיף יום אימון חדש.
            </p>
          </div>
        </div>

        {/* Workout Days Container - Compact Version */}
        <div className="space-y-3">
          {/* No workouts state */}
          {trainingPlan.length === 0 ? (
            <div className="bg-gray-900 rounded-xl shadow-sm p-6 border-2 border-dashed border-purple-700 flex items-center justify-center">
              <div className="text-center">
                <div className="mb-4 bg-purple-900 p-4 rounded-full inline-block">
                  <Dumbbell className="h-10 w-10 text-purple-300" />
                </div>
                <h3 className="font-bold text-xl text-gray-200 mb-1">
                  אין ימי אימון עדיין
                </h3>
                <p className="text-gray-400 mb-3">
                  יש ללחוץ על הכפתור כדי להוסיף יום אימון ראשון
                </p>
                <button
                  onClick={() => handleDayAdd()}
                  className="bg-purple-700 hover:bg-purple-600 text-gray-200 font-medium rounded-lg px-5 py-2.5 shadow-sm flex items-center gap-2 mx-auto"
                >
                  <Plus className="h-5 w-5" />
                  <span>הוסף יום אימון</span>
                </button>
              </div>
            </div>
          ) : (
            // Compact workout days list
            <div className="space-y-4">
              {trainingPlan.map((day, index) => (
                <TrainingDayEditor
                  key={index}
                  day={day}
                  onUpdate={(updatedDay) => handleDayUpdate(index, updatedDay)}
                  onDelete={() => handleDayDelete(index, day.id)}
                  onAddDay={() => handleDayAdd(index)}
                  exercises={exercises}
                />
              ))}

              {/* Compact add button */}
              <button
                onClick={() => handleDayAdd()}
                className="w-full py-2.5 flex items-center justify-center gap-2 bg-purple-800 hover:bg-purple-700 text-gray-200 font-medium rounded-lg shadow-sm"
              >
                <Plus className="h-5 w-5" />
                <span>הוסף יום אימון חדש</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Save Plan Template Modal - Compact */}
      {showSaveModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-lg max-w-md w-full mx-4 shadow-lg border-2 border-purple-700">
            <div className="p-5 border-b border-gray-800">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-800 rounded-full">
                  <Save className="h-6 w-6 text-purple-300" />
                </div>
                <h3 className="text-lg font-bold text-gray-200">
                  שמור תוכנית אימונים
                </h3>
              </div>
            </div>

            <div className="p-5 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  שם התוכנית:
                </label>
                <input
                  type="text"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="לדוגמה: תוכנית פיתוח שרירים"
                  className="w-full p-2 border border-gray-700 bg-gray-800 rounded-lg text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-800"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  תיאור התוכנית:
                </label>
                <textarea
                  value={templateDescription}
                  onChange={(e) => setTemplateDescription(e.target.value)}
                  placeholder="תאר את מטרות התוכנית..."
                  className="w-full p-2 border border-gray-700 bg-gray-800 rounded-lg h-20 text-gray-200 focus:border-purple-600 focus:ring focus:ring-purple-800"
                ></textarea>
              </div>

              <div className="flex justify-end pt-2 gap-3">
                <button
                  onClick={() => setShowSaveModal(false)}
                  className="px-4 py-2 text-gray-400 hover:text-gray-300 transition-colors"
                >
                  ביטול
                </button>
                <button
                  onClick={handleSavePlanTemplate}
                  disabled={!templateName.trim() || trainingPlan.length === 0}
                  className="px-4 py-2 bg-purple-700 text-gray-200 rounded-lg hover:bg-purple-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  שמור תוכנית
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Template Manager Modal */}
      {showTemplatesModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="max-w-4xl w-full max-h-[90vh]">
            <WorkoutPlanTemplateManager
              onClose={() => setShowTemplatesModal(false)}
              onSelectTemplate={handleSelectTemplate}
            />
          </div>
        </div>
      )}
    </div>
  );
};
