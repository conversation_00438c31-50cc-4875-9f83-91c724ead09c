import React, { useState, useEffect } from "react";
import { UserTable } from "../users/UserTable";
import { FilterBar } from "../users/FilterBar";
import { AdminPanel } from "../users/AdminPanel";
import { useSelector } from "react-redux";
import {
  useAddTraineeMutation,
  useGetAllTrainerTraineesQuery,
} from "../../api/services/Trainer/TrainerService";
import { RootState } from "../../api/store";
import { Trainee } from "../../types/Trainee";
import { AlertSection } from "../users/AlertSection";
import { StatisticCards } from "../users/StatisticCards";
import Loader_1 from "../Loader/Loader_1";

interface UsersOverviewProps {
  onUserSelect: (userId: string) => void;
}

export interface NewTraineeDataInterface {
  trainee_email: string;
  trainee_name: string;
  trainee_age: number;
  trainee_height: number;
  trainee_gender: string;
  trainee_level: string;
  trainee_weight: number;
  trainee_bodyFatPercentage: number;
  trainee_fitnessGoal: string;
  trainee_activityLevel: string;
  trainee_profileImageUrl: string;
  trainee_countryCode: string;
  trainee_mobileNumber: string;
}

export const UsersOverview: React.FC<UsersOverviewProps> = ({
  onUserSelect,
}) => {
  const [
    AddTrainee,
    {
      isLoading: isAddingNewTrainee,
      error: addNewTraineeError,
      isError: isAddingNewTraineeError,
    },
  ] = useAddTraineeMutation();

  const { user: TrainerData } = useSelector((state: RootState) => state.auth);
  const { data, isLoading, refetch } = useGetAllTrainerTraineesQuery(
    TrainerData.id
  );

  const [trainees, setTrainees] = useState<Trainee[]>([]);

  useEffect(() => {
    if (data && data.length > 0) {
      const additionalData = {
        avatar: "/default-profile-image.png",
        status: "active",
        performance: 85,
        level: "advanced",
        lastActive: "2024-03-21",
        overdueTasks: 0,
        attentionNote: null,
        trainerId: "trainer1",
      };

      const UpdatedTraineesData = data.map(
        (item: { trainee: Partial<Trainee> }) => ({
          ...item.trainee,
          ...additionalData,
        })
      );
      setTrainees(UpdatedTraineesData);
    }
  }, [data]);

  useEffect(() => {
    refetch();
  }, [refetch]);

  const [filters, setFilters] = useState({
    status: "all",
    performance: "all",
    team: "all",
    dateRange: "all",
  });

  // Add user handler
  const HandleTraineeAdd = async (
    newTraineeData: NewTraineeDataInterface
  ): Promise<boolean> => {
    const fullMobileNumber = newTraineeData.trainee_mobileNumber
      ? `${newTraineeData.trainee_countryCode}${
          newTraineeData.trainee_mobileNumber.startsWith("0")
            ? newTraineeData.trainee_mobileNumber.substring(1)
            : newTraineeData.trainee_mobileNumber
        }`
      : "";

    try {
      const resp = await AddTrainee({
        ...newTraineeData,
        trainerId: TrainerData.id,
        trainee_mobileNumber: fullMobileNumber,
      }).unwrap();

      if (resp?.trainee) {
        const newTrainee: Trainee = {
          id: resp.trainee.id || (trainees.length + 1).toString(),
          name: resp.trainee.name,
          email: resp.trainee.email,
          age: newTraineeData.trainee_age,
          height: newTraineeData.trainee_height,
          gender: newTraineeData.trainee_gender,
          goal: newTraineeData.trainee_fitnessGoal,
          level: newTraineeData.trainee_level,
          avatar: "/default-profile-image.png",
          mobileNumber: resp.trainee.mobileNumber,

          approved: true,
          isActive: true,
          startDate: new Date().toString(),
          endDate: new Date().toString(),
          lastActive: new Date().toISOString().split("T")[0],
          status: "active",

          performance: 0,
          overdueTasks: 0,
          attentionNote: null,
          weight: newTraineeData.trainee_weight,
          startingWeight: newTraineeData.trainee_weight,
          startingFatPercentage: newTraineeData.trainee_bodyFatPercentage,
          activityLevel: newTraineeData.trainee_activityLevel,
        };

        setTrainees((prev) => [...prev, newTrainee]);
      }

      return true;
    } catch (error) {
      console.error(error);
      return false;
    }
  };

  if (isLoading) {
    return <Loader_1 />;
  }

  return (
    <div className="space-y-6">
      <AdminPanel
        onAddUser={HandleTraineeAdd}
        isLoading={isAddingNewTrainee}
        isError={isAddingNewTraineeError}
        error={addNewTraineeError}
      />

      <StatisticCards users={trainees} />

      <AlertSection users={trainees} onUserSelect={onUserSelect} />

      <FilterBar filters={filters} onFilterChange={setFilters} />

      <UserTable
        users={trainees}
        filters={filters}
        onUserSelect={onUserSelect}
      />
    </div>
  );
};
