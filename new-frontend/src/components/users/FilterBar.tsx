import React from "react";
import { Filter, Users, Award, Clock, ChevronDown } from "lucide-react";

interface FilterBarProps {
  filters: {
    status: string;
    performance: string;
    team: string;
    dateRange: string;
  };
  onFilterChange: (filters: any) => void;
}

export const FilterBar: React.FC<FilterBarProps> = ({
  filters,
  onFilterChange,
}) => {
  return (
    <div
      className="bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 mb-8 transform hover:-translate-y-1 border-2 border-purple-800"
      dir="rtl"
    >
      <div className="flex items-center gap-2 mb-6">
        <div className="p-2 rounded-lg bg-purple-700 shadow-md transform hover:rotate-12 transition-transform duration-300">
          <Filter className="h-6 w-6 text-white" />
        </div>
        <h2 className="text-xl font-semibold text-white text-right">
          סינון מתאמנים
        </h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="relative group">
          <label className=" text-base font-semibold text-white mb-2 flex items-center gap-2">
            <Users className="h-5 w-5 text-purple-400 transform group-hover:rotate-12 transition-transform duration-300" />
            סטטוס
            {filters.status !== "all" && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-purple-700 text-white animate-pulse">
                {filters.status === "active"
                  ? "פעיל"
                  : filters.status === "inactive"
                  ? "לא פעיל"
                  : filters.status === "attention"
                  ? "דורש תשומת לב"
                  : filters.status}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-700 shadow-sm focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 transition-all duration-200 hover:border-purple-700 text-base font-medium text-white py-2 pr-4 pl-10 appearance-none bg-gray-700"
              value={filters.status}
              onChange={(e) =>
                onFilterChange({ ...filters, status: e.target.value })
              }
            >
              <option value="all">כל הסטטוסים</option>
              <option value="active">פעיל</option>
              <option value="inactive">לא פעיל</option>
              <option value="attention">דורש תשומת לב</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500 pointer-events-none group-hover:text-purple-400 transition-colors duration-200" />
          </div>
        </div>

        <div className="relative group">
          <label className=" text-base font-semibold text-white mb-2 flex items-center gap-2">
            <Award className="h-5 w-5 text-yellow-400 transform group-hover:rotate-12 transition-transform duration-300" />
            ביצועים
            {filters.performance !== "all" && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-yellow-700 text-white animate-pulse">
                {filters.performance === "above" ? "מעל היעד" : "מתחת ליעד"}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-700 shadow-sm focus:border-yellow-600 focus:ring focus:ring-yellow-700 focus:ring-opacity-50 transition-all duration-200 hover:border-yellow-700 text-base font-medium text-white py-2 pr-4 pl-10 appearance-none bg-gray-700"
              value={filters.performance}
              onChange={(e) =>
                onFilterChange({ ...filters, performance: e.target.value })
              }
            >
              <option value="all">כל הביצועים</option>
              <option value="above">מעל היעד</option>
              <option value="below">מתחת ליעד</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500 pointer-events-none group-hover:text-yellow-400 transition-colors duration-200" />
          </div>
        </div>

        <div className="relative group">
          <label className=" text-base font-semibold text-white mb-2 flex items-center gap-2">
            <Users className="h-5 w-5 text-purple-400 transform group-hover:rotate-12 transition-transform duration-300" />
            רמה
            {filters.team !== "all" && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-purple-700 text-white animate-pulse">
                {filters.team === "beginner"
                  ? "מתחיל"
                  : filters.team === "intermediate"
                  ? "מתקדם"
                  : filters.team === "advanced"
                  ? "מקצועי"
                  : filters.team}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-700 shadow-sm focus:border-purple-600 focus:ring focus:ring-purple-700 focus:ring-opacity-50 transition-all duration-200 hover:border-purple-700 text-base font-medium text-white py-2 pr-4 pl-10 appearance-none bg-gray-700"
              value={filters.team}
              onChange={(e) =>
                onFilterChange({ ...filters, team: e.target.value })
              }
            >
              <option value="all">כל הרמות</option>
              <option value="beginner">מתחיל</option>
              <option value="intermediate">מתקדם</option>
              <option value="advanced">מקצועי</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500 pointer-events-none group-hover:text-purple-400 transition-colors duration-200" />
          </div>
        </div>

        <div className="relative group">
          <label className=" text-base font-semibold text-white mb-2 flex items-center gap-2">
            <Clock className="h-5 w-5 text-green-400 transform group-hover:rotate-12 transition-transform duration-300" />
            טווח זמן
            {filters.dateRange !== "all" && (
              <span className="mr-2 text-sm font-medium px-2.5 py-0.5 rounded-full bg-green-700 text-white animate-pulse">
                {filters.dateRange === "today"
                  ? "היום"
                  : filters.dateRange === "week"
                  ? "השבוע"
                  : filters.dateRange === "month"
                  ? "החודש"
                  : filters.dateRange}
              </span>
            )}
          </label>
          <div className="relative">
            <select
              className="w-full rounded-lg border-2 border-gray-700 shadow-sm focus:border-green-600 focus:ring focus:ring-green-700 focus:ring-opacity-50 transition-all duration-200 hover:border-green-700 text-base font-medium text-white py-2 pr-4 pl-10 appearance-none bg-gray-700"
              value={filters.dateRange}
              onChange={(e) =>
                onFilterChange({ ...filters, dateRange: e.target.value })
              }
            >
              <option value="all">כל הזמן</option>
              <option value="today">היום</option>
              <option value="week">השבוע</option>
              <option value="month">החודש</option>
            </select>
            <ChevronDown className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-500 pointer-events-none group-hover:text-green-400 transition-colors duration-200" />
          </div>
        </div>
      </div>

      {/* Active Filters */}
      {(filters.status !== "all" ||
        filters.performance !== "all" ||
        filters.team !== "all" ||
        filters.dateRange !== "all") && (
        <div className="mt-6 pt-4 border-t border-gray-700">
          <div className="flex items-center gap-2 text-base text-white">
            <span className="font-semibold">מסננים פעילים:</span>
            <div className="flex flex-wrap gap-2">
              {filters.status !== "all" && (
                <span className="px-3 py-1.5 rounded-full bg-purple-800 text-white border border-purple-700 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {filters.status === "active"
                    ? "פעיל"
                    : filters.status === "inactive"
                    ? "לא פעיל"
                    : filters.status === "attention"
                    ? "דורש תשומת לב"
                    : filters.status}
                </span>
              )}
              {filters.performance !== "all" && (
                <span className="px-3 py-1.5 rounded-full bg-yellow-800 text-white border border-yellow-700 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Award className="h-4 w-4" />
                  {filters.performance === "above" ? "מעל היעד" : "מתחת ליעד"}
                </span>
              )}
              {filters.team !== "all" && (
                <span className="px-3 py-1.5 rounded-full bg-purple-800 text-white border border-purple-700 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {filters.team === "beginner"
                    ? "מתחיל"
                    : filters.team === "intermediate"
                    ? "מתקדם"
                    : filters.team === "advanced"
                    ? "מקצועי"
                    : filters.team}
                </span>
              )}
              {filters.dateRange !== "all" && (
                <span className="px-3 py-1.5 rounded-full bg-green-800 text-white border border-green-700 transform hover:scale-105 transition-all duration-200 font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  {filters.dateRange === "today"
                    ? "היום"
                    : filters.dateRange === "week"
                    ? "השבוע"
                    : filters.dateRange === "month"
                    ? "החודש"
                    : filters.dateRange}
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
