import React from "react";
import {
  <PERSON>,
  Target,
  <PERSON><PERSON><PERSON><PERSON>gle,
  TrendingUp,
  Trophy,
  Award,
} from "lucide-react";
import { getOverviewById } from "../../data/overview";
import { calculateUserPerformance } from "../../utils/userPerformance";
import { Trainee } from "../../types/Trainee";

interface StatisticCardsProps {
  users: Trainee[];
}

export const StatisticCards: React.FC<StatisticCardsProps> = ({ users }) => {
  const totalUsers = users.length;
  const usersNeedingAction = users.filter(
    (u) => u.status === "attention"
  ).length;

  const usersOnTarget = users.filter((user) => {
    const userOverview = getOverviewById("1") || user;
    const performance = calculateUserPerformance(
      userOverview.engagementLevel,
      userOverview.progressLevel,
      userOverview.weeklyIntensity || 0
    );
    return performance >= 80;
  }).length;

  const averagePerformance = 20;

  const getPerformanceEmoji = (value: number) => {
    if (value >= 90) return "🌟";
    if (value >= 80) return "⭐";
    if (value >= 70) return "✨";
    return "💪";
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" dir="rtl">
      <div className="relative bg-purple-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-purple-600 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <Trophy className="h-6 w-6 text-yellow-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-purple-700 text-white">
            <Users className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              👥
            </span>
            <span className="text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">
              {totalUsers}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-white">מתאמנים פעילים</div>
          <div className="text-xs text-purple-200 mt-1">סה״כ בקהילה</div>
        </div>
      </div>

      <div className="relative bg-red-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-red-600 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <AlertTriangle className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-red-800 text-white">
            <AlertTriangle className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              ❗
            </span>
            <span className="text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">
              {usersNeedingAction}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-white">זקוקים לתמיכה</div>
          <div className="text-xs text-red-200 mt-1">מתאמנים לבדיקה</div>
        </div>
      </div>

      <div className="relative bg-green-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-green-600 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <Award className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-green-800 text-white">
            <Target className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              🏆
            </span>
            <span className="text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">
              {usersOnTarget}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-white">משיגי יעדים</div>
          <div className="text-xs text-green-200 mt-1">מצליחים ביעדים</div>
        </div>
      </div>

      <div className="relative bg-amber-700 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 border-amber-600 group">
        <div className="absolute top-0 left-0 -mt-2 -ml-2">
          <TrendingUp className="h-6 w-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>
        <div className="flex items-center justify-between">
          <div className="p-3 rounded-xl bg-amber-800 text-white">
            <TrendingUp className="h-7 w-7" />
          </div>
          <div className="flex items-center gap-2">
            <div className="flex flex-col items-end">
              <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {getPerformanceEmoji(averagePerformance)}
              </span>
              <span className="text-sm text-amber-200">%</span>
            </div>
            <span className="text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">
              {averagePerformance}
            </span>
          </div>
        </div>
        <div className="mt-4 text-right">
          <div className="text-sm font-semibold text-white">רוח הקבוצה</div>
          <div className="text-xs text-amber-200 mt-1">רמת האנרגיה הכללית</div>
        </div>
      </div>
    </div>
  );
};
