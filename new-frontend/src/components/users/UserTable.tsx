import React from "react";
import { StatusBadge } from "./StatusBadge";
import { getOverviewById } from "../../data/overview";
import { calculateUserPerformance } from "../../utils/userPerformance";
import { getLastMessageDate } from "../../utils/messageUtils";
import { formatDate } from "../../utils/dateUtils";
import { Target, ChevronLeft, Medal, Crown, Award, Flame } from "lucide-react";
import { Trainee } from "../../types/Trainee";

interface UserTableProps {
  users: Trainee[];
  filters: {
    status: string;
    performance: string;
    team: string;
    dateRange: string;
  };
  onUserSelect: (userId: string) => void;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  filters,
  onUserSelect,
}) => {
  const getPerformanceBadge = (performance: number, rank: number) => {
    if (rank === 0) {
      return (
        <div className="flex items-center gap-2 bg-yellow-700 px-3 py-1.5 rounded-full border border-yellow-600 transform hover:scale-105 transition-all duration-200">
          <Crown className="h-5 w-5 text-yellow-300 animate-pulse" />
          <span className="font-medium text-yellow-200 text-sm whitespace-nowrap">
            מקום ראשון
          </span>
        </div>
      );
    }
    if (rank === 1) {
      return (
        <div className="flex items-center gap-2 bg-gray-700 px-3 py-1.5 rounded-full border border-gray-600 transform hover:scale-105 transition-all duration-200">
          <Medal className="h-5 w-5 text-gray-300" />
          <span className="font-medium text-gray-300 text-sm whitespace-nowrap">
            מקום שני
          </span>
        </div>
      );
    }
    if (rank === 2) {
      return (
        <div className="flex items-center gap-2 bg-orange-700 px-3 py-1.5 rounded-full border border-orange-600 transform hover:scale-105 transition-all duration-200">
          <Award className="h-5 w-5 text-orange-300" />
          <span className="font-medium text-orange-200 text-sm whitespace-nowrap">
            מקום שלישי
          </span>
        </div>
      );
    }
    if (performance >= 90) {
      return (
        <div className="flex items-center gap-2 bg-purple-700 px-3 py-1.5 rounded-full border border-purple-600 transform hover:scale-105 transition-all duration-200">
          <Flame className="h-5 w-5 text-purple-300" />
          <span className="font-medium text-purple-200 text-sm whitespace-nowrap">
            מצוין
          </span>
        </div>
      );
    }
    return (
      <div className="flex items-center gap-2 bg-purple-700 px-3 py-1.5 rounded-full border border-purple-600 transform hover:scale-105 transition-all duration-200">
        <Target className="h-5 w-5 text-purple-300" />
        <span className="font-medium text-purple-200 text-sm whitespace-nowrap">
          כוכב עולה
        </span>
      </div>
    );
  };

  const filteredUsers = users.filter((user) => {
    if (filters.status !== "all" && user.status !== filters.status) {
      return false;
    }

    const userOverview = getOverviewById("1");

    const performance = calculateUserPerformance(
      userOverview?.engagementLevel,
      userOverview?.progressLevel,
      userOverview?.weeklyIntensity || 0
    );

    if (filters.performance !== "all") {
      const isAboveTarget = performance >= 80;
      if (filters.performance === "above" && !isAboveTarget) return false;
      if (filters.performance === "below" && isAboveTarget) return false;
    }
    if (
      filters.team !== "all" &&
      user.level.toLowerCase() !== filters.team.toLowerCase()
    ) {
      return false;
    }
    if (filters.dateRange !== "all") {
      const lastMessageDate = getLastMessageDate(user.id);
      if (!lastMessageDate) return false;

      const today = new Date();
      const diffTime = Math.abs(today.getTime() - lastMessageDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      switch (filters.dateRange) {
        case "today":
          if (diffDays > 1) return false;
          break;
        case "week":
          if (diffDays > 7) return false;
          break;
        case "month":
          if (diffDays > 30) return false;
          break;
      }
    }
    return true;
  });

  // Sort users by performance score
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    const aOverview = getOverviewById(a.id);
    const bOverview = getOverviewById(b.id);
    const aPerformance = calculateUserPerformance(
      aOverview?.engagementLevel || 0,
      aOverview?.progressLevel || 0,
      aOverview?.weeklyIntensity || 0
    );
    const bPerformance = calculateUserPerformance(
      bOverview?.engagementLevel || 0,
      bOverview?.progressLevel || 0,
      bOverview?.weeklyIntensity || 0
    );
    return bPerformance - aPerformance;
  });

  return (
    <div
      className="bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden transform hover:-translate-y-1 border-2 border-purple-800"
      dir="rtl"
    >
      <div className="overflow-x-auto">
        <table className="w-full divide-y divide-gray-700">
          <thead className="bg-purple-900">
            <tr>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-300">
                דירוג
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-300">
                מתאמן
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-300">
                סטטוס
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-300">
                רמה
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-300">
                ניקוד
              </th>
              <th className="px-4 py-4 text-right text-sm font-semibold text-gray-300">
                פעילות אחרונה
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-700">
            {sortedUsers.map((user, index) => {
              const userOverview = getOverviewById(user.id);
              const performance = calculateUserPerformance(
                userOverview?.engagementLevel || 0,
                userOverview?.progressLevel || 0,
                userOverview?.weeklyIntensity || 0
              );
              const lastMessageDate = getLastMessageDate(user.id);
              const lastActive = lastMessageDate
                ? formatDate(lastMessageDate)
                : "אף פעם";

              return (
                <tr
                  key={user.id}
                  onClick={() => onUserSelect(user.id)}
                  className={`hover:bg-gray-700 cursor-pointer transition-all duration-300 group ${
                    index < 3 ? "bg-gray-800" : ""
                  }`}
                >
                  <td className="px-4 py-4 text-center">
                    <div
                      className={`text-2xl font-bold ${
                        index === 0
                          ? "text-yellow-300"
                          : index === 1
                          ? "text-gray-300"
                          : index === 2
                          ? "text-orange-300"
                          : "text-gray-400"
                      }`}
                    >
                      #{index + 1}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center gap-3">
                      <div className="relative group/avatar flex-shrink-0">
                        <div className="absolute -inset-0.5 bg-purple-500 rounded-full opacity-0 group-hover/avatar:opacity-75 transition-opacity duration-300 blur"></div>
                        <img
                          className="relative h-10 w-10 rounded-full object-cover transform group-hover/avatar:scale-105 transition-transform duration-300 border-2 border-gray-700 group-hover:border-purple-500"
                          src={user.avatar}
                          alt=""
                        />
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="text-base font-semibold text-white group-hover:text-purple-300 transition-colors duration-200 flex items-center gap-1">
                          {user.name}
                          <ChevronLeft className="h-4 w-4 opacity-0 group-hover:opacity-100 transform group-hover:translate-x-1 transition-all duration-200" />
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <StatusBadge status={user.status} />
                  </td>
                  <td className="px-4 py-4">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-sm font-medium bg-purple-700 text-purple-200 transform hover:scale-105 transition-all duration-200 border border-purple-600">
                      {user.level === "beginner"
                        ? "מתחיל"
                        : user.level === "intermediate"
                        ? "מתקדם"
                        : user.level === "advanced"
                        ? "מקצועי"
                        : user.level}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center gap-4">
                      {getPerformanceBadge(performance, index)}
                      <div className="text-lg font-bold text-white">
                        {performance}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <span className="text-sm font-medium text-gray-400 group-hover:text-gray-200 transition-colors duration-200">
                      {lastActive}
                    </span>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export { UserTable };
