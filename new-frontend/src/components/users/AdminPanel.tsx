import React, { useState } from "react";
import { User<PERSON><PERSON>, X, <PERSON>, Al<PERSON><PERSON>riangle, Loader, Phone } from "lucide-react";
import { NewTraineeDataInterface } from "../tabs/UsersOverview";
import {
  GENDER_OPTIONS,
  TRAINEE_ACTIVITY_LEVELS,
  TRAINEE_FITNESS_GOALS,
  TRAINEE_LEVELS,
} from "../../enums/TraineeEnums";

interface AdminPanelProps {
  onAddUser: (userData: NewTraineeDataInterface) => Promise<boolean>;
  isLoading: boolean;
  error: any;
  isError: boolean;
}

export const AdminPanel: React.FC<AdminPanelProps> = ({
  onAddUser,
  isLoading,
  isError,
  error,
}) => {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [showSaveSuccess, setShowSaveSuccess] = useState<boolean>(false);
  const [NewTraineeData, setNewTraineeData] = useState<NewTraineeDataInterface>(
    {
      trainee_email: "",
      trainee_name: "",
      trainee_age: 0,
      trainee_height: 0,
      trainee_weight: 0,
      trainee_gender: "",
      trainee_bodyFatPercentage: 0,
      trainee_level: "",
      trainee_fitnessGoal: "",
      trainee_activityLevel: "",
      trainee_profileImageUrl: "",
      trainee_countryCode: "+972",
      trainee_mobileNumber: "",
    }
  );

  const backendToFrontendErrorMap: Record<string, string> = {
    "Trainer not found": "מאמן לא נמצא.",
    "Selected trainer is not a valid trainer": "המאמן שנבחר אינו מאמן תקין.",
    "Default role not found.": "תפקיד ברירת מחדל לא נמצא.",
    "Trainer and trainee cannot be the same person":
      "מאמן ומתאמן לא יכולים להיות אותו אדם.",
    "This trainer is already assigned to this trainee":
      "מאמן זה כבר משויך למתאמן זה.",
  };

  const getTrainerAssignmentError = (backendMessage: string) =>
    backendToFrontendErrorMap[backendMessage] ||
    "משהו השתבש בעת יצירת השיוך. נסה שוב.";

  const handleSubmit = async (e: React.FormEvent) => {
    setShowSaveSuccess(false);

    e.preventDefault();

    const resp = await onAddUser(NewTraineeData);

    if (resp) {
      setNewTraineeData({
        trainee_email: "",
        trainee_name: "",
        trainee_age: 0,
        trainee_height: 0,
        trainee_weight: 0,
        trainee_gender: "",
        trainee_bodyFatPercentage: 0,
        trainee_level: "",
        trainee_fitnessGoal: "",
        trainee_activityLevel: "",
        trainee_profileImageUrl: "",
        trainee_countryCode: "+972",
        trainee_mobileNumber: "",
      });

      setShowSaveSuccess(true);
      setIsOpen(false);
      setTimeout(() => setShowSaveSuccess(false), 3000);
    }
  };

  const inputClasses = `w-full p-2 border-2 border-gray-600 bg-gray-700 rounded-lg focus:border-purple-500 focus:ring focus:ring-purple-600 focus:ring-opacity-50 text-white text-right`;
  const disabledClass = isLoading ? "opacity-50 cursor-not-allowed" : "";

  if (!isOpen) {
    return (
      <>
        {showSaveSuccess && (
          <div className="mb-4 p-4 bg-green-900 text-green-200 rounded-lg flex justify-end items-center gap-3 border-2 border-green-700 shadow-sm">
            <div className="p-2 bg-green-800 rounded-full">
              <Check className="h-5 w-5" />
            </div>
            <span className="font-medium">מתאמן חדש נוסף בהצלחה</span>
          </div>
        )}

        <button
          onClick={() => setIsOpen(true)}
          className="w-full bg-purple-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 mb-8 flex items-center justify-center gap-3 border-2 border-purple-700 group"
        >
          <div className="p-3 rounded-xl bg-purple-700 transform group-hover:rotate-12 transition-transform duration-300">
            <UserPlus className="h-6 w-6 text-white" />
          </div>
          <span className="text-lg font-semibold text-white">
            הוסף מתאמן חדש
          </span>
        </button>
      </>
    );
  }

  return (
    <div className="w-full bg-purple-900 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 mb-8 border-2 border-purple-700">
      {/* Error block */}
      {isError && error && (
        <div
          className="mb-6 p-4 bg-gray-300 border border-red-200 rounded-lg flex items-start gap-3"
          dir="rtl"
        >
          <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-red-700 font-medium">שגיאה</p>
            <p className="text-red-600" dir="rtl">
              {error?.data?.status || 500}
              {" : "}
              {error
                ? error?.data?.message
                  ? getTrainerAssignmentError(error?.data?.message)
                  : error?.message
                  ? getTrainerAssignmentError(error?.message)
                  : error?.error
                  ? getTrainerAssignmentError(error?.error)
                  : ""
                : ""}
            </p>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between mb-6" dir="rtl">
        <div className="flex items-center gap-3">
          <div className="p-3 rounded-xl bg-purple-700">
            <UserPlus className="h-6 w-6 text-white" />
          </div>
          <h2 className="text-xl font-semibold text-white">הוסף מתאמן חדש</h2>
        </div>

        <button
          onClick={() => setIsOpen(false)}
          className="p-2 hover:bg-purple-800 rounded-full transition-colors duration-200"
        >
          <X className="h-5 w-5 text-white" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
          <h3 className="text-right text-lg font-medium text-white mb-4">
            מידע בסיסי
          </h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                שם
              </label>
              <input
                type="text"
                required
                disabled={isLoading}
                value={NewTraineeData.trainee_name}
                placeholder="הכנס שם"
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_name: e.target.value,
                  })
                }
                className={`${inputClasses} ${disabledClass}`}
              />
            </div>

            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                אימייל
              </label>
              <input
                type="email"
                required
                disabled={isLoading}
                value={NewTraineeData.trainee_email}
                placeholder="הכנס אימייל"
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_email: e.target.value,
                  })
                }
                className={`${inputClasses} ${disabledClass}`}
              />
            </div>

            <div className="rounded-lg">
              <div className="w-100">
                <label
                  className="w-100 text-right flex items-center justify-end
                 text-sm font-medium text-gray-300 mb-2"
                >
                  מספר נייד
                  <Phone className="h-4 w-4 text-gray-300 ml-2" />
                </label>

                <div className="flex justify-between items-center w-100">
                  {/* Country Code Selector */}
                  <select
                    value={NewTraineeData.trainee_countryCode}
                    disabled={isLoading}
                    onChange={(e) =>
                      setNewTraineeData({
                        ...NewTraineeData,
                        trainee_countryCode: e.target.value,
                      })
                    }
                    className={`p-2 border-2 border-gray-600 bg-gray-700 rounded-lg focus:border-purple-500 focus:ring focus:ring-purple-600 focus:ring-opacity-50 text-white ${disabledClass} w-[6rem] mr-2`}
                  >
                    <option value="+972">+972 🇮🇱</option>
                    <option value="+1">+1 🇺🇸</option>
                    <option value="+44">+44 🇬🇧</option>
                    <option value="+33">+33 🇫🇷</option>
                    <option value="+49">+49 🇩🇪</option>
                    <option value="+7">+7 🇷🇺</option>
                    <option value="+86">+86 🇨🇳</option>
                    <option value="+91">+91 🇮🇳</option>
                  </select>

                  <input
                    type="tel"
                    required
                    value={NewTraineeData.trainee_mobileNumber}
                    disabled={isLoading}
                    onChange={(e) =>
                      setNewTraineeData({
                        ...NewTraineeData,
                        trainee_mobileNumber: e.target.value.replace(/\D/g, ""),
                      })
                    }
                    className={`${inputClasses} ${disabledClass} w-[100%]`}
                    placeholder="50-1234567"
                    autoComplete="tel"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Demographics */}
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
          <h3 className="text-right text-lg font-medium text-white mb-4">
            פרטים דמוגרפיים
          </h3>
          <div className="grid grid-cols-3 gap-6">
            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                גיל
              </label>
              <input
                type="number"
                required
                disabled={isLoading}
                min="0"
                max="120"
                value={NewTraineeData.trainee_age || ""}
                placeholder="הכנס גיל"
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_age: parseInt(e.target.value),
                  })
                }
                className={`${inputClasses} ${disabledClass}`}
              />
            </div>
            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                מין
              </label>
              <select
                disabled={isLoading}
                value={NewTraineeData.trainee_gender}
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_gender: e.target.value,
                  })
                }
                className={`${inputClasses} capitalize ${disabledClass}`}
                required
              >
                <option value="">בחר מין</option>
                {GENDER_OPTIONS.map((item) => (
                  <option key={item.value} value={item.value}>
                    {item.label}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                רמה
              </label>
              <select
                disabled={isLoading}
                value={NewTraineeData.trainee_level}
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_level: e.target.value,
                  })
                }
                required
                className={`${inputClasses} capitalize ${disabledClass}`}
              >
                <option value="">בחר רמה</option>
                {TRAINEE_LEVELS.map((item) => (
                  <option key={item.value} value={item.value}>
                    {item.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Measurements */}
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
          <h3 className="text-right text-lg font-medium text-white mb-4">
            מדדים
          </h3>
          <div className="grid grid-cols-3 gap-6">
            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                גובה (ס"מ)
              </label>
              <input
                type="number"
                required
                disabled={isLoading}
                min="0"
                max="300"
                value={NewTraineeData.trainee_height || ""}
                placeholder="הכנס גובה"
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_height: parseInt(e.target.value),
                  })
                }
                className={`${inputClasses} ${disabledClass}`}
              />
            </div>

            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                משקל התחלתי (ק"ג)
              </label>
              <input
                type="number"
                required
                disabled={isLoading}
                min="0"
                max="300"
                value={NewTraineeData.trainee_weight || ""}
                placeholder="הכנס משקל"
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_weight: parseFloat(e.target.value),
                  })
                }
                className={`${inputClasses} ${disabledClass}`}
              />
            </div>

            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                אחוז שומן התחלתי (%)
              </label>
              <input
                type="number"
                required
                disabled={isLoading}
                min="0"
                max="100"
                value={NewTraineeData.trainee_bodyFatPercentage || ""}
                placeholder="הכנס אחוז שומן"
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_bodyFatPercentage: parseFloat(e.target.value),
                  })
                }
                className={`${inputClasses} ${disabledClass}`}
              />
            </div>
          </div>
        </div>

        {/* Goals and Activity */}
        <div className="bg-gray-800 p-4 rounded-lg border border-gray-700">
          <h3 className="text-right text-lg font-medium text-white mb-4">
            יעדים ורמת פעילות
          </h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                יעד
              </label>
              <select
                disabled={isLoading}
                value={NewTraineeData.trainee_fitnessGoal}
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_fitnessGoal: e.target.value,
                  })
                }
                required
                className={`${inputClasses} capitalize ${disabledClass}`}
              >
                <option value="">בחר יעד כושר</option>
                {TRAINEE_FITNESS_GOALS.map((goal) => (
                  <option key={goal.value} value={goal.value}>
                    {goal.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="text-right block text-sm font-medium text-gray-300 mb-2">
                רמת פעילות
              </label>
              <select
                disabled={isLoading}
                value={NewTraineeData.trainee_activityLevel}
                onChange={(e) =>
                  setNewTraineeData({
                    ...NewTraineeData,
                    trainee_activityLevel: e.target.value,
                  })
                }
                className={`${inputClasses} capitalize ${disabledClass}`}
                required
              >
                <option value="">בחר רמת פעילות</option>
                {TRAINEE_ACTIVITY_LEVELS.map((level) => (
                  <option key={level.value} value={level.value}>
                    {level.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        <div className="flex justify-start">
          <button
            dir="rtl"
            type="submit"
            disabled={isLoading}
            className={`flex items-center gap-2 px-6 py-2 bg-purple-700 text-white rounded-lg transition-colors duration-200 ${
              isLoading
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-purple-600"
            }`}
          >
            {isLoading ? (
              <Loader className="animate-spin h-5 w-5" />
            ) : (
              <Check className="h-5 w-5" />
            )}
            <span>{isLoading ? "מוסיף מתאמן..." : "הוסף מתאמן"}</span>
          </button>
        </div>
      </form>
    </div>
  );
};
