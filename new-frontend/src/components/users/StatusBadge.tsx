import React from 'react';

interface StatusBadgeProps {
  status: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'active':
        return 'bg-green-700 text-white ring-green-600';
      case 'inactive':
        return 'bg-gray-700 text-white ring-gray-600';
      case 'attention':
        return 'bg-red-700 text-white ring-red-600';
      default:
        return 'bg-gray-700 text-white ring-gray-600';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'פעיל';
      case 'inactive':
        return 'לא פעיל';
      case 'attention':
        return 'דורש תשומת לב';
      default:
        return status;
    }
  };

  return (
    <span 
      className={`inline-flex items-center px-2 py-1 text-sm font-medium rounded-full ring-1 transform hover:scale-105 transition-all duration-200 whitespace-nowrap md:text-sm md:px-2 md:py-1 ${getStatusStyles()}`}
    >
      {getStatusText(status)}
    </span>
  );
};