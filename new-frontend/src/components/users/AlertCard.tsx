import React from "react";
import { <PERSON>ert<PERSON>riangle, TrendingDown, Clock, ChevronLeft } from "lucide-react";
import { getLastMessageDate } from "../../utils/messageUtils";
import { formatDate } from "../../utils/dateUtils";
import { Trainee } from "../../types/Trainee";

interface AlertCardProps {
  title: string;
  count: number;
  type: "attention" | "performance" | "overdue";
  users: Trainee[];
  onUserSelect: (userId: string) => void;
}

export const AlertCard: React.FC<AlertCardProps> = ({
  title,
  count,
  type,
  users,
  onUserSelect,
}) => {
  const getIcon = () => {
    switch (type) {
      case "attention":
        return <AlertTriangle className="h-6 w-6 text-white animate-pulse" />;
      case "performance":
        return <TrendingDown className="h-6 w-6 text-white" />;
      case "overdue":
        return <Clock className="h-6 w-6 text-white" />;
    }
  };

  const getBackground = () => {
    switch (type) {
      case "attention":
        return "bg-red-700";
      case "performance":
        return "bg-yellow-700";
      case "overdue":
        return "bg-amber-700";
    }
  };

  const getBorderColor = () => {
    switch (type) {
      case "attention":
        return "border-red-600";
      case "performance":
        return "border-yellow-600";
      case "overdue":
        return "border-amber-600";
    }
  };

  const getIconBackground = () => {
    switch (type) {
      case "attention":
        return "bg-red-800";
      case "performance":
        return "bg-yellow-800";
      case "overdue":
        return "bg-amber-800";
    }
  };

  const getAlertStyle = () => {
    switch (type) {
      case "attention":
        return "bg-red-900 text-white border-red-600";
      case "performance":
        return "bg-yellow-900 text-white border-yellow-600";
      case "overdue":
        return "bg-amber-900 text-white border-amber-600";
    }
  };

  return (
    <div
      className={`${getBackground()} rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 transform hover:-translate-y-1 border-2 ${getBorderColor()} relative group`}
      dir="rtl"
    >
      <div className="absolute top-0 left-0 -mt-2 -ml-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {getIcon()}
      </div>

      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div
            className={`p-3 rounded-xl ${getIconBackground()} shadow-md transform group-hover:rotate-12 transition-transform duration-300`}
          >
            {getIcon()}
          </div>
          <h3 className="text-lg font-bold text-white">{title}</h3>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-3xl font-bold text-white group-hover:scale-110 transition-transform duration-300">
            {count}
          </span>
          {count > 0 && type === "attention" && (
            <span className="text-lg animate-pulse">❗</span>
          )}
          {count > 0 && type === "performance" && (
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              📊
            </span>
          )}
          {count > 0 && type === "overdue" && (
            <span className="text-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              ⏰
            </span>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {users.slice(0, 3).map((user) => {
          const lastMessageDate = getLastMessageDate(user.id);
          const lastActive = lastMessageDate
            ? formatDate(lastMessageDate)
            : "Never";

          return (
            <div
              key={user.id}
              onClick={() => onUserSelect(user.id)}
              className="bg-gray-800 rounded-lg p-3 cursor-pointer transform hover:scale-102 transition-all duration-200 hover:shadow-md group/item border-2 border-gray-700"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <div className="absolute -inset-0.5 bg-white rounded-full opacity-0 group-hover/item:opacity-20 transition-opacity duration-300 blur"></div>
                    <img
                      className="relative h-10 w-10 rounded-full object-cover transform group-hover/item:scale-105 transition-transform duration-300 border-2 border-gray-600"
                      src={user.avatar}
                      alt=""
                    />
                  </div>
                  <div>
                    <div className="font-medium text-white">{user.name}</div>
                    <div className="text-sm text-gray-300">{lastActive}</div>
                  </div>
                </div>
                <ChevronLeft className="h-5 w-5 text-gray-400 transform group-hover/item:translate-x-1 transition-transform duration-200" />
              </div>
              {user.attentionNote && (
                <div
                  className={`mt-3 text-sm px-3 py-2 rounded-lg flex items-center gap-2 ${getAlertStyle()}`}
                >
                  {type === "attention" && (
                    <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                  )}
                  {type === "performance" && (
                    <TrendingDown className="h-4 w-4 flex-shrink-0" />
                  )}
                  {type === "overdue" && (
                    <Clock className="h-4 w-4 flex-shrink-0" />
                  )}
                  <span className="font-medium">{user.attentionNote}</span>
                </div>
              )}
            </div>
          );
        })}
        {users.length > 3 && (
          <button className="w-full text-sm font-medium text-gray-300 hover:text-white py-2 flex items-center justify-center gap-1 group/button bg-gray-800 rounded-lg border border-gray-700 mt-2">
            Show all {users.length} members
            <ChevronLeft className="h-4 w-4 transform group-hover/button:translate-x-1 transition-transform duration-200" />
          </button>
        )}
      </div>
    </div>
  );
};
