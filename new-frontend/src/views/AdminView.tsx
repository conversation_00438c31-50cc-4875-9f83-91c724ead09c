import React, { useState } from "react";
import { ChevronLeft } from "lucide-react";
import { AdminDashboard } from "../components/admin/AdminDashboard";
import { AdminTrainers } from "../components/admin/AdminTrainers";
import { AdminAnalytics } from "../components/admin/AdminAnalytics";
import { AdminSettings } from "../components/admin/AdminSettings";

interface AdminViewProps {
  onBackToSelect: () => void;
}

export const AdminView: React.FC<AdminViewProps> = ({ onBackToSelect }) => {
  const [activeTab, setActiveTab] = useState("dashboard");

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-4">
        <div className="flex items-center justify-between">
          <button
            onClick={onBackToSelect}
            className="flex items-center gap-2 text-purple-400 hover:text-purple-300 font-medium"
          >
            <ChevronLeft className="h-5 w-5" />
            התנתקות{" "}
          </button>
          <h1 className="text-xl font-bold text-gray-200">לוח בקרה למנהל</h1>
        </div>
      </div>

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sidebar */}
        <div className="w-64 bg-gray-800 border-r border-gray-700 p-4">
          <nav className="space-y-2">
            <button
              onClick={() => setActiveTab("dashboard")}
              className={`w-full px-4 py-2 text-left rounded-lg transition-colors duration-200 ${
                activeTab === "dashboard"
                  ? "bg-purple-900 text-purple-100"
                  : "hover:bg-gray-700 text-gray-300"
              }`}
            >
              לוח בקרה
            </button>
            <button
              onClick={() => setActiveTab("trainers")}
              className={`w-full px-4 py-2 text-left rounded-lg transition-colors duration-200 ${
                activeTab === "trainers"
                  ? "bg-purple-900 text-purple-100"
                  : "hover:bg-gray-700 text-gray-300"
              }`}
            >
              מאמנים
            </button>
            <button
              onClick={() => setActiveTab("analytics")}
              className={`w-full px-4 py-2 text-left rounded-lg transition-colors duration-200 ${
                activeTab === "analytics"
                  ? "bg-purple-900 text-purple-100"
                  : "hover:bg-gray-700 text-gray-300"
              }`}
            >
              אנליטיקה
            </button>
            <button
              onClick={() => setActiveTab("settings")}
              className={`w-full px-4 py-2 text-left rounded-lg transition-colors duration-200 ${
                activeTab === "settings"
                  ? "bg-purple-900 text-purple-100"
                  : "hover:bg-gray-700 text-gray-300"
              }`}
            >
              הגדרות
            </button>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto p-6 bg-gray-900">
          {activeTab === "dashboard" && <AdminDashboard />}
          {activeTab === "trainers" && <AdminTrainers />}
          {activeTab === "analytics" && <AdminAnalytics />}
          {activeTab === "settings" && <AdminSettings />}
        </div>
      </div>
    </div>
  );
};
