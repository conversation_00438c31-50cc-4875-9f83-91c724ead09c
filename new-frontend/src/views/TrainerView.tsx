import React from "react";
import { ChevronLeft } from "lucide-react";
import App from "../components/App";

interface TrainerViewProps {
  onBackToSelect: () => void;
}

export const TrainerView: React.FC<TrainerViewProps> = ({ onBackToSelect }) => {
  return (
    <div className="min-h-screen bg-gray-900">
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-4">
        <button
          onClick={onBackToSelect}
          className="flex items-center gap-2 text-purple-400 hover:text-purple-300 font-medium"
        >
          <ChevronLeft className="h-5 w-5" />
          התנתקות{" "}
        </button>
      </div>

      <App />
    </div>
  );
};
