import { TrainerView } from "../views/TrainerView";
import { UserView } from "../views/UserView";
import { AdminView } from "../views/AdminView";
// import { <PERSON><PERSON><PERSON>, Shield, Users } from "lucide-react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../api/store";
import { useLogoutMutation } from "../api/services/Auth/AuthService";
import { logoutUser } from "../api/services/Auth/AuthSlice";
import { WorkoutProvider } from "../contexts/WorkoutContext";

const HomePage = () => {
  const { user, token } = useSelector((state: RootState) => state.auth);
  const [logout] = useLogoutMutation();
  const dispatch = useDispatch();

  const view: "ROLE_TRAINER" | "ROLE_TRAINEE" | "ROLE_ADMIN" | "" =
    user?.role?.name || "";

  const HandleUserLogout = async () => {
    try {
      await logout({ token }).unwrap();
      dispatch(logoutUser());
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <>
      {view === "ROLE_TRAINER" ? (
        <TrainerView onBackToSelect={HandleUserLogout} />
      ) : view === "ROLE_TRAINEE" ? (
        <>
          <WorkoutProvider>
            <UserView onBackToSelect={HandleUserLogout} />
          </WorkoutProvider>
        </>
      ) : view === "ROLE_ADMIN" ? (
        <AdminView onBackToSelect={HandleUserLogout} />
      ) : null}
    </>
  );
};

export default HomePage;
