import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { Mail, Lock, LogIn, Eye, EyeOff } from "lucide-react";
import { useLoginMutation } from "../api/services/Auth/AuthService";
import { useDispatch } from "react-redux";
import { setCredentials } from "../api/services/Auth/AuthSlice";
// import { useAuth } from "../contexts/AuthContext";

const LoginPage = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [login, { isLoading }] = useLoginMutation();
  const dispatch = useDispatch();
  const [error, setError] = useState<string | null>(null);

  // Effect to clear error after 3 seconds
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout>;

    if (error) {
      timeoutId = setTimeout(() => {
        setError(null);
      }, 3000);
    }

    // Cleanup function to clear timeout
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [error]);

  const backendToFrontendErrorMap: Record<string, string> = {
    "Please provide a valid email": "אנא ספק כתובת אימייל חוקית",
    "email should not be empty": "שדה האימייל לא יכול להיות ריק",
    "password should not be empty": "שדה הסיסמה לא יכול להיות ריק",
    "password must match the required pattern": "הסיסמה חייבת להתאים לדרישות",
    "Invalid email or password.": "אימייל או סיסמה לא נכונים",
    "User not found": "משתמש לא נמצא",
    "Role not found": "תפקיד לא נמצא",
    "JWT_SECRET is not defined in the environment variables":
      "שגיאה בתצורת השרת",
  };

  const getFrontendError = (backendMessage: string) => {
    return (
      backendToFrontendErrorMap[backendMessage] || "משהו השתבש. אנא נסי שוב."
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const data = { email, password };

    try {
      const userData = await login(data).unwrap();
      dispatch(setCredentials(userData));
    } catch (error) {
      console.error({ error });
      setError(
        getFrontendError(
          (error as { data: { message: string } })?.data?.message || ""
        )
      );
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen bg-[#111827] flex items-center justify-center p-4">
      {isLoading && (
        <div className="fixed inset-0 bg-white bg-opacity-60 flex items-center justify-center z-50">
          <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-600 border-solid"></div>
        </div>
      )}

      <div className="max-w-md w-full space-y-8 bg-[#1f2937] text-white p-8 rounded-xl shadow-lg border-2 border-[#5c3c87]">
        {/*  */}
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">AI-ראדה</h1>
          <p className="text-lg text-[#998e79]">התחבר לחשבונך</p>
        </div>

        {/* Error Message with Transition */}
        {error && (
          <div
            dir="rtl"
            className="bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded relative transition-all duration-300 ease-in-out"
            role="alert"
          >
            <span className="block sm:inline">{error}</span>
          </div>
        )}

        <form onSubmit={handleSubmit} className="mt-8 space-y-6">
          <div className="space-y-4">
            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="text-right w-full pr-10 py-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-black"
                placeholder="כתובת אימייל"
                autoComplete="email"
              />
            </div>

            <div className="relative">
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type={showPassword ? "text" : "password"}
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="text-right w-full pl-10 pr-10 py-2 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-black"
                placeholder="סיסמה"
                autoComplete="current-password"
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute inset-y-0 left-0 pl-3 flex items-center focus:outline-none"
              >
                {showPassword ? (
                  <Eye className="h-5 w-5 text-gray-400" />
                ) : (
                  <EyeOff className="h-5 w-5 text-gray-400" />
                )}
              </button>
            </div>
            <div className="text-right">
              <Link
                to="/forgot-password"
                className="hover:underline text-gray-400"
              >
                שכחת סיסמה?
              </Link>
            </div>
          </div>

          <button
            type="submit"
            className="w-full flex justify-center items-center gap-2 px-4 py-3 bg-[#5c3c87] text-white rounded-lg hover:bg-[#664296] transition-colors duration-200 font-semibold"
          >
            <LogIn className="h-5 w-5" />
            התחבר
          </button>

          {/* <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">or continue with</span>
            </div>
          </div> */}

          {/* <GoogleLogin onSuccess={handleGoogleLoginSuccess} /> */}

          {/* <div className="text-center">
            <button
              type="button"
              onClick={() => navigate("/register")}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              Need an account? Sign up
            </button>
          </div> */}
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
