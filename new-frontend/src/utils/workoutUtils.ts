import { WorkoutLog } from '../data/workoutLogs';
import { trainingPlan } from '../data/trainingPlan';

// Calculate total volume for a single exercise
const calculateExerciseVolume = (weight: number, reps: number) => weight * reps;

// Parse reps range and return average
const parseRepsRange = (repsRange: string): number => {
  const matches = repsRange.match(/(\d+)-(\d+)/);
  if (matches) {
    const [, min, max] = matches;
    return (parseInt(min) + parseInt(max)) / 2;
  }
  return parseInt(repsRange);
};

// Calculate Actual Total Volume from workout logs
export const calculateActualVolume = (logs: WorkoutLog[]): number => {
  return logs.reduce((totalVolume, log) => {
    const exerciseVolume = log.exercises.reduce((exerciseTotal, exercise) => {
      const setsVolume = exercise.sets.reduce((setsTotal, set) => {
        return setsTotal + calculateExerciseVolume(set.weight, set.reps);
      }, 0);
      return exerciseTotal + setsVolume;
    }, 0);
    return totalVolume + exerciseVolume;
  }, 0);
};

// Calculate Planned Total Volume from training plan
export const calculatePlannedVolume = (): number => {
  // Assuming average weights based on exercise type
  const defaultWeights: { [key: string]: number } = {
    'לחיצת חזה': 80,
    'סקוואט': 100,
    'דדליפט': 120,
    'פרפר': 20,
    'לחיצה': 60,
    'פולי': 70,
    'חתירה': 65,
  };

  return trainingPlan.reduce((totalVolume, day) => {
    const dayVolume = day.exercises.reduce((dayTotal, exercise) => {
      // Estimate weight based on exercise name
      const estimatedWeight = Object.entries(defaultWeights).reduce((weight, [key, value]) => {
        if (exercise.name.toLowerCase().includes(key.toLowerCase())) {
          return value;
        }
        return weight;
      }, 60); // Default weight if no match

      const sets = parseInt(exercise.sets.split(' ')[0]);
      const avgReps = parseRepsRange(exercise.reps.split(' ')[0]);
      
      return dayTotal + (estimatedWeight * sets * avgReps);
    }, 0);
    return totalVolume + dayVolume;
  }, 0);
};

// Calculate Weekly Intensity
export const calculateWeeklyIntensity = (logs: WorkoutLog[]): number => {
  const actualVolume = calculateActualVolume(logs);
  const plannedVolume = calculatePlannedVolume();
  
  if (plannedVolume === 0) return 0;
  
  return (actualVolume / plannedVolume) * 100;
};