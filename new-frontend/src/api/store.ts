import { configureStore } from "@reduxjs/toolkit";
import { authApi } from "./services/Auth/AuthService";
import authReducer from "./services/Auth/AuthSlice";
import { otpApi } from "./services/OTP/OtpService";
import { adminApi } from "./services/Admin/AdminService";
import { trainerApi } from "./services/Trainer/TrainerService";
import { traineeApi } from "./services/Trainee/TraineeService";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    [authApi.reducerPath]: authApi.reducer,
    [otpApi.reducerPath]: otpApi.reducer,
    [adminApi.reducerPath]: adminApi.reducer,
    [trainerApi.reducerPath]: trainerApi.reducer,
    [traineeApi.reducerPath]: traineeApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      authApi.middleware,
      otpApi.middleware,
      adminApi.middleware,
      trainerApi.middleware,
      traineeApi.middleware
    ),
});

export type RootState = ReturnType<typeof store.getState>;
