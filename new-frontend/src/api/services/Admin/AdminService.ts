import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BASE_URL } from "../../api-utils";

export const adminApi = createApi({
  reducerPath: "adminApi",
  keepUnusedDataFor: 0,
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL + "/api/admin",
    prepareHeaders: (headers) => {
      const token = localStorage.getItem("access_token");

      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }

      return headers;
    },
  }),

  endpoints: (builder) => ({
    adminGetAllTrainers: builder.query({
      query: () => ({
        url: "trainers",
        method: "GET",
      }),
    }),

    addNewTrainer: builder.mutation({
      query: (body) => ({
        url: "trainer",
        method: "POST",
        body,
      }),
    }),

    deleteTrainer: builder.mutation({
      query: (trainerId) => ({
        url: `trainer/${trainerId}`,
        method: "DELETE",
      }),
    }),

    editTrainer: builder.mutation({
      query: ({ body, trainerId }) => ({
        url: `trainer/${trainerId}`,
        method: "PUT",
        body,
      }),
    }),
  }),
});

export const {
  useAdminGetAllTrainersQuery,
  useAddNewTrainerMutation,
  useDeleteTrainerMutation,
  useEditTrainerMutation,
} = adminApi;
