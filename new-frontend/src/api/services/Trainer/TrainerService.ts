import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BASE_URL } from "../../api-utils";

export const trainerApi = createApi({
  reducerPath: "trainerApi",
  keepUnusedDataFor: 0,
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: (headers) => {
      const token = localStorage.getItem("access_token");

      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }

      return headers;
    },
  }),

  endpoints: (builder) => ({
    // -------------------- Trainee --------------------------------------
    getAllTrainerTrainees: builder.query({
      query: (trainerId) => ({
        url: `trainer-assignments/trainer/${trainerId}`,
        method: "GET",
      }),
    }),

    getSingleTraineeData: builder.query({
      query: (traineeId) => ({
        url: `trainer-assignments/trainee/${traineeId}`,
        method: "GET",
      }),
    }),

    addTrainee: builder.mutation({
      query: (body) => ({
        url: "trainer-assignments",
        method: "POST",
        body,
      }),
    }),

    updateTraineeMealMacrosSettings: builder.mutation({
      query: (traineeId) => ({
        url: `meals/macros/${traineeId}`,
        method: "PUT",
      }),
    }),
    // -------------------- Meal --------------------------------------

    getTraineeMealPlans: builder.query({
      query: (traineeId) => ({
        url: `meals/trainee/${traineeId}`,
        method: "GET",
      }),
    }),

    getAllFoodItems: builder.query({
      query: () => ({
        url: `food-items`,
        method: "GET",
      }),
    }),

    deleteMealFromMealPlan: builder.mutation({
      query: (mealId) => ({
        url: `meals/${mealId}`,
        method: "DELETE",
      }),
    }),

    addMealToMealPlan: builder.mutation({
      query: (body) => ({
        url: `meals`,
        method: "POST",
        body,
      }),
    }),

    updateMealInMealPlan: builder.mutation({
      query: ({ body, mealId }) => ({
        url: `meals/${mealId}`,
        method: "PATCH",
        body,
      }),
    }),

    createMealTemplate: builder.mutation({
      query: (body) => ({
        url: `meal-templates`,
        method: "POST",
        body,
      }),
    }),

    updateMealTemplate: builder.mutation({
      query: ({ body, templateId }) => ({
        url: `meal-templates/${templateId}`,
        method: "PUT",
        body,
      }),
    }),

    getAllMealTemplates: builder.query({
      query: () => ({
        url: `meal-templates`,
        method: "GET",
      }),
    }),

    loadMealFromMealTemplates: builder.mutation({
      query: ({ body, templateId }) => ({
        url: `meals/${templateId}`,
        method: "POST",
        body,
      }),
    }),

    // -------------------- Training --------------------------------------
    getAllExercises: builder.query({
      query: () => ({
        url: `exercises`,
        method: "GET",
      }),
    }),

    getTraineeTrainingPlans: builder.query({
      query: (traineeId) => ({
        url: `training-plans/user/${traineeId}`,
        method: "GET",
      }),
    }),

    addTrainingPlan: builder.mutation({
      query: (body) => ({
        url: "training-plans",
        method: "POST",
        body,
      }),
    }),

    updateTrainingPlan: builder.mutation({
      query: ({ body, trainingPlanId }) => ({
        url: `training-plans/${trainingPlanId}`,
        method: "PUT",
        body,
      }),
    }),

    deleteTrainingPlan: builder.mutation({
      query: (trainingPlanId) => ({
        url: `training-plans/${trainingPlanId}`,
        method: "DELETE",
      }),
    }),

    getTraineeWorkoutLogs: builder.query({
      query: (traineeId) => ({
        url: `workout-logs/user/${traineeId}`,
        method: "GET",
      }),
    }),

    getTraineeLastWeekWorkoutLogs: builder.query({
      query: (traineeId) => ({
        url: `workout-logs/user/${traineeId}/last-week`,
        method: "GET",
      }),
    }),

    createTrainingPlanTemplate: builder.mutation({
      query: (body) => ({
        url: `training-templates`,
        method: "POST",
        body,
      }),
    }),

    updateTrainingPlanTemplate: builder.mutation({
      query: ({ body, templateId }) => ({
        url: `training-templates/${templateId}`,
        method: "PUT",
        body,
      }),
    }),

    deleteTrainingPlanTemplate: builder.mutation({
      query: (templateId) => ({
        url: `training-templates/${templateId}`,
        method: "DELETE",
      }),
    }),

    getAllTrainingPlanTemplate: builder.query({
      query: () => ({
        url: `training-templates`,
        method: "GET",
      }),
    }),

    LoadTrainingPlanFromTemplate: builder.mutation({
      query: ({ body, templateId }) => ({
        url: `training-plans/${templateId}`,
        method: "POST",
        body,
      }),
    }),
  }),
});

export const {
  // ----------------trainee----------------
  useGetAllTrainerTraineesQuery,
  useLazyGetSingleTraineeDataQuery,
  useAddTraineeMutation,
  useUpdateTraineeMealMacrosSettingsMutation,

  // ----------------Meal----------------
  useGetTraineeMealPlansQuery,
  useGetAllFoodItemsQuery,
  useDeleteMealFromMealPlanMutation,
  useAddMealToMealPlanMutation,
  useUpdateMealInMealPlanMutation,

  useCreateMealTemplateMutation,
  useGetAllMealTemplatesQuery,
  useUpdateMealTemplateMutation,

  useLoadMealFromMealTemplatesMutation,

  // ----------------training----------------
  useGetAllExercisesQuery,

  useLazyGetTraineeTrainingPlansQuery,

  useAddTrainingPlanMutation,
  useUpdateTrainingPlanMutation,
  useDeleteTrainingPlanMutation,

  useGetTraineeWorkoutLogsQuery,
  useGetTraineeLastWeekWorkoutLogsQuery,

  useGetAllTrainingPlanTemplateQuery,
  useCreateTrainingPlanTemplateMutation,
  useUpdateTrainingPlanTemplateMutation,
  useDeleteTrainingPlanTemplateMutation,
  useLoadTrainingPlanFromTemplateMutation,
} = trainerApi;
