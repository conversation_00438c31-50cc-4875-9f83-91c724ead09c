import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { BASE_URL } from "../../api-utils";

export const traineeApi = createApi({
  reducerPath: "traineeApi",
  keepUnusedDataFor: 0,
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: (headers) => {
      const token = localStorage.getItem("access_token");

      if (token) {
        headers.set("Authorization", `Bearer ${token}`);
      }

      return headers;
    },
  }),

  endpoints: (builder) => ({
    // -------------------- Profile --------------------------------------
    getProfileDetails: builder.query({
      query: (traineeId) => ({
        url: `trainer-assignments/trainee/${traineeId}`,
        method: "GET",
      }),
    }),

    // -------------------- Meal --------------------------------------
    getMealPlan: builder.query({
      query: ({ userId }) => ({
        url: `meals/trainee/${userId}`,
        method: "GET",
      }),
    }),

    // -------------------- Training --------------------------------------
    getTrainingPlan: builder.query({
      query: ({ userId }) => ({
        url: `training-plans/user/${userId}`,
        method: "GET",
      }),
    }),

    addWorkoutLog: builder.mutation({
      query: (body) => ({
        url: `workout-logs`,
        method: "POST",
        body,
      }),
    }),

    addWorkoutSet: builder.mutation({
      query: (body) => {
        return {
          url: `workout-sets`,
          method: "POST",
          body,
        };
      },
    }),

    completeWorkoutSet: builder.mutation({
      query: ({ body, workoutLogId }) => ({
        url: `workout-logs/${workoutLogId}`,
        method: "PUT",
        body,
      }),
    }),
  }),
});

export const {
  // ----------------Profile----------------
  useGetProfileDetailsQuery,

  // ----------------meal----------------
  useGetMealPlanQuery,

  // ----------------training----------------
  useGetTrainingPlanQuery,
  useAddWorkoutLogMutation,
  useAddWorkoutSetMutation,
  useCompleteWorkoutSetMutation,
} = traineeApi;
