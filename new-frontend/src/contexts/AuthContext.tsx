import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import { useNavigate } from "react-router-dom";

// Define types
interface User {
  id: string;
  email: string;
  name: string;
  isActive: boolean;
  role: {
    id: number;
    name: string;
  };
}

interface AuthContextType {
  user: User | null;
  login: (userData: any) => void;
  logout: () => void;
  isAuthenticated: boolean;
  setIsAuthenticated: (auth: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Define props type
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const navigate = useNavigate();

  // Retrieve user data from localStorage and parse it
  const storedUser = localStorage.getItem("user");
  const parsedUser: User | null = storedUser ? JSON.parse(storedUser) : null;

  const [user, setUser] = useState<User | null>(parsedUser);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(!!parsedUser);

  useEffect(() => {
    const token = localStorage.getItem("access_token");
    if (token && parsedUser) {
      setIsAuthenticated(true);

      if (parsedUser.role.name === "ROLE_ADMIN") {
        navigate("/admin");
      } else {
        navigate("/homepage");
      }
    }
  }, []);

  const login = (userData: any) => {
    setIsAuthenticated(true);
    setUser(userData.user);

    localStorage.setItem("access_token", userData.jwtToken);
    localStorage.setItem("user", JSON.stringify(userData.user));
    localStorage.setItem("role", userData.user.role.name);

    if (userData.user.role.name === "ROLE_ADMIN") {
      navigate("/admin");
    } else {
      navigate("/homepage");
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.clear();
    navigate("/login");
  };

  return (
    <AuthContext.Provider
      value={{ user, login, logout, isAuthenticated, setIsAuthenticated }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook for authentication context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
