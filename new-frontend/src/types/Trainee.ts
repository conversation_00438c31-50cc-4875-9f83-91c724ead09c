export interface Trainee {
  id: string;
  name: string;
  email: string;

  age: number;
  height: number;
  weight: number;
  gender: string;

  goal: string;
  level: string;
  activityLevel: string;

  avatar: string;
  mobileNumber: string;

  approved: boolean;
  isActive: boolean;
  startDate: string;
  endDate: string;

  lastActive: string;
  status: string;
  performance: number;
  overdueTasks: number;
  attentionNote: null;
  startingFatPercentage: number;
  startingWeight: number;
  currentWeight?: number;
  currentFatPercentage?: number;
}
