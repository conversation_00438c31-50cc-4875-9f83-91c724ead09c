## Setup Instructions

### 1. **Environment Variables**

Create a `.env` file in the root directory with the following variables:

```bash
# General
VITE_BACKEND_BASE_URL = "Backend Url"
```

### 2. Project setup

Run this command in terminal to setup the project first.

```bash
$ npm install
```

### 3. Compile and run the project

Run this command in terminal to compile and run the project.

```bash
# development
$ npm run dev
```
