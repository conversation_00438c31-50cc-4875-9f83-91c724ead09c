[project]
name = "agents"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "flask>=3.1.0",
    "openai>=1.74.0",
    "pinecone>=6.0.2",
    "pinecone-client>=6.0.0",
    "pydantic-settings>=2.8.1",
    "pyngrok>=7.2.4",
    "tiktoken>=0.9.0",
    "twilio>=9.5.2",
    "redis>=5.0.0",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
]

[tool.pytest.ini_options]
pythonpath = "src"
