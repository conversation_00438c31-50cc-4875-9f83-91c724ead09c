# Recommended API Call Flow

This document outlines the recommended order for calling the Fitness Coach AI API endpoints in different scenarios.

## Basic User Interaction Flow

For a typical user interaction, follow this sequence:

1. **Send User Message** (`/api/chat`)
   - This is the main endpoint that processes user input through the complete agent workflow
   - The system will automatically:
     - Add the message to memory
     - Classify the domain
     - Route to the appropriate agent
     - Generate a response
     - Update user profile and progress
     - Check for needed follow-ups

2. **View Conversation History** (`/api/memory/short-term`)
   - After sending messages, check the conversation history to see the context

3. **Check Progress** (`/api/progress`)
   - Periodically check the user's progress data to see tracked events

4. **Generate Weekly Summary** (`/api/progress/weekly-summary`)
   - Weekly, generate a summary of the user's progress

## Advanced Scenarios

### Debugging Classification

If you need to debug how messages are being classified:

1. **Test Classification** (`/api/classify`)
   - Send a message to see how it would be classified without generating a response
2. **Try Direct Agent Access** (`/api/agents/{agent}`)
   - Test specific agents directly to compare responses

### Managing Long Conversations

For managing long-running conversations:

1. **Check Conversation Length** (`/api/memory/short-term`)
   - See how many messages are in the conversation
2. **Force Summarization** (`/api/memory/summarize`)
   - If the conversation is getting too long, force a summarization

### Setting Up Automated Follow-ups

For scheduling follow-up messages:

1. **Schedule a Checkup** (`/api/checkups/schedule`)
   - Set up a follow-up message to be sent at a specific time
2. **View Scheduled Checkups** (`/api/checkups`)
   - Check what follow-ups are scheduled

## Complete Testing Sequence

For a complete test of the system, follow this sequence:

1. **Send User Message** (`/api/chat`)
2. **Check Classification** (`/api/classify`)
3. **Test Each Agent Directly**:
   - Nutrition Agent (`/api/agents/nutrition`)
   - Social Event Agent (`/api/agents/social`)
   - General Agent (`/api/agents/general`)
4. **Check Memory**:
   - View Short-Term Memory (`/api/memory/short-term`)
   - Summarize Conversation (`/api/memory/summarize`)
5. **Check Progress**:
   - Get Progress Data (`/api/progress`)
   - Generate Weekly Summary (`/api/progress/weekly-summary`)
6. **Test Checkups**:
   - Schedule a Checkup (`/api/checkups/schedule`)
   - View Scheduled Checkups (`/api/checkups`)
7. **Check User State**:
   - Get Full User State (`/api/user/state`)
   - Get User Profile (`/api/user/profile`)
   - Get Audit Log (`/api/audit-log`)
8. **Run Quick Test** (`/api/test`)

## Execution Script

For convenience, you can use the provided `CURL_COMMANDS.sh` script to test all endpoints in sequence:

```bash
chmod +x CURL_COMMANDS.sh
./CURL_COMMANDS.sh
```

Or test specific endpoints:

```bash
./CURL_COMMANDS.sh | grep -A 20 'CHAT ENDPOINT'
```
