# Agent Workflow Diagram

The following diagram illustrates the flow of a user message through the Fitness Coach AI system:

```
┌─────────────────┐
│                 │
│   User Input    │
│                 │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  Memory Storage │
│                 │
│ 1. Short-term   │
│ 2. Long-term    │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│                 │
│ Classification  │
│     Agent       │
│                 │
└────────┬────────┘
         │
         ▼
         │
    ┌────┴─────┐
    │          │
    ▼          ▼          ▼
┌─────────┐ ┌─────────┐ ┌─────────┐
│ Social  │ │Nutrition│ │ General │
│  Agent  │ │  Agent  │ │  Agent  │
└────┬────┘ └────┬────┘ └────┬────┘
     │           │           │
     └───────────┼───────────┘
                 │
                 ▼
        ┌─────────────────┐
        │                 │
        │    Response     │
        │                 │
        └────────┬────────┘
                 │
                 ▼
        ┌─────────────────┐
        │ Memory Storage  │
        │                 │
        │ 1. Short-term   │
        │ 2. Long-term    │
        └────────┬────────┘
                 │
                 ▼
        ┌─────────────────┐
        │  User Profile   │
        │     Update      │
        └────────┬────────┘
                 │
                 ▼
        ┌─────────────────┐
        │Progress Tracking│
        │                 │
        │ 1. Missed meals │
        │ 2. Unplanned    │
        │    foods        │
        │ 3. Social events│
        └────────┬────────┘
                 │
                 ▼
        ┌─────────────────┐
        │   Automated     │
        │   Checkups      │
        └─────────────────┘
```

## API Endpoints Workflow

The API endpoints are organized to support this workflow:

1. **Main Processing Flow**:
   - `/api/chat` - Primary endpoint that handles the complete workflow

2. **Component Access**:
   - `/api/classify` - Just classification
   - `/api/agents/*` - Direct access to specific agents
   - `/api/memory/*` - Memory management
   - `/api/progress/*` - Progress tracking
   - `/api/checkups/*` - Automated checkups
   - `/api/user/*` - User state management

3. **Testing & Debugging**:
   - `/api/test` - Quick test of the system
   - `/api/audit-log` - View interaction history

## Execution Order in the Orchestrator

The `FitnessOrchestrator.process_user_input()` method executes these steps in order:

1. Add user input to short-term memory
2. Upsert user input to long-term store
3. Classify domain
4. Route to correct agent
5. Store agent response in short-term & long-term memory
6. Summarize older conversation if it grows large
7. Update user profile and progress analytics
8. Check if a weekly summary is requested or if it's time to show progress
9. Schedule any needed follow-up checkups

This workflow ensures that each user message is properly processed, classified, and responded to while maintaining context and tracking progress over time.
