from typing import Dict, List, Any, Optional, Tuple
import time
from datetime import datetime, date
import re

class ContradictionService:
    """
    Service for detecting contradictions in user messages, especially about meals.
    """
    
    # Patterns for meal completion statements
    MEAL_COMPLETED_PATTERNS = [
        r"(?:i|I)\s+(?:had|ate|consumed|finished|completed)\s+(?:my\s+)?(?:breakfast|lunch|dinner|meal|supper)",
        r"(?:i|I)\s+(?:just|already)\s+(?:had|ate|consumed|finished|completed)\s+(?:my\s+)?(?:breakfast|lunch|dinner|meal|supper)",
        r"(?:i|I)'ve\s+(?:had|eaten|consumed|finished|completed)\s+(?:my\s+)?(?:breakfast|lunch|dinner|meal|supper)",
        r"(?:i|I)\s+(?:had|ate|consumed|finished|completed)\s+(?:my\s+)?(?:meal\s+\d+|meal\s+[a-zA-Z]+)"
    ]
    
    # Patterns for meal missed statements
    MEAL_MISSED_PATTERNS = [
        r"(?:i|I)\s+(?:missed|skipped|didn't\s+have|did\s+not\s+have|couldn't\s+have|could\s+not\s+have)\s+(?:my\s+)?(?:breakfast|lunch|dinner|meal|supper)",
        r"(?:i|I)\s+(?:missed|skipped|didn't\s+have|did\s+not\s+have|couldn't\s+have|could\s+not\s+have)\s+(?:my\s+)?(?:meal\s+\d+|meal\s+[a-zA-Z]+)",
        r"(?:i|I)\s+(?:haven't|haven't\s+yet|have\s+not|have\s+not\s+yet)\s+(?:had|eaten|consumed|finished|completed)\s+(?:my\s+)?(?:breakfast|lunch|dinner|meal|supper)",
        r"(?:i|I)\s+(?:won't|will\s+not)\s+(?:have|eat|consume|finish|complete)\s+(?:my\s+)?(?:breakfast|lunch|dinner|meal|supper)"
    ]
    
    @classmethod
    def extract_meal_type(cls, message: str) -> Optional[str]:
        """
        Extract the meal type from a message.
        
        Args:
            message: The user message
            
        Returns:
            The meal type (breakfast, lunch, dinner, etc.) or None if not found
        """
        meal_types = ["breakfast", "lunch", "dinner", "supper", "meal"]
        
        # Check for specific meal types
        for meal_type in meal_types:
            if meal_type in message.lower():
                # Check if it's a numbered meal
                if meal_type == "meal":
                    meal_number_match = re.search(r"meal\s+(\d+|[a-zA-Z]+)", message.lower())
                    if meal_number_match:
                        return meal_number_match.group(0)
                return meal_type
        
        return None
    
    @classmethod
    def is_meal_completed_message(cls, message: str) -> bool:
        """
        Check if the message indicates a meal was completed.
        
        Args:
            message: The user message
            
        Returns:
            True if the message indicates a meal was completed, False otherwise
        """
        for pattern in cls.MEAL_COMPLETED_PATTERNS:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def is_meal_missed_message(cls, message: str) -> bool:
        """
        Check if the message indicates a meal was missed.
        
        Args:
            message: The user message
            
        Returns:
            True if the message indicates a meal was missed, False otherwise
        """
        for pattern in cls.MEAL_MISSED_PATTERNS:
            if re.search(pattern, message, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def check_for_contradiction(cls, conversation_db, user_input: str) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Check if the current message contradicts previous messages about meals.
        
        Args:
            conversation_db: The conversation database
            user_input: The current user message
            
        Returns:
            A tuple of (has_contradiction, contradiction_type, meal_type)
            - has_contradiction: True if a contradiction was detected
            - contradiction_type: The type of contradiction ('completed_then_missed' or 'missed_then_completed')
            - meal_type: The meal type involved in the contradiction
        """
        # Check if the current message is about completing or missing a meal
        is_completed = cls.is_meal_completed_message(user_input)
        is_missed = cls.is_meal_missed_message(user_input)
        
        # If the message is not about meal completion or missing, no contradiction
        if not is_completed and not is_missed:
            return False, None, None
        
        # Extract the meal type from the current message
        current_meal_type = cls.extract_meal_type(user_input)
        if not current_meal_type:
            return False, None, None
        
        # Get recent messages from today
        today = date.today()
        recent_docs = conversation_db.get_recent_documents(n=20)
        
        # Filter for user messages from today
        today_user_messages = []
        for doc in recent_docs:
            if doc["metadata"].get("role") == "user":
                timestamp = doc["metadata"].get("timestamp", 0)
                message_date = date.fromtimestamp(timestamp)
                
                if message_date == today and doc["content"] != user_input:
                    today_user_messages.append(doc["content"])
        
        # Check for contradictions
        for previous_message in today_user_messages:
            previous_meal_type = cls.extract_meal_type(previous_message)
            
            # If the previous message is about the same meal type
            if previous_meal_type and previous_meal_type == current_meal_type:
                previous_completed = cls.is_meal_completed_message(previous_message)
                previous_missed = cls.is_meal_missed_message(previous_message)
                
                # Check for contradictions
                if is_completed and previous_missed:
                    return True, "missed_then_completed", current_meal_type
                elif is_missed and previous_completed:
                    return True, "completed_then_missed", current_meal_type
        
        return False, None, None
    
    @classmethod
    def generate_contradiction_response(cls, contradiction_type: str, meal_type: str) -> str:
        """
        Generate a response for a detected contradiction.
        
        Args:
            contradiction_type: The type of contradiction ('completed_then_missed' or 'missed_then_completed')
            meal_type: The meal type involved in the contradiction
            
        Returns:
            A response message addressing the contradiction
        """
        if contradiction_type == "completed_then_missed":
            return f"אני מבולבל מעט. קודם אמרת שאכלת את ה{meal_type} שלך, ועכשיו אתה אומר שפספסת אותו. האם תוכל להבהיר מה קרה בדיוק?"
        elif contradiction_type == "missed_then_completed":
            return f"אני מבולבל מעט. קודם אמרת שפספסת את ה{meal_type} שלך, ועכשיו אתה אומר שאכלת אותו. האם תוכל להבהיר מה קרה בדיוק?"
        else:
            return "אני מזהה סתירה בדבריך. האם תוכל להבהיר את המצב?"
