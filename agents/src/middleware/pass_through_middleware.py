from typing import Dict, Any, Optional

class ContradictionMiddleware:
    """
    Pass-through middleware that replaces the contradiction detection middleware.
    
    This middleware always returns None, effectively passing all messages through
    without any processing while maintaining the same interface as the original middleware.
    """
    
    def __init__(self):
        """Initialize the pass-through middleware."""
        pass
    
    def process(self, user_input: str, global_state: Dict[str, Any]) -> Optional[str]:
        """
        Process the user input but always return None (pass-through behavior).
        
        Args:
            user_input: The user message
            global_state: The global state dictionary
            
        Returns:
            Always returns None, indicating no intervention is needed
        """
        # Always return None to pass messages through without processing
        return None
