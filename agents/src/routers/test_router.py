from fastapi import APIRouter, Depends
from typing import Dict, Any

from src.services import UserService, OrchestratorService
from src.models import TestResponse

router = APIRouter(prefix="/api/test", tags=["test"])

def get_orchestrator(user_id: str = None):
    """
    Dependency to get a user-specific orchestrator instance.

    Args:
        user_id: The user ID to get an orchestrator for.
                If None, returns the default orchestrator.

    Returns:
        A FitnessOrchestrator instance specific to the user
    """
    return OrchestratorService.get_orchestrator(user_id)

@router.get("", response_model=TestResponse)
async def quick_test(orchestrator_instance = Depends(get_orchestrator)):
    """
    Run a quick test of the system
    """
    user_id = "test_user"

    # Get user-specific orchestrator
    user_orchestrator = OrchestratorService.get_orchestrator(user_id)

    user_state = UserService.get_user_state(user_id)

    sample_input = "I missed my second meal today, it was supposed to be rice and chicken"
    bot_reply = user_orchestrator.process_user_input(sample_input, user_state)

    # Retrieve short-term docs and audit log
    short_term_docs = []
    if "conversation_db" in user_state:
        short_term_docs = user_state["conversation_db"].get_recent_documents(n=10)

    audit_log = user_state.get("audit_log", [])

    return {
        "bot_reply": bot_reply,
        "short_term_docs": [
            {"role": doc.get("metadata", {}).get("role", "?"), "content": doc["content"]}
            for doc in short_term_docs
        ],
        "audit_log": audit_log
    }
