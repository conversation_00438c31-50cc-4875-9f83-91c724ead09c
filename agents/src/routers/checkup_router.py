from fastapi import APIRouter
from typing import Dict, Any

from src.services import CheckupService
from src.models import CheckupRequest, CheckupResponse, CheckupsListResponse

router = APIRouter(prefix="/api/checkups", tags=["checkups"])

@router.post("/schedule", response_model=CheckupResponse)
async def schedule_checkup(request: CheckupRequest):
    """
    Schedule a checkup message to be sent to the user
    """
    return CheckupService.schedule_checkup(
        request.user_id,
        request.minutes_from_now,
        request.message
    )

@router.get("", response_model=CheckupsListResponse)
async def get_checkups(user_id: str = "default_user", include_completed: bool = False):
    """
    Get all scheduled checkups for a user
    """
    checkups = CheckupService.get_checkups(user_id, include_completed)
    return {"checkups": checkups}
