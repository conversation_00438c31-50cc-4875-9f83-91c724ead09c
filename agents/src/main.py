from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
import sys
import os
import time

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.agents.orchestrator import FitnessOrchestrator
from src.config.settings import settings
from src.routers import (
    chat_router,
    memory_router,
    progress_router,
    checkup_router,
    user_router,
    test_router
)

from src.services.orchestrator_service import OrchestratorService
import uvicorn

# Initialize the FastAPI app
app = FastAPI(
    title="Fitness Coach AI API",
    description="API for the Fitness Coach AI system with multiple specialized agents",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5000", "http://localhost:5173", "http://************:8081"],  # Nest.js server, frontend, mobile
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# We don't initialize any specific orchestrator here - they will be created on-demand per user
print("Orchestrator service initialized successfully")

# Include the routers
app.include_router(chat_router)
app.include_router(memory_router)
app.include_router(progress_router)
app.include_router(checkup_router)
app.include_router(user_router)
app.include_router(test_router)

# Root endpoint
@app.get("/")
async def read_root():
    return {
        "message": "Welcome to the Fitness Coach AI API",
        "version": "1.0.0",
        "endpoints": {
            "main": "/api/chat",
            "classification": "/api/classify",
            "agents": {
                "nutrition": "/api/agents/nutrition",
                "social": "/api/agents/social",
                "general": "/api/agents/general"
            },
            "memory": {
                "short_term": "/api/memory/short-term",
                "summarize": "/api/memory/summarize",
                "summaries": "/api/memory/summaries"
            },
            "progress": {
                "get": "/api/progress",
                "weekly_summary": "/api/progress/weekly-summary"
            },
            "checkups": {
                "schedule": "/api/checkups/schedule",
                "get": "/api/checkups"
            },
            "user": {
                "state": "/api/user/state",
                "profile": "/api/user/profile"
            },
            "audit_log": "/api/audit-log",
            "test": "/api/test"
        }
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """
    Simple health check endpoint to verify the API is running
    and the orchestrator service is initialized
    """
    # Check if the orchestrator service is initialized
    try:
        # Create a test orchestrator to verify the service works
        test_orchestrator = OrchestratorService.get_orchestrator("health_check_user")
        orchestrator_ok = test_orchestrator is not None

        # Clean up the test orchestrator
        OrchestratorService.remove_orchestrator("health_check_user")
    except Exception as e:
        print(f"Error getting orchestrator: {e}")
        orchestrator_ok = False

    return {
        "status": "healthy" if orchestrator_ok else "unhealthy",
        "orchestrator_service_initialized": orchestrator_ok,
        "version": "1.0.0",
        "user_isolation": True  # Indicate that user isolation is enabled
    }

# Simple test endpoint that doesn't require the full chat workflow
@app.post("/test-message")
async def test_message(message: str = "Hello, I'm testing the API", user_id: str = None):
    """
    Simple test endpoint to verify the orchestrator is working
    without going through the full chat workflow.

    Args:
        message: The message to process
        user_id: Optional user ID to use. If not provided, a random test user ID will be generated.
    """
    try:
        # Generate a test user ID if not provided
        test_user_id = user_id or f"test_user_{int(time.time())}"

        # Get a user-specific orchestrator
        user_orchestrator = OrchestratorService.get_orchestrator(test_user_id)

        # Create a simple user state
        user_state = UserService.get_user_state(test_user_id)

        # Process the message
        response = user_orchestrator.process_user_input(message, user_state)

        return {
            "status": "success",
            "user_id": test_user_id,
            "message": message,
            "response": response,
            "user_isolation": True  # Indicate that user isolation is enabled
        }
    except Exception as e:
        print(f"Error processing test message: {e}")
        return {
            "status": "error",
            "message": message,
            "error": str(e)
        }

if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=8000, reload=True)
