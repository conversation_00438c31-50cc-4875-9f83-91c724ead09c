from src.agents.orchestrator import FitnessOrchestrator
from src.config.settings import settings
from typing import Dict
import time
from .redis_context_store import RedisContextStore

class OrchestratorService:
    """
    Service for managing user-specific orchestrator instances.
    Each user gets their own orchestrator to ensure data isolation.

    Orchestrator instances are kept in memory but user state is stored in Redis
    with a TTL to automatically expire inactive contexts.
    """
    _instance = None
    _orchestrators: Dict[str, FitnessOrchestrator] = {}
    _default_orchestrator = None
    _last_cleanup_time = 0
    _cleanup_interval = 3600  # Cleanup every hour

    @classmethod
    def get_instance(cls):
        """
        Get the singleton service instance
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    @classmethod
    def get_orchestrator(cls, user_id: str = None):
        """
        Get a user-specific orchestrator instance.

        Periodically cleans up orchestrator instances for users whose
        Redis context has expired.

        Args:
            user_id: The user ID to get an orchestrator for.
                    If None, returns a default orchestrator.

        Returns:
            A FitnessOrchestrator instance specific to the user
        """
        instance = cls.get_instance()

        # Periodically clean up expired orchestrators
        current_time = time.time()
        if current_time - instance._last_cleanup_time > instance._cleanup_interval:
            instance._cleanup_expired_orchestrators()
            instance._last_cleanup_time = current_time

        # If no user_id provided, use the default orchestrator
        if user_id is None:
            if instance._default_orchestrator is None:
                print("Initializing default orchestrator...")
                instance._default_orchestrator = FitnessOrchestrator(
                    openai_api_key=settings.OPENAI_API_KEY
                )
                print("Default orchestrator initialized successfully")
            return instance._default_orchestrator

        # Create a user-specific orchestrator if it doesn't exist
        if user_id not in instance._orchestrators:
            print(f"Initializing orchestrator for user {user_id}...")
            instance._orchestrators[user_id] = FitnessOrchestrator(
                openai_api_key=settings.OPENAI_API_KEY,
                user_id=user_id
            )
            print(f"Orchestrator for user {user_id} initialized successfully")

        # Reset TTL in Redis when accessing the orchestrator
        redis_store = RedisContextStore.get_instance()
        redis_store.reset_ttl(user_id)

        return instance._orchestrators[user_id]

    @classmethod
    def remove_orchestrator(cls, user_id: str):
        """
        Remove a user's orchestrator instance.
        Useful for cleanup or when a user is deleted.

        Args:
            user_id: The user ID to remove the orchestrator for
        """
        instance = cls.get_instance()
        if user_id in instance._orchestrators:
            del instance._orchestrators[user_id]
            print(f"Orchestrator for user {user_id} removed")

    @classmethod
    def _cleanup_expired_orchestrators(cls):
        """
        Clean up orchestrator instances for users whose Redis context has expired.
        This helps prevent memory leaks by removing orchestrators that are no longer needed.
        """
        instance = cls.get_instance()
        redis_store = RedisContextStore.get_instance()

        # Get all user IDs with orchestrators
        user_ids = list(instance._orchestrators.keys())

        # Check each user's TTL in Redis
        for user_id in user_ids:
            ttl = redis_store.get_ttl(user_id)

            # If TTL is -2, the key doesn't exist (expired)
            if ttl == -2:
                # Remove the orchestrator
                if user_id in instance._orchestrators:
                    del instance._orchestrators[user_id]
                    print(f"Cleaned up expired orchestrator for user {user_id}")

    @classmethod
    def get_active_user_count(cls):
        """
        Get the number of active users with orchestrators.

        Returns:
            The number of active users
        """
        instance = cls.get_instance()
        return len(instance._orchestrators)
