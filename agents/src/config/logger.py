"""
Centralized logger configuration for the Raeda AI agents.

This module provides a single logger instance that should be used throughout the agents codebase.
It configures logging to write to both file and console with appropriate formatting.
"""

import logging
import os
import pathlib

# Get the project root directory (parent of the agents directory)
project_root = pathlib.Path(__file__).parent.parent.parent.parent.absolute()
logs_dir = os.path.join(project_root, 'logs')

# Create logs directory if it doesn't exist
os.makedirs(logs_dir, exist_ok=True)

# Configure logging to write to both file and console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(logs_dir, 'raeda-ai.log')),
        logging.StreamHandler()
    ]
)

# Create a logger
logger = logging.getLogger("raeda-ai")

# Export the logger as a module-level variable
__all__ = ['logger']
