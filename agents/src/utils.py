import re
import json
import logging
from typing import List, Dict, Any
from src.config.settings import settings, ENCODING
import openai
from src.config.clients import client
from src.config.logger import logger

def summarize_text(text: str) -> str:
    """
    Summarizes long text into short bullet points or a concise paragraph.
    Uses GPT-4 or a capable model to generate a summary.
    """
    try:
        resp = client.chat.completions.create(
            model=settings.GPT_MODEL,
            messages=[
                {"role": "system", "content": "You are a helpful assistant that summarizes text."},
                {"role": "user", "content": text}
            ],
            temperature=0.2,
            max_tokens=150
        )
        return resp.choices[0].message.content.strip()
    except Exception as e:
        return f"[Error summarizing text: {str(e)}]"

def enhanced_summarize_text(text: str, purpose: str = "general") -> str:
    """
    Creates a summary optimized for semantic search.

    Args:
        text: The text to summarize
        purpose: The purpose of the summary (general, use_case_matching, etc.)
    """
    logger.info(f"Creating enhanced summary with purpose: {purpose}")
    logger.debug(f"Text length to summarize: {len(text)} characters")

    system_prompts = {
        "general": "You are a helpful assistant that summarizes text. Create a concise summary that captures the key points, topics, and information from this conversation chunk."
    }

    try:
        logger.debug(f"Using model: {settings.GPT_MODEL}")
        resp = client.chat.completions.create(
            model=settings.GPT_MODEL,
            messages=[
                {"role": "system", "content": system_prompts.get(purpose, system_prompts["general"])},
                {"role": "user", "content": text}
            ],
            temperature=0.2,
            max_tokens=150
        )
        summary = resp.choices[0].message.content.strip()
        logger.debug(f"Summary created successfully: {len(summary)} characters")
        return summary
    except Exception as e:
        logger.error(f"Error creating summary: {str(e)}")
        return f"[Error summarizing text: {str(e)}]"

def approximate_token_length(text: str) -> int:
    """Approximate the token length of `text` for the given model encoding."""
    return len(ENCODING.encode(text))

def _fallback_subsplit(long_text: str, max_tokens: int) -> List[str]:
    """
    If GPT returns a chunk that's still too large, we do a naive sentence-split fallback.
    """
    sentences = re.split(r'(?<=[.?!])\s+', long_text.strip())
    out, current_tokens, current_block = [], 0, []
    for sent in sentences:
        tlen = approximate_token_length(sent)
        if current_tokens + tlen <= max_tokens:
            current_block.append(sent)
            current_tokens += tlen
        else:
            out.append(" ".join(current_block))
            current_block = [sent]
            current_tokens = tlen
    if current_block:
        out.append(" ".join(current_block))
    return out

def llm_propose_chunks(text_slice: str, max_chunk_tokens: int = 1000) -> List[str]:
    """
    Calls GPT to propose a chunking scheme for `text_slice`,
    ensuring each chunk is <= `max_chunk_tokens` tokens.
    The return is a list of chunk strings, each presumably a cohesive subject-based block.

    If GPT fails or doesn't comply, we fallback to a naive single-chunk return.
    """
    system_prompt = f"""
You are a helpful chunking assistant. I will give you a slice of text.
You must break it into cohesive sections or paragraphs so that:
1) No section is longer than {max_chunk_tokens} tokens (approx).
2) Each section is logically self-contained (i.e. do not split a single subject mid-sentence).
3) Return your answer in JSON format: an array of chunk strings: ["chunk1", "chunk2", ...]

If the text is already short, just return one chunk.
Make sure your sections are returned as valid JSON with no extra keys.
"""
    user_content = f"TEXT SLICE:\n{text_slice}\n\nNow propose chunk boundaries."

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_content}
    ]

    try:
        resp = openai.ChatCompletion.create(
            model=settings.GPT_MODEL,
            messages=messages,
            temperature=0.0,
            max_tokens=1500
        )
        raw = resp.choices[0].message.content.strip()
        # Attempt to parse JSON
        data = json.loads(raw)

        # Ensure it's a list of strings
        if isinstance(data, list) and all(isinstance(x, str) for x in data):
            final_chunks = []
            for chunk in data:
                # If GPT's chunk is too big, do a naive fallback
                if approximate_token_length(chunk) > max_chunk_tokens:
                    final_chunks.extend(_fallback_subsplit(chunk, max_chunk_tokens))
                else:
                    final_chunks.append(chunk)
            return final_chunks
        else:
            # fallback if GPT didn't comply
            return [text_slice]
    except Exception as e:
        # fallback if GPT fails
        return [text_slice]

def llm_smart_chunk(document: str, max_tokens_per_chunk: int = 1000, slice_size: int = 3000) -> List[str]:
    """
    Iteratively chunk a large document by calling GPT in slices.
    - `slice_size`: how many tokens we feed GPT at once for chunking.
    - `max_tokens_per_chunk`: max tokens we allow in each final chunk.

    Process:
      1) We'll read up to `slice_size` tokens at a time from the doc.
      2) We feed it to GPT to propose chunk boundaries.
      3) If the last chunk in GPT's result is incomplete (mid-sentence or mid-topic),
         we keep it in 'carry_over' to prepend to the next iteration's slice.
      4) Continue until the entire doc is processed.
    """
    tokens = ENCODING.encode(document)
    doc_len = len(tokens)

    idx = 0
    chunks_out: List[str] = []
    carry_over = ""

    while idx < doc_len:
        slice_end = min(idx + slice_size, doc_len)
        slice_tokens = tokens[idx : slice_end]
        text_slice = ENCODING.decode(slice_tokens)
        # Prepend leftover from previous iteration
        slice_text_with_carry = carry_over + "\n" + text_slice if carry_over else text_slice

        # Let GPT propose chunk splits
        proposed = llm_propose_chunks(slice_text_with_carry, max_chunk_tokens=max_tokens_per_chunk)

        # Check if the last chunk might be incomplete
        if len(proposed) > 1:
            last_chunk = proposed[-1].strip()
            last_chunk_len = approximate_token_length(last_chunk)
            # If the last chunk is quite short or not ending in punctuation, we treat it as carry over
            if last_chunk_len < int(max_tokens_per_chunk / 5) or not re.search(r'[.!?)]\s*$', last_chunk):
                carry_over = last_chunk
                final_now = proposed[:-1]
            else:
                carry_over = ""
                final_now = proposed
        else:
            # GPT returned only 1 chunk
            chunk0 = proposed[0].strip()
            if (approximate_token_length(chunk0) < int(max_tokens_per_chunk / 5)
               or not re.search(r'[.!?)]\s*$', chunk0)):
                carry_over = chunk0
                final_now = []
            else:
                carry_over = ""
                final_now = [chunk0]

        chunks_out.extend(final_now)
        idx = slice_end

    # If there's leftover carry_over after finishing
    if carry_over.strip():
        chunks_out.append(carry_over.strip())

    # Return final list of chunk strings
    return [c.strip() for c in chunks_out]

def embed_and_upsert_chunks_llm(document: str, doc_source: str, index_obj, max_tokens=800, slice_size=3000):
    """
    1) Runs llm_smart_chunk on the doc to produce coherent sub-topic chunks.
    2) Embeds each chunk with text-embedding-ada-002.
    3) Upserts to Pinecone with metadata: source, chunk_index, and chunk text.
    """
    # 1) Use GPT-based chunking
    chunks = llm_smart_chunk(document, max_tokens_per_chunk=max_tokens, slice_size=slice_size)

    for i, chunk in enumerate(chunks):
        chunk_id = f"{doc_source}-{i}"
        emb_resp = openai.Embedding.create(
            model="text-embedding-ada-002",
            input=chunk
        )
        vector = emb_resp["data"][0]["embedding"]
        meta = {
            "source": doc_source,
            "chunk_index": i,
            "text": chunk
        }
        # Upsert to Pinecone
        index_obj.upsert(vectors=[(chunk_id, vector, meta)])

    print(f"Upserted {len(chunks)} chunk(s) from doc_source '{doc_source}'")