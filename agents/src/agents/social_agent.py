from typing import Dict, Any
from src.config.system_prompts import system_prompts
from src.agents.base_agent import BaseAgent

class SocialEventAgent(BaseAgent):
    def __init__(self, openai_api_key: str, model_name: str = None):
        super().__init__(
            openai_api_key=openai_api_key,
            model_name=model_name,
            agent_name="SocialEventAgent",
            temperature=0.2,
            max_completion_tokens=4000
        )

    def handle_message(self, user_input: str, global_state: Dict[str, Any]) -> str:
        """
        Uses DynamicPromptBuilder to construct a prompt with the three-tier context structure:
        - 5 most recent conversation turns
        - 5 most recent summaries chronologically
        - 3 most relevant summaries from older history (with relevancy threshold)
        """
        # Build the system prompt
        system_prompt = self._build_prompt(
            user_input=user_input,
            global_state=global_state,
            base_prompt=system_prompts.SOCIAL_EVENT_SYSTEM_PROMPT,
            domain="social_event"
        )

        # Prepare messages for OpenAI API
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input}
        ]

        # Call OpenAI API
        assistant_response = self._call_openai_api(messages)

        # Log to audit
        self._log_to_audit(global_state, user_input, assistant_response)

        return assistant_response