from typing import Dict, Any, Optional, List
from src.config.system_prompts import system_prompts
from src.config.clients import client
from src.config.settings import settings
from src.utils import summarize_text
from src.prompt_builders import DynamicPromptBuilder
from src.config.logger import logger

class BaseAgent:
    """
    Base agent class that provides common functionality for all agent implementations.

    This class handles:
    1. Initialization with API key and model name
    2. Prompt building with DynamicPromptBuilder
    3. OpenAI API calls
    4. Audit logging
    5. Error handling

    All specific agent implementations should inherit from this class.
    """

    def __init__(self,
                 openai_api_key: str,
                 model_name: str = None,
                 agent_name: str = "BaseAgent",
                 temperature: float = 0.2,
                 max_completion_tokens: int = 4000):
        """
        Initialize the base agent.

        Args:
            openai_api_key: OpenAI API key
            model_name: Model name to use (defaults to settings.GPT_MODEL)
            agent_name: Name of the agent for logging purposes
            temperature: Temperature for OpenAI API calls
            max_completion_tokens: Maximum number of tokens for completion
        """
        self.api_key = openai_api_key
        self.model_name = model_name if model_name else settings.GPT_MODEL
        self.agent_name = agent_name
        self.temperature = temperature
        self.max_completion_tokens = max_completion_tokens
        self.prompt_builder = None  # Will be set when handling a message

    def _initialize_prompt_builder(self, global_state: Dict[str, Any]) -> None:
        """
        Initialize the prompt builder if not already done.

        Args:
            global_state: Global state dictionary containing longterm_store
        """
        longterm_store = global_state.get("longterm_store")
        if self.prompt_builder is None and longterm_store:
            self.prompt_builder = DynamicPromptBuilder(longterm_store)

    def _get_recent_conversation_turns(self, global_state: Dict[str, Any], n: int = 5) -> str:
        """
        Get the n most recent conversation turns.

        Args:
            global_state: Global state dictionary containing conversation_db
            n: Number of recent turns to retrieve

        Returns:
            String containing the recent conversation turns
        """
        conversation_db = global_state.get("conversation_db")
        last_turns_text = ""

        if conversation_db:
            recent_docs = conversation_db.get_recent_documents(n=n)
            for doc in recent_docs:
                role_label = doc["metadata"].get("role", "unknown").upper()
                last_turns_text += f"{role_label}: {doc['content']}\n"

        return last_turns_text

    def _build_prompt(self,
                     user_input: str,
                     global_state: Dict[str, Any],
                     base_prompt: str,
                     domain: str) -> str:
        """
        Build the prompt using either DynamicPromptBuilder or a fallback method.

        Args:
            user_input: User input message
            global_state: Global state dictionary
            base_prompt: Base system prompt to use
            domain: Domain for the prompt builder

        Returns:
            The constructed system prompt
        """
        # Retrieve user profile and progress data
        user_profile = global_state.get("profile", {})
        progress_data = global_state.get("progress", {})

        # Get recent conversation turns
        last_turns_text = self._get_recent_conversation_turns(global_state)

        # Initialize prompt builder if needed
        self._initialize_prompt_builder(global_state)

        # Build the dynamic prompt with structured context
        if self.prompt_builder:
            system_prompt = self.prompt_builder.build_prompt(
                user_input=user_input,
                base_prompt=base_prompt,
                user_profile=user_profile,
                recent_turns=last_turns_text,
                progress_data=progress_data,
                domain=domain
            )
        else:
            # Fallback to basic prompt if prompt builder is not available
            system_prompt = base_prompt
            profile_text = "\n".join([f"{k}: {v}" for k, v in user_profile.items()])
            progress_text = f"Missed Meals: {progress_data.get('missed_meals_count', 0)}\nUnplanned Foods: {progress_data.get('unplanned_food_count', 0)}\nSocial Events: {progress_data.get('social_events_count', 0)}"

            if profile_text:
                system_prompt += f"\n\nUSER PROFILE:\n{profile_text}"
            if last_turns_text:
                system_prompt += f"\n\nRecent Context (Last 5 Messages):\n{last_turns_text}"
            if progress_data:
                system_prompt += f"\n\nPROGRESS DATA:\n{progress_text}"
            system_prompt += "\n\nNow handle the user's message below.\n"

        return system_prompt

    def _call_openai_api(self, messages: List[Dict[str, str]]) -> str:
        """
        Call the OpenAI API with the given messages.

        Args:
            messages: List of message dictionaries

        Returns:
            The assistant's response
        """
        logger.debug(f"[{self.agent_name}] Calling OpenAI API with model: {self.model_name}")
        try:
            response = client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=self.temperature,
                max_completion_tokens=self.max_completion_tokens
            )
            response_text = response.choices[0].message.content.strip()
            logger.debug(f"[{self.agent_name}] Received response of length: {len(response_text)}")
            return response_text
        except Exception as e:
            logger.error(f"[{self.agent_name}] Error calling OpenAI API: {str(e)}")
            return f"מצטער, אירעה תקלה בעיבוד הבקשה: {str(e)}"

    def _log_to_audit(self, global_state: Dict[str, Any], user_input: str, assistant_response: str) -> None:
        """
        Log the interaction to the audit log.

        Args:
            global_state: Global state dictionary
            user_input: User input message
            assistant_response: Assistant's response
        """
        logger.debug(f"[{self.agent_name}] Logging interaction to audit log")

        if "audit_log" not in global_state:
            global_state["audit_log"] = []
            logger.debug(f"[{self.agent_name}] Initialized audit_log in global_state")

        global_state["audit_log"].append({
            "type": "agent_response",
            "agent": self.agent_name,
            "user_input": user_input,
            "response": assistant_response
        })

        logger.debug(f"[{self.agent_name}] Added entry to audit_log, total entries: {len(global_state['audit_log'])}")

    def handle_message(self, user_input: str, global_state: Dict[str, Any]) -> str:
        """
        Handle a user message. This method should be overridden by subclasses
        to provide domain-specific behavior.

        Args:
            user_input: User input message
            global_state: Global state dictionary

        Returns:
            The assistant's response
        """
        raise NotImplementedError("Subclasses must implement handle_message()")
