"""
Agent implementations for the Fitness Coach AI system.

This package contains the following agent implementations:
- BaseAgent: Base class for all agents
- ClassificationAgent: Classifies user messages into domains
- NutritionAgent: Handles nutrition-related queries
- SocialEventAgent: Handles social event-related queries
- GeneralAgent: Handles general queries
- FitnessOrchestrator: Orchestrates the agents
"""

from .base_agent import BaseAgent
from .classification_agent import ClassificationAgent
from .nutrition_agent import NutritionAgent
from .social_agent import SocialEventAgent
from .general_agent import GeneralAgent
from .orchestrator import FitnessOrchestrator

__all__ = [
    'BaseAgent',
    'ClassificationAgent',
    'NutritionAgent',
    'SocialEventAgent',
    'GeneralAgent',
    'FitnessOrchestrator'
]
