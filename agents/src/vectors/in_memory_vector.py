from typing import List, Dict, Any, Optional

class InMemoryVectorDB:
    """
    Stores the most recent conversation turns with metadata.
    Retrieve last N turns for short-term context.

    Each instance is specific to a user to ensure conversation isolation.
    """

    def __init__(self, user_id: Optional[str] = None):
        """
        Initialize the in-memory vector database.

        Args:
            user_id: The user ID to associate with this database instance
        """
        self.documents = []
        self.user_id = user_id

    def add_document(self, content: str, metadata: Dict[str, Any]):
        """
        Add a document to the in-memory database.

        Args:
            content: The text content of the document
            metadata: Additional metadata for the document
        """
        # Add user_id to metadata if available
        if self.user_id:
            metadata["user_id"] = self.user_id

        self.documents.append({"content": content, "metadata": metadata})

    def get_recent_documents(self, n: int = 5) -> List[Dict[str, Any]]:
        """
        Get the most recent n documents.

        Args:
            n: Number of recent documents to retrieve

        Returns:
            List of the most recent n documents
        """
        if not self.documents:
            return []
        start_idx = max(0, len(self.documents) - n)
        return self.documents[start_idx:]

    def clear(self):
        """
        Clear all documents from the database.
        """
        self.documents = []