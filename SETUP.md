# Raeda AI Setup Guide

This document provides step-by-step instructions for setting up and running the Raeda AI project, focusing on the agents service which uses FastAPI and uvicorn.

## System Architecture Overview

The Raeda AI project consists of several components:

1. **Agents Service**: A FastAPI application that handles AI agent logic (nutrition, social events, etc.)
2. **API Service**: A NestJS application that serves as the main backend API
3. **Frontend**: A React application for the web interface
4. **Mobile App**: A React Native application for mobile devices

This guide focuses primarily on setting up the **Agents Service**.

## Prerequisites

### System Requirements

- **Operating System**: Linux, macOS, or Windows
- **Python**: Version 3.12 or higher
- **Node.js**: Version 16 or higher (for API service)
- **Docker**: For running Redis (optional, you can install Redis directly)
- **Git**: For cloning the repository

### Required Services

- **Redis**: For context storage and user state management
- **Pinecone**: Vector database for long-term memory storage
- **OpenAI API**: For AI model access

## Step 1: Clone the Repository

```bash
git clone https://github.com/your-username/<PERSON>da-ai-.git
cd Raeda-ai-
```

## Step 2: Set Up Python Environment

### Option 1: Using venv (Standard Python)

```bash
# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Linux/macOS
source venv/bin/activate
# On Windows
venv\Scripts\activate

# Install dependencies
cd agents
pip install -e .

# Install FastAPI dependencies (not included in setup.py)
pip install fastapi uvicorn
```

### Option 2: Using uv (Faster Package Manager)

```bash
# Install uv if not already installed
pip install uv

# Create a virtual environment and install dependencies
cd agents
uv venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate
uv pip install -e .

# Install FastAPI dependencies (not included in setup.py)
uv pip install fastapi uvicorn
```

## Step 3: Update Dependencies (Optional)

For a more permanent solution, you can update the `setup.py` file to include FastAPI dependencies:

```bash
cd agents
```

Edit the `setup.py` file and add FastAPI dependencies to the `install_requires` list:

```python
setup(
    name="agents",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "flask>=3.1.0",
        "openai>=1.74.0",
        "pinecone>=6.0.2",
        "pinecone-client>=6.0.0",
        "pydantic-settings>=2.8.1",
        "pyngrok>=7.2.4",
        "tiktoken>=0.9.0",
        "twilio>=9.5.2",
        "redis>=5.0.0",
        "fastapi>=0.110.0",  # Add this line
        "uvicorn>=0.27.0",   # Add this line
    ],
    python_requires=">=3.12",
)
```

Similarly, if you're using `pyproject.toml`, update it to include FastAPI dependencies:

```python
[project]
name = "agents"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "flask>=3.1.0",
    "openai>=1.74.0",
    "pinecone>=6.0.2",
    "pinecone-client>=6.0.0",
    "pydantic-settings>=2.8.1",
    "pyngrok>=7.2.4",
    "tiktoken>=0.9.0",
    "twilio>=9.5.2",
    "redis>=5.0.0",
    "fastapi>=0.110.0",  # Add this line
    "uvicorn>=0.27.0",   # Add this line
]
```

After updating either file, reinstall the package:

```bash
pip install -e .
```

## Step 4: Set Up Environment Variables

Create a `.env` file in the `agents` directory with the following variables:

```
# OpenAI API
OPENAI_API_KEY=your_openai_api_key
GPT_MODEL=gpt-4.1-2025-04-14  # Or your preferred model

# Pinecone (Vector Database)
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=your_pinecone_index_name

# Redis (Context Store)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=  # Leave empty if no password
REDIS_TTL=50400  # 14 hours in seconds
```

## Step 5: Set Up Redis

### Option 1: Using Docker (Recommended)

The project includes a Docker Compose file for Redis. Run:

```bash
cd agents
chmod +x start_redis.sh
./start_redis.sh
```

This script will:
1. Start Redis using Docker Compose
2. Wait for Redis to be ready
3. Confirm when Redis is running

### Option 2: Manual Redis Installation

If you prefer to install Redis directly:

#### On Linux:
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
```

#### On macOS:
```bash
brew install redis
brew services start redis
```

#### On Windows:
Download and install Redis from [https://github.com/microsoftarchive/redis/releases](https://github.com/microsoftarchive/redis/releases)

## Step 6: Set Up Pinecone

1. Create an account at [pinecone.io](https://www.pinecone.io/)
2. Create a new index with the following settings:
   - Dimensions: 1536 (for OpenAI embeddings)
   - Metric: Cosine
   - Pod Type: Starter (for development)
3. Copy your API key, environment, and index name to your `.env` file

## Step 7: Running the Agents Service

### Development Mode

To run the agents service with hot reloading (recommended for development):

```bash
cd agents
python src/main.py
```

This will start the service at http://127.0.0.1:8000 with automatic reloading when code changes.

### Production Mode

For production, it's recommended to use uvicorn directly with multiple workers:

```bash
cd agents
uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Using Gunicorn (Production)

For production deployments, you can use Gunicorn with uvicorn workers:

```bash
cd agents
pip install gunicorn
gunicorn src.main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## Step 8: Verify the Setup

1. Open your browser and navigate to http://127.0.0.1:8000
2. You should see the API welcome message with available endpoints
3. Check the health endpoint at http://127.0.0.1:8000/health
4. The API documentation is available at http://127.0.0.1:8000/docs

## Step 9: Testing the API

You can test the API using the built-in documentation or with curl:

```bash
# Health check
curl http://127.0.0.1:8000/health

# Test message
curl -X POST "http://127.0.0.1:8000/test-message" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, I am testing the API", "user_id": "test_user"}'
```

## Troubleshooting

### Redis Connection Issues

If you encounter Redis connection errors:

1. Verify Redis is running: `redis-cli ping` should return `PONG`
2. Check your `.env` file for correct Redis configuration
3. If using Docker, ensure the container is running: `docker ps | grep redis`

### OpenAI API Issues

If you encounter OpenAI API errors:

1. Verify your API key is correct
2. Check if you have sufficient credits
3. Verify the model name is correct in your `.env` file

### Pinecone Issues

If you encounter Pinecone connection errors:

1. Verify your API key, environment, and index name
2. Check if your index is properly initialized
3. Ensure your Pinecone plan has not expired

### Missing Dependencies

If you encounter missing dependencies:

```bash
cd agents
pip install -e .  # This will reinstall all dependencies

# Make sure FastAPI and uvicorn are installed
pip install fastapi uvicorn

# If you're still missing dependencies, install them individually
pip install pydantic-settings redis pinecone-client openai tiktoken
```

## Additional Components (Optional)

### Setting Up the API Service (NestJS)

If you want to run the complete system, you'll also need to set up the API service:

```bash
cd api
npm install
npm run start:dev
```

The API service will be available at http://localhost:5000 (or the port specified in your `.env` file).

### Setting Up the Frontend

To run the frontend:

```bash
cd frontend
npm install
npm run dev
```

The frontend will be available at http://localhost:5173.

## Conclusion

You now have a working setup of the Raeda AI agents service. The service uses FastAPI with uvicorn for high performance and Redis for state management. For any issues or questions, please refer to the project documentation or open an issue on the repository.
