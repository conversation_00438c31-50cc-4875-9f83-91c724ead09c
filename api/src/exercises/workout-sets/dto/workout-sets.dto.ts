import { IsNotEmpty, <PERSON>UUI<PERSON>, <PERSON>Optional, IsInt, IsString } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateWorkoutSetDto {
  @ApiProperty({ 
    example: 'b2a3f68e-7c88-44a9-bbc6-123456789abc', 
    description: 'ID of the workout exercise to which this set belongs' 
  })
  @IsUUID()
  @IsNotEmpty()
  workoutExerciseId: string;

  @ApiProperty({ 
    example: 1, 
    description: 'Set number (e.g., 1 for first set, 2 for second set)' 
  })
  @IsNotEmpty()
  @IsInt()
  setNumber: number;

  @ApiPropertyOptional({ 
    example: 80, 
    description: 'Weight lifted in kg (optional)' 
  })
  @IsOptional()
  @IsInt()
  weight?: number;

  @ApiPropertyOptional({ 
    example: 12, 
    description: 'Number of reps performed (optional)' 
  })
  @IsOptional()
  @IsInt()
  reps?: number;

  @ApiPropertyOptional({ 
    example: 'Felt strong, increased weight from last session.', 
    description: 'Additional notes about the set (optional)' 
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class UpdateWorkoutSetDto extends PartialType(CreateWorkoutSetDto) {}
