import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkoutSetsService } from './workout-sets.service';
import { WorkoutSetsController } from './workout-sets.controller';
import { WorkoutSetEntity } from '../../models/excercise/workout-sets.entity';
import { RoleEntity } from '../../models/user-entity/role.entity';
import { AccessTokenEntity, UserEntity } from '../../models/user-entity';
import { WorkoutExerciseEntity } from 'src/models/excercise/workout-exercise.entity';
import { JwtService } from '@nestjs/jwt';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkoutSetEntity,
      RoleEntity,
      UserEntity,
      WorkoutExerciseEntity,
      AccessTokenEntity,
    ]),
  ],
  providers: [WorkoutSetsService, JwtService],
  controllers: [WorkoutSetsController],
})
export class WorkoutSetsModule {}
