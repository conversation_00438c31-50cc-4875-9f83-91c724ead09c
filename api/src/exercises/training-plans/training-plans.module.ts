import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingPlansService } from './training-plans.service';
import { TrainingPlansController } from './training-plans.controller';
import { TrainingPlanEntity } from '../../models/excercise/training-plans.entity';
import { RoleEntity } from '../../models/user-entity/role.entity';
import { AccessTokenEntity, UserEntity } from '../../models/user-entity';
import { JwtService } from '@nestjs/jwt';
import { TrainingExerciseEntity } from 'src/models/excercise/training-exercises.entity';
import { TrainerAssignmentEntity } from 'src/models/profiles/trainer-trainee-assignment.entity';
import {
  ExerciseEntity,
  TrainingPlanTemplateEntity,
} from 'src/models/excercise';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TrainingPlanEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
      TrainingPlanEntity,
      TrainingExerciseEntity,
      TrainerAssignmentEntity,
      ExerciseEntity,
      TrainingPlanTemplateEntity,
    ]),
  ],
  controllers: [TrainingPlansController],
  providers: [TrainingPlansService, JwtService],
})
export class TrainingPlansModule {}
