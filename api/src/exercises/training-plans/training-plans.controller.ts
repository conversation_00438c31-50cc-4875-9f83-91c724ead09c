import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseGuards,
  Request,
  ForbiddenException,
} from '@nestjs/common';
import { TrainingPlansService } from './training-plans.service';
import {
  CreateTrainingPlanDto,
  UpdateTrainingPlanDto,
} from './dto/training-plans.dto';
import { TrainingPlanEntity } from '../../models/excercise/training-plans.entity';
import { RolesGuard } from '../../security/middleware/rolesGuard.middleware';
import { Roles } from '../../security/middleware/roles.decorator';
import { ROLE_VALUES } from '../../models/user-entity/role.entity';
import { UserEntity } from '../../models/user-entity/user.entity';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { CreateTrainingPlanDataFromTemplateDTO } from './dto/CreateTrainingPlanDataFromTemplat.dto';

@Controller('training-plans')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TrainingPlansController {
  constructor(private readonly trainingPlansService: TrainingPlansService) {}

  @Get()
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER) // Both can view plans
  async findAll(): Promise<TrainingPlanEntity[]> {
    return this.trainingPlansService.findAll();
  }

  @Get('user/:userId')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER) // View plans by user
  async findByUser(
    @Param('userId') userId: string,
    @Request() req,
  ): Promise<TrainingPlanEntity[]> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingPlansService.getRoleById(
      trainer.roleId,
    );

    return this.trainingPlansService.findByUser(userId, req.user, roleValue);
  }

  @Get(':id')
  @Roles(ROLE_VALUES.TRAINEE, ROLE_VALUES.TRAINER) // View single plan
  async findOne(
    @Param('id') id: string,
    @Request() req,
  ): Promise<TrainingPlanEntity> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingPlansService.getRoleById(
      trainer.roleId,
    );

    return this.trainingPlansService.findOne(id, req.user, roleValue.value);
  }

  @Post()
  @Roles(ROLE_VALUES.TRAINER)
  async create(
    @Body() createTrainingPlanDto: CreateTrainingPlanDto,
    @Request() req,
  ): Promise<TrainingPlanEntity> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingPlansService.getRoleById(
      trainer.roleId,
    );

    if (roleValue.value !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can create training plans.');
    }

    return this.trainingPlansService.create(
      createTrainingPlanDto,
      req.user,
      roleValue.value,
    );
  }

  @Put(':id')
  @Roles(ROLE_VALUES.TRAINER) // Only trainers can update plans
  async update(
    @Param('id') id: string,
    @Body() updateTrainingPlanDto: UpdateTrainingPlanDto,
    @Request() req,
  ): Promise<TrainingPlanEntity> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingPlansService.getRoleById(
      trainer.roleId,
    );

    if (roleValue.value !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can update training plans.');
    }

    return this.trainingPlansService.update(
      id,
      updateTrainingPlanDto,
      req.user,
      roleValue.value,
    );
  }

  @Delete(':id')
  @Roles(ROLE_VALUES.TRAINER) // Only trainers can delete plans
  async remove(@Param('id') id: string, @Request() req): Promise<void> {
    const trainer: UserEntity = req.user;
    const roleValue = await this.trainingPlansService.getRoleById(
      trainer.roleId,
    );

    if (roleValue.value !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can delete training plans.');
    }

    return this.trainingPlansService.remove(id, req.user, roleValue.value);
  }

  @Post(':templateId')
  async createTrainingPlansFromTemplate(
    @Request() req,
    @Param('templateId') templateId: string,
    @Body() CreateTrainingPlanData: CreateTrainingPlanDataFromTemplateDTO,
  ) {
    const trainerId = req.user.id;
    const { traineeId } = CreateTrainingPlanData;

    return this.trainingPlansService.createTrainingsFromTemplate(
      templateId,
      traineeId,
      trainerId,
    );
  }
}
