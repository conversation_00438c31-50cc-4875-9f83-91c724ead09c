import { IsNotEmpty, IsString, IsUUID } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty } from '@nestjs/swagger';
import { CreateExerciseDto } from '../../dto/excercises.dto';
import { CreateTrainingExerciseDto } from '../../training-exercises/dto/training-excercises.dto';

// export class CreateTrainingPlanDto {
//   @ApiProperty({ 
//     example: 'Monday', 
//     description: 'Day of the week for the training plan' 
//   })
//   @IsNotEmpty()
//   @IsString()
//   day: string;

//   @ApiProperty({ 
//     example: 'Chest & Triceps', 
//     description: 'Focus area of the workout for the day' 
//   })
//   @IsNotEmpty()
//   @IsString()
//   focus: string;

//   @ApiProperty({ 
//     example: 'b2a3f68e-7c88-44a9-bbc6-123456789abc', 
//     description: 'User ID of the trainee to whom the training plan belongs' 
//   })
//   @IsUUID()
//   userId: string;
// }

// export class UpdateTrainingPlanDto extends PartialType(CreateTrainingPlanDto) {}


export class CreateTrainingPlanDto {
  @ApiProperty({ 
    example: 'Monday', 
    description: 'Day of the week for the training plan' 
  })
  @IsNotEmpty()
  @IsString()
  day: string;
  

  @ApiProperty({ 
    example: 'Chest & Triceps', 
    description: 'Focus area of the workout for the day' 
  })
  @IsNotEmpty()
  @IsString()
  focus: string;

  @ApiProperty({ 
    example: 'b2a3f68e-7c88-44a9-bbc6-123456789abc', 
    description: 'Trainee ID to whom this training plan is assigned'
  })
  @IsString()
  traineeId: string;

  @ApiProperty({ 
    type: [CreateExerciseDto], 
    description: 'List of exercises for the training plan' 
  })
  exercises: CreateTrainingExerciseDto[];
}


export class UpdateTrainingPlanDto extends PartialType(CreateTrainingPlanDto) {}
