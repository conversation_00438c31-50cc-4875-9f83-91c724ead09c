import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ExercisesService } from './exercises.service';
import { ExercisesController } from './exercises.controller';
import { ExerciseEntity } from '../models/excercise/exercises.entity';
import { RoleEntity } from '../models/user-entity/role.entity';
import { AccessTokenEntity, UserEntity } from '../models/user-entity';
import { JwtService } from '@nestjs/jwt';
import { TrainingExercisesModule } from './training-exercises/training-exercises.module';
import { TrainingPlansModule } from './training-plans/training-plans.module';
import { WorkoutExerciseModule } from './workout-exercise/workout-exercise.module';
import { WorkoutLogsModule } from './workout-logs/workout-logs.module';
import { WorkoutSetsModule } from './workout-sets/workout-sets.module';
import { TrainingPlanTemplatesModule } from './training-plan-templates/training-plan-templates.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ExerciseEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
    ]),
    TrainingExercisesModule,
    TrainingPlansModule,
    WorkoutExerciseModule,
    WorkoutLogsModule,
    WorkoutSetsModule,
    TrainingPlanTemplatesModule,
  ],
  controllers: [ExercisesController],
  providers: [ExercisesService, JwtService],
})
export class ExercisesModule {}
