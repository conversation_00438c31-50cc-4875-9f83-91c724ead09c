import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Request,
  UseGuards,
} from '@nestjs/common';
import { Roles } from 'src/security/middleware/roles.decorator';
import { RolesGuard } from 'src/security/middleware/rolesGuard.middleware';
import { JwtAuthGuard } from 'src/security/middleware/authGuard.middleware';
import { ROLE_VALUES } from 'src/models/user-entity';
import { TrainingPlanTemplateService } from './training-plan-templates.service';
import {
  CreateTrainingPlanTemplateDto,
  TrainingPlanTemplateResponseDto,
  UpdateTrainingPlanTemplateDto,
} from './dtos';

@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('training-templates')
export class TrainingPlanTemplateController {
  constructor(private readonly service: TrainingPlanTemplateService) {}

  @Roles(ROLE_VALUES.TRAINER)
  @Get()
  getAll(@Request() req): Promise<TrainingPlanTemplateResponseDto[]> {
    return this.service.getAll(req.user.id);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Get(':id')
  getOne(
    @Param('id') id: string,
    @Request() req,
  ): Promise<TrainingPlanTemplateResponseDto> {
    return this.service.getOne(id, req.user.id);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Post()
  create(
    @Request() req,
    @Body() dto: CreateTrainingPlanTemplateDto,
  ): Promise<TrainingPlanTemplateResponseDto> {
    return this.service.create({ ...dto, trainerId: req.user.id });
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Put(':id')
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() dto: UpdateTrainingPlanTemplateDto,
  ): Promise<TrainingPlanTemplateResponseDto> {
    return this.service.update(id, req.user.id, dto);
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Delete(':id')
  delete(@Param('id') id: string, @Request() req): Promise<void> {
    return this.service.delete(id, req.user.id);
  }
}
