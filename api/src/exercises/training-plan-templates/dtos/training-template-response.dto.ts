import {
  IsUUID,
  IsString,
  IsDate,
  ValidateNested,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TrainingPlanTemplateEntity } from 'src/models/excercise';

class ExerciseDto {
  @IsUUID()
  exerciseId: string;

  @IsString()
  name?: string;

  @IsString()
  sets?: string;

  @IsString()
  reps?: string;

  @IsString()
  rest?: string;

  @IsString()
  instructions?: string;
}

class TemplateDayDto {
  @IsString()
  day: string;

  @IsString()
  focus: string;

  @ValidateNested({ each: true })
  @Type(() => ExerciseDto)
  exercises: ExerciseDto[];
}

export class TrainingPlanTemplateResponseDto {
  @IsUUID()
  id: string;

  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsUUID()
  trainerId: string;

  @IsDate()
  createdAt: Date;

  @IsDate()
  updatedAt: Date;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateDayDto)
  templateData: TemplateDayDto[];

  static transform(
    entity: TrainingPlanTemplateEntity,
  ): TrainingPlanTemplateResponseDto {
    const dto = new TrainingPlanTemplateResponseDto();

    dto.id = entity.id;
    dto.name = entity.name;
    dto.description = entity.description;
    dto.trainerId = entity.trainerId;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.templateData = entity.templateData.map((template) => ({
      day: template.day,
      focus: template.focus,
      exercises: template.exercises,
    }));

    return dto;
  }
}
