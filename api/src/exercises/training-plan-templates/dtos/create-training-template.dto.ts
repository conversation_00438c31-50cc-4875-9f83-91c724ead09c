import {
  IsNotEmpty,
  Is<PERSON>rray,
  ValidateNested,
  IsString,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

class ExerciseDto {
  @IsString()
  exerciseId: string;

  @IsString()
  name: string;

  @IsString()
  sets: string;

  @IsString()
  reps: string;

  @IsString()
  rest: string;

  @IsString()
  @IsOptional()
  instructions?: string;
}

class TemplateDayDto {
  @IsString()
  day: string;

  @IsString()
  focus: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExerciseDto)
  exercises: ExerciseDto[];
}

export class CreateTrainingPlanTemplateDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsOptional()
  @IsNotEmpty()
  description?: string;

  @IsString()
  @IsNotEmpty()
  trainerId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateDayDto)
  templateData: TemplateDayDto[];
}

export class UpdateTrainingPlanTemplateDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateDayDto)
  templateData?: TemplateDayDto[];
}
