import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TrainingPlanTemplateEntity } from 'src/models/excercise';
import {
  CreateTrainingPlanTemplateDto,
  TrainingPlanTemplateResponseDto,
  UpdateTrainingPlanTemplateDto,
} from './dtos';

@Injectable()
export class TrainingPlanTemplateService {
  constructor(
    @InjectRepository(TrainingPlanTemplateEntity)
    private readonly repo: Repository<TrainingPlanTemplateEntity>,
  ) {}

  async getAll(trainerId: string): Promise<TrainingPlanTemplateResponseDto[]> {
    const templates = await this.repo.find({
      where: { trainerId },
    });

    return templates.map(TrainingPlanTemplateResponseDto.transform);
  }

  async getOne(
    id: string,
    trainerId: string,
  ): Promise<TrainingPlanTemplateResponseDto> {
    const template = await this.repo.findOneBy({ id, trainerId });
    if (!template) throw new NotFoundException('Template not found');
    return TrainingPlanTemplateResponseDto.transform(template);
  }

  async create(
    dto: CreateTrainingPlanTemplateDto,
  ): Promise<TrainingPlanTemplateResponseDto> {
    if (!dto?.templateData?.length) {
      throw new BadRequestException('Add at least one day to template');
    }

    const template = this.repo.create({
      name: dto.name,
      description: dto.description,
      trainerId: dto.trainerId,
      templateData: dto.templateData,
    });

    const saved = await this.repo.save(template);

    return TrainingPlanTemplateResponseDto.transform(saved);
  }

  async update(
    id: string,
    trainerId: string,
    dto: UpdateTrainingPlanTemplateDto,
  ): Promise<TrainingPlanTemplateResponseDto> {
    const template = await this.repo.findOneBy({ id, trainerId });
    if (!template) throw new NotFoundException('Template not found');

    if (dto.name !== undefined) template.name = dto.name;

    if (dto.description !== undefined) template.description = dto.description;

    if ('templateData' in dto && dto.templateData) {
      template.templateData = dto.templateData.map((day) => ({
        ...day,
        exercises: day.exercises.map((exercise) => ({
          ...exercise,
          name: exercise.name || '', // Ensure name is not undefined
          sets: exercise.sets || '',
          reps: exercise.reps || '',
          rest: exercise.rest || '',
          instructions: exercise.instructions || '',
        })),
      }));
    }

    const updated = await this.repo.save(template);
    return TrainingPlanTemplateResponseDto.transform(updated);
  }

  async delete(id: string, trainerId: string): Promise<void> {
    const result = await this.repo.delete({ id, trainerId });
    if (result.affected === 0)
      throw new NotFoundException('Template not found');
  }
}
