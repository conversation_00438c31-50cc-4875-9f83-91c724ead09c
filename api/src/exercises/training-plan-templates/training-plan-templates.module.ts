import { Module } from '@nestjs/common';
import { TrainingPlanTemplateController } from './training-plan-templates.controller';
import { TrainingPlanTemplateService } from './training-plan-templates.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingPlanTemplateEntity } from 'src/models/excercise';
import { SecurityModule } from 'src/security/security.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TrainingPlanTemplateEntity]),
    SecurityModule,
  ],
  controllers: [TrainingPlanTemplateController],
  providers: [TrainingPlanTemplateService],
})
export class TrainingPlanTemplatesModule {}
