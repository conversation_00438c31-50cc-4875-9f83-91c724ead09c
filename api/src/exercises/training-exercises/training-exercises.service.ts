import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TrainingExerciseEntity } from '../../models/excercise/training-exercises.entity';
import { CreateTrainingExerciseDto, UpdateTrainingExerciseDto } from './dto/training-excercises.dto';
import { UserEntity } from '../../models/user-entity/user.entity';
import { ROLE_VALUES } from '../../models/user-entity/role.entity';
import { RoleEntity } from '../../models/user-entity/role.entity';

@Injectable()
export class TrainingExercisesService {
  constructor(
    @InjectRepository(TrainingExerciseEntity)
    private readonly trainingExerciseRepo: Repository<TrainingExerciseEntity>,
    @InjectRepository(RoleEntity)
    private roleRepository: Repository<RoleEntity>,
  ) {}

  /**
   * Get all training exercises (For Admins/Trainers)
   */
  async findAll(): Promise<TrainingExerciseEntity[]> {
    return this.trainingExerciseRepo.find({ relations: ['trainingPlan', 'exercise'] });
  }

  /**
   * Get all exercises for a specific training plan
   */
  async findByTrainingPlan(trainingPlanId: string): Promise<TrainingExerciseEntity[]> {
    return this.trainingExerciseRepo.find({
      where: { trainingPlan: { id: trainingPlanId } },
      relations: ['exercise'],
    });
  }

  /**
   * Get a single training exercise by ID
   */
  async findOne(id: string): Promise<TrainingExerciseEntity> {
    const exercise = await this.trainingExerciseRepo.findOne({
      where: { id },
      relations: ['trainingPlan', 'exercise'],
    });

    if (!exercise) throw new NotFoundException('Training exercise not found');

    return exercise;
  }

  /**
   * Create a new training exercise (Only Trainers)
   */
  async create(dto: CreateTrainingExerciseDto, trainer: UserEntity, roleValue): Promise<TrainingExerciseEntity> {
    if (roleValue    !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can add exercises to training plans.');
    }

    const trainingExercise = this.trainingExerciseRepo.create({ ...dto });
    return this.trainingExerciseRepo.save(trainingExercise);
  }

  /**
   * Update a training exercise (Only Trainers)
   */
  async update(id: string, updateDto: UpdateTrainingExerciseDto, trainer: UserEntity, roleValue): Promise<TrainingExerciseEntity> {
    const exercise = await this.findOne(id);

    if (roleValue !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can update training exercises.');
    }

    await this.trainingExerciseRepo.update(id, updateDto);
    return this.findOne(id);
  }

  /**
   * Delete a training exercise (Only Trainers)
   */
  async remove(id: string, trainer: UserEntity, roleValue): Promise<void> {
    const exercise = await this.findOne(id);

    if (roleValue !== ROLE_VALUES.TRAINER) {
      throw new ForbiddenException('Only trainers can delete training exercises.');
    }

    const result = await this.trainingExerciseRepo.delete(id);
    if (result.affected === 0) throw new NotFoundException('Training exercise not found');
  }

  async getRoleById(roleId: number): Promise<RoleEntity> {
    return this.roleRepository.findOne({ where: { id: roleId } });
  }
}

