import { IsNotEmpty, IsString, IsUUID, IsOptional } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateTrainingExerciseDto {
  @ApiProperty({
    example: 'b2a3f68e-7c88-44a9-bbc6-123456789abc',
    description: 'ID of the training plan to which this exercise belongs',
  })
  @IsUUID()
  @IsNotEmpty()
  trainingPlanId: string;

  @ApiProperty({
    example: '3c9f8abc-1234-5678-bcde-9876543210ab',
    description: 'ID of the exercise being added to the training plan',
  })
  @IsUUID()
  @IsNotEmpty()
  exerciseId: string;

  @ApiPropertyOptional({
    example: '4 sets',
    description: 'Number of sets for the exercise',
  })
  @IsOptional()
  @IsString()
  sets?: string;

  @ApiPropertyOptional({
    example: '10-12 reps',
    description: 'Number of reps per set',
  })
  @IsOptional()
  @IsString()
  reps?: string;

  @ApiPropertyOptional({
    example: '90 seconds',
    description: 'Rest time between sets',
  })
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  rest?: string;

  @ApiPropertyOptional({
    example: 'Maintain proper form, slow controlled reps',
    description: 'Instructions on how to perform the exercise',
  })
  @IsOptional()
  @IsString()
  instructions?: string;

  @ApiPropertyOptional({
    example: 'url',
    description: 'exercise url',
  })
  @IsOptional()
  @IsString()
  videoUrl?: string;
}

export class UpdateTrainingExerciseDto extends PartialType(
  CreateTrainingExerciseDto,
) {}
