import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainingExercisesService } from './training-exercises.service';
import { TrainingExercisesController } from './training-exercises.controller';
import { TrainingExerciseEntity } from '../../models/excercise/training-exercises.entity';
import { RoleEntity } from '../../models/user-entity/role.entity';
import { AccessTokenEntity, UserEntity } from '../../models/user-entity';
import { JwtService } from '@nestjs/jwt';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TrainingExerciseEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
    ]),
  ],
  providers: [TrainingExercisesService, JwtService],
  controllers: [TrainingExercisesController],
})
export class TrainingExercisesModule {}
