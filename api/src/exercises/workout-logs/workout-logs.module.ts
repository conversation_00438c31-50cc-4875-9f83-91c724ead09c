import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkoutLogsService } from './workout-logs.service';
import { WorkoutLogsController } from './workout-logs.controller';
import { WorkoutLogEntity } from '../../models/excercise/workout-logs.entity';
import { RoleEntity } from '../../models/user-entity/role.entity';
import { AccessTokenEntity, UserEntity } from '../../models/user-entity';
import { JwtService } from '@nestjs/jwt';
import { TrainingPlanEntity } from 'src/models/excercise/training-plans.entity';
import { WorkoutExerciseEntity } from 'src/models/excercise/workout-exercise.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkoutLogEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
      TrainingPlanEntity,
      WorkoutExerciseEntity,
    ]),
  ],
  providers: [WorkoutLogsService, JwtService],
  controllers: [WorkoutLogsController],
})
export class WorkoutLogsModule {}
