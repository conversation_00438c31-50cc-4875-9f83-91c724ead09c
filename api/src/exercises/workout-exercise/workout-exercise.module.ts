import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkoutExercisesController } from './workout-exercise.controller';
import { WorkoutExercisesService } from './workout-exercise.service';
import { WorkoutExerciseEntity } from 'src/models/excercise/workout-exercise.entity';
import { WorkoutLogEntity } from 'src/models/excercise/workout-logs.entity';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { JwtService } from '@nestjs/jwt';
import { ExerciseEntity } from 'src/models/excercise/exercises.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      WorkoutExerciseEntity,
      WorkoutLogEntity,
      RoleEntity,
      UserEntity,
      AccessTokenEntity,
      ExerciseEntity,
    ]),
  ],
  controllers: [WorkoutExercisesController],
  providers: [WorkoutExercisesService, JwtService],
})
export class WorkoutExerciseModule {}
