import { IsNotEmpty, IsUUID, IsString, IsOptional } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateWorkoutExerciseDto {
  @ApiProperty({
    example: 'b2a3f68e-7c88-44a9-bbc6-123456789abc',
    description: 'UUID of the workout log to which this exercise belongs',
  })
  @IsUUID()
  @IsNotEmpty()
  workoutLogId: string;

  @ApiProperty({
    example: '3c9f8abc-1234-5678-bcde-9876543210ab',
    description: 'UUID of the exercise being performed',
  })
  @IsUUID()
  @IsNotEmpty()
  exerciseId: string; // ✅ Renamed for clarity

  @ApiProperty({
    example: 'Bench Press',
    description: 'Custom name for the exercise (defaults to exercise name)',
  })
  @IsString()
  @IsOptional()
  name?: string;
}

export class UpdateWorkoutExerciseDto extends PartialType(CreateWorkoutExerciseDto) {}
