import { PermissionCategory } from './models/user-entity';
import { ROLE_VALUES } from './models/user-entity/role.entity';

const permissionsData = {
  [PermissionCategory.ADMIN]: [
    {
      name: 'Delete User',
      value: 'DELETE_USER',
      description:
        "User having this authority can delete another user's account",
    },
    {
      name: 'Update User',
      value: 'UPDATE_USER',
      description: "User having this authority can update another user's data",
    },
    {
      name: 'Add Single User',
      value: 'ADD_SINGLE_USER',
      description:
        'User having this authority can add a new user to their enterprise.',
    },
    {
      name: 'Add Bulk User',
      value: 'ADD_BULK_USERS',
      description:
        'User having this authority can add users in bulk via CSV file to their enterprise.',
    },

    {
      name: 'Get All Users List',
      value: 'GET_ALL_USERS_LIST',
      description:
        'User having this authority can retrieve the list of all users in the enterprise.',
    },
    {
      name: 'Get Single User Profile',
      value: 'GET_SINGLE_USER_PROFILE',
      description:
        'User having this authority can view the profile details of a single user in the enterprise.',
    },
  ],
};

export const PermissionsList = Object.entries(permissionsData).flatMap(
  ([category, permissions]) =>
    permissions.map((permission) => ({
      ...permission,
      category: category as PermissionCategory,
    })),
);

export const RolesList = [
  {
    name: 'Trainer',
    value: ROLE_VALUES.TRAINER, // Ensure this exists
    description: 'Manages training sessions',
    permissionValueList: ['PERMISSION_CREATE', 'PERMISSION_EDIT'],
  },
  {
    name: 'Trainee',
    value: ROLE_VALUES.TRAINEE, // Ensure this exists
    description: 'Attends training sessions',
    permissionValueList: ['PERMISSION_VIEW'],
  },
  {
    name: 'Admin',
    value: ROLE_VALUES.ADMIN, // Ensure this exists
    description: 'Has full access to the system',
    permissionValueList: [
      'PERMISSION_CREATE',
      'PERMISSION_EDIT',
      'PERMISSION_DELETE',
      'PERMISSION_VIEW',
    ],
  },
];

export const FoodItemsList = [
  // Protein Sources
  {
    name: 'חזה עוף',
    category: 'protein',
    macrosPer100g: {
      protein: 31,
      carbs: 0,
      fats: 3.6,
      calories: 165,
    },
    defaultServing: 170,
    minServing: 150,
    maxServing: 200,
  },
  {
    name: 'סלמון',
    category: 'protein',
    macrosPer100g: {
      protein: 25,
      carbs: 0,
      fats: 13,
      calories: 208,
    },
    defaultServing: 150,
    minServing: 130,
    maxServing: 180,
  },
  {
    name: 'אמנון',
    category: 'protein',
    macrosPer100g: {
      protein: 26,
      carbs: 0,
      fats: 2.7,
      calories: 128,
    },
    defaultServing: 170,
    minServing: 150,
    maxServing: 200,
  },
  {
    name: 'ביצים שלמות',
    category: 'protein',
    macrosPer100g: {
      protein: 13,
      carbs: 0.7,
      fats: 11,
      calories: 155,
    },
    defaultServing: 150, // ~3 eggs
    minServing: 100,
    maxServing: 200,
  },
  {
    name: 'טונה במים',
    category: 'protein',
    macrosPer100g: {
      protein: 26,
      carbs: 0,
      fats: 1,
      calories: 116,
    },
    defaultServing: 130,
    minServing: 100,
    maxServing: 160,
  },

  // Carb Sources
  {
    name: 'בטטה',
    category: 'carbs',
    macrosPer100g: {
      protein: 1.6,
      carbs: 20,
      fats: 0.1,
      calories: 86,
    },
    defaultServing: 300,
    minServing: 200,
    maxServing: 400,
  },
  {
    name: 'אורז',
    category: 'carbs',
    macrosPer100g: {
      protein: 2.7,
      carbs: 28,
      fats: 0.3,
      calories: 130,
    },
    defaultServing: 200,
    minServing: 150,
    maxServing: 300,
  },
  {
    name: 'תפוח אדמה',
    category: 'carbs',
    macrosPer100g: {
      protein: 2,
      carbs: 17,
      fats: 0.1,
      calories: 77,
    },
    defaultServing: 380,
    minServing: 300,
    maxServing: 450,
  },
  {
    name: 'שיבולת שועל',
    category: 'carbs',
    macrosPer100g: {
      protein: 13.5,
      carbs: 68,
      fats: 6.5,
      calories: 389,
    },
    defaultServing: 80,
    minServing: 60,
    maxServing: 100,
  },
  {
    name: 'לחם שיפון',
    category: 'carbs',
    macrosPer100g: {
      protein: 8.5,
      carbs: 43,
      fats: 1.2,
      calories: 216,
    },
    defaultServing: 120,
    minServing: 90,
    maxServing: 150,
  },

  // Fats
  {
    name: 'שמן זית',
    category: 'other',
    macrosPer100g: {
      protein: 0,
      carbs: 0,
      fats: 100,
      calories: 884,
    },
    defaultServing: 10,
    minServing: 5,
    maxServing: 15,
  },
  {
    name: 'חמאת בוטנים טבעית',
    category: 'other',
    macrosPer100g: {
      protein: 25,
      carbs: 20,
      fats: 50,
      calories: 589,
    },
    defaultServing: 15,
    minServing: 10,
    maxServing: 30,
  },

  // Other
  {
    name: 'אבקת חלבון',
    category: 'other',
    macrosPer100g: {
      protein: 80,
      carbs: 10,
      fats: 2,
      calories: 380,
    },
    defaultServing: 50,
    minServing: 30,
    maxServing: 60,
  },
  {
    name: 'בננה',
    category: 'other',
    macrosPer100g: {
      protein: 1.1,
      carbs: 23,
      fats: 0.3,
      calories: 89,
    },
    defaultServing: 120,
    minServing: 100,
    maxServing: 140,
  },
];

export const ExercisesList = [
  // Chest
  {
    name: 'לחיצת חזה',
    category: 'chest',
    defaultSets: '4 סטים',
    defaultReps: '8-12 חזרות',
    defaultRest: '90 שניות מנוחה',
    instructions: '2 שניות למטה, 1 שניה למעלה',
  },
  {
    name: 'לחיצת חזה בשיפוע',
    category: 'chest',
    defaultSets: '4 סטים',
    defaultReps: '8-12 חזרות',
    defaultRest: '90 שניות מנוחה',
  },
  {
    name: 'לחיצת חזה בירידה',
    category: 'chest',
    defaultSets: '3 סטים',
    defaultReps: '10-15 חזרות',
    defaultRest: '60 שניות מנוחה',
  },
  {
    name: 'פרפר עם משקולות',
    category: 'chest',
    defaultSets: '3 סטים',
    defaultReps: '12-15 חזרות',
    defaultRest: '60 שניות מנוחה',
  },

  // Back
  {
    name: 'משיכת פולי',
    category: 'back',
    defaultSets: '4 סטים',
    defaultReps: '10-12 חזרות',
    defaultRest: '90 שניות מנוחה',
  },
  {
    name: 'חתירה בישיבה',
    category: 'back',
    defaultSets: '4 סטים',
    defaultReps: '10-12 חזרות',
    defaultRest: '90 שניות מנוחה',
  },
  {
    name: 'דדליפט',
    category: 'back',
    defaultSets: '4 סטים',
    defaultReps: '6-8 חזרות',
    defaultRest: '120 שניות מנוחה',
    instructions: 'שמור על הגב ישר לאורך כל התנועה',
  },

  // Legs
  {
    name: 'סקוואט',
    category: 'legs',
    defaultSets: '4 סטים',
    defaultReps: '8-12 חזרות',
    defaultRest: '120 שניות מנוחה',
    instructions: 'להוריד עד שהירכיים מקבילות לרצפה',
  },
  {
    name: 'לחיצת רגליים',
    category: 'legs',
    defaultSets: '4 סטים',
    defaultReps: '10-15 חזרות',
    defaultRest: '90 שניות מנוחה',
  },
  {
    name: 'יישור רגליים',
    category: 'legs',
    defaultSets: '3 סטים',
    defaultReps: '12-15 חזרות',
    defaultRest: '60 שניות מנוחה',
  },

  // Shoulders
  {
    name: 'לחיצת כתפיים',
    category: 'shoulders',
    defaultSets: '4 סטים',
    defaultReps: '8-12 חזרות',
    defaultRest: '90 שניות מנוחה',
  },
  {
    name: 'הרמת צד',
    category: 'shoulders',
    defaultSets: '3 סטים',
    defaultReps: '12-15 חזרות',
    defaultRest: '60 שניות מנוחה',
  },

  // Arms
  {
    name: 'כפיפת מרפקים',
    category: 'arms',
    defaultSets: '3 סטים',
    defaultReps: '12-15 חזרות',
    defaultRest: '60 שניות מנוחה',
  },
  {
    name: 'יישור מרפקים',
    category: 'arms',
    defaultSets: '3 סטים',
    defaultReps: '12-15 חזרות',
    defaultRest: '60 שניות מנוחה',
  },

  // Abs
  {
    name: 'כפיפות בטן',
    category: 'abs',
    defaultSets: '3 סטים',
    defaultReps: '15-20 חזרות',
    defaultRest: '45 שניות מנוחה',
    instructions: 'שמור על הגב שטוח והראש מורם',
  },
  {
    name: 'פלאנק',
    category: 'abs',
    defaultSets: '3 סטים',
    defaultReps: '30-60 שניות',
    defaultRest: '45 שניות מנוחה',
  },

  // Cardio
  {
    name: 'הליכון',
    category: 'cardio',
    defaultSets: 'סט 1',
    defaultReps: '20-30 דקות',
    defaultRest: 'ללא מנוחה',
  },
  {
    name: 'רכיבה על אופניים',
    category: 'cardio',
    defaultSets: 'סט 1',
    defaultReps: '20-30 דקות',
    defaultRest: 'ללא מנוחה',
  },
];
