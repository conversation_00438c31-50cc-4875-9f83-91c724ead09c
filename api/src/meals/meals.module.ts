import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MealsService } from './meals.service';
import { MealsController } from './meals.controller';
import { MealEntity } from '../models/meals/meals.entity';
import { MealCategoryEntity } from '../models/meals/meals-categories.entity';
import { MealOptionEntity } from '../models/meals/meal-option.entity';
import { FoodItemEntity } from '../models/meals/food-items.entity';
import { UserEntity } from '../models/user-entity';
import { SecurityModule } from 'src/security/security.module';
import { FoodItemsService } from './food-items/food-items.service';
import { FoodItemsModule } from './food-items/food-items.module';
import { MealsCategoriesModule } from './meal-categories/meals-categories.module';
import { MealOptionModule } from './meal-option/meal-option.module';
import { MealTemplateModule } from './meal-template/meal-template.module';
import { MealTemplateEntity } from 'src/models/meals';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      MealEntity,
      MealCategoryEntity, // Ensure this is imported
      MealOptionEntity,
      FoodItemEntity,
      UserEntity,
      MealTemplateEntity,
    ]),
    SecurityModule,
    FoodItemsModule,
    MealsCategoriesModule,
    MealOptionModule,
    MealTemplateModule,
  ],
  providers: [MealsService, FoodItemsService],
  controllers: [MealsController],
  exports: [MealsService],
})
export class MealsModule {}
