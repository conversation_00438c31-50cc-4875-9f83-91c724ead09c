import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FoodItemsController } from './food-items.controller';
import { FoodItemsService } from './food-items.service';
import { FoodItemEntity } from '../../models/meals/food-items.entity';
import { JwtService } from '@nestjs/jwt';
import { AccessTokenEntity } from '../../models/user-entity';

@Module({
  imports: [TypeOrmModule.forFeature([FoodItemEntity, AccessTokenEntity])],
  controllers: [FoodItemsController],
  providers: [FoodItemsService, JwtService],
  exports: [FoodItemsService],
})
export class FoodItemsModule {}
