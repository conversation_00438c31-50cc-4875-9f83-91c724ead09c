import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsEnum, ValidateNested, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export class MacroNutrientsDto {
  @IsNumber()
  @Min(0)
  protein: number;

  @IsNumber()
  @Min(0)
  carbs: number;

  @IsNumber()
  @Min(0)
  fats: number;

  @IsNumber()
  @Min(0)
  calories: number;
}

export class CreateFoodItemDto {
  @IsString()
  name: string;

  @IsString()
  category: string;

  @IsNumber()
  @Min(0)
  defaultServing: number;

  @IsNumber()
  @IsOptional()
  @Min(0)
  minServing?: number;

  @IsNumber()
  @IsOptional()
  @Min(0)
  maxServing?: number;

  @ValidateNested()
  @Type(() => MacroNutrientsDto)
  macrosPer100g: MacroNutrientsDto;
}

export class UpdateFoodItemDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  category?: string;

  @IsNumber()
  @IsOptional()
  @Min(0)
  defaultServing?: number;

  @IsNumber()
  @IsOptional()
  @Min(0)
  minServing?: number;

  @IsNumber()
  @IsOptional()
  @Min(0)
  maxServing?: number;

  @ValidateNested()
  @IsOptional()
  @Type(() => MacroNutrientsDto)
  macrosPer100g?: MacroNutrientsDto;
}

export class CalculateMacrosDto {
  @IsString()
  foodId: string;

  @IsNumber()
  @Min(0)
  amount: number;
}