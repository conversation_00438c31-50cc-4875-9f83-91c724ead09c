import {
  IsString,
  IsO<PERSON>al,
  IsArray,
  ValidateNested,
  IsUUID,
  IsNotEmpty,
  IsEnum,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CreateMealCategoryDto } from '../meal-categories/dto/meals-categories.dto';
import { MacroNutrientsDto } from '../food-items/dto/food-items.dto';
import { BaseResponse } from 'src/utils/responses';

export enum MealTimeEnum {
  BREAKFAST = 'breakfast',
  LUNCH = 'lunch',
  DINNER = 'dinner',
  SNACK = 'snack',
  PRE_WORKOUT = 'pre_workout',
  POST_WORKOUT = 'post_workout',
}

export class MacroRangeDto extends BaseResponse {
  @ApiProperty({
    type: () => MacroNutrientsDto,
    description: 'Minimum macronutrient values',
  })
  @ValidateNested()
  @Type(() => MacroNutrientsDto)
  min: MacroNutrientsDto;

  @ApiProperty({
    type: () => MacroNutrientsDto,
    description: 'Maximum macronutrient values',
  })
  @ValidateNested()
  @Type(() => MacroNutrientsDto)
  max: MacroNutrientsDto;
}

export class CreateMealDto extends BaseResponse {
  @ApiProperty({
    description: 'Name of the meal',
    example: 'Grilled Chicken Salad',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({
    description: 'Optional description of the meal',
    example: 'A healthy salad with grilled chicken and mixed vegetables',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    enum: MealTimeEnum,
    description: 'The time of day this meal is intended for',
    example: MealTimeEnum.LUNCH,
  })
  @IsEnum(MealTimeEnum)
  @IsOptional()
  mealTime?: MealTimeEnum;

  @ApiProperty({
    description: 'ID of the trainee this meal is assigned to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  traineeId: string;

  @ApiPropertyOptional({
    description: 'Whether this meal is featured or highlighted',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  isFeatured?: boolean;

  @ApiPropertyOptional({
    type: () => MacroRangeDto,
    description: 'Macronutrient range for the meal',
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => MacroRangeDto)
  macroRange?: MacroRangeDto;

  @ApiPropertyOptional({
    type: () => [CreateMealCategoryDto],
    description: 'Categories the meal belongs to',
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateMealCategoryDto)
  categories?: CreateMealCategoryDto[];

  @ApiPropertyOptional({
    type: () => [String],
    description: 'IDs of the food items associated with this meal',
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  foodItemIds?: string[];
}

export class UpdateMealDto extends BaseResponse {
  @ApiPropertyOptional({
    description: 'Updated name of the meal',
    example: 'Spicy Grilled Chicken Salad',
  })
  @IsString()
  @IsOptional()
  @MinLength(3)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: 'Updated description of the meal',
    example: 'A healthy salad with spicy grilled chicken and mixed vegetables',
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    enum: MealTimeEnum,
    description: 'The time of day this meal is intended for',
    example: MealTimeEnum.DINNER,
  })
  @IsEnum(MealTimeEnum)
  @IsOptional()
  mealTime?: MealTimeEnum;

  @ApiPropertyOptional({
    description: 'ID of the trainee this meal is assigned to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  @IsNotEmpty()
  traineeId?: string;

  @ApiPropertyOptional({
    description: 'Whether this meal is featured or highlighted',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isFeatured?: boolean;

  @ApiPropertyOptional({
    type: () => MacroRangeDto,
    description: 'Updated macronutrient range',
  })
  @ValidateNested()
  @IsOptional()
  @Type(() => MacroRangeDto)
  macroRange?: MacroRangeDto;

  @ApiPropertyOptional({
    type: () => [CreateMealCategoryDto],
    description: 'Updated categories for the meal',
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateMealCategoryDto)
  categories?: CreateMealCategoryDto[];

  @ApiPropertyOptional({
    type: () => [String],
    description: 'Updated IDs of the food items associated with this meal',
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  foodItemIds?: string[];
}

export class MealResponseDto extends BaseResponse {
  @ApiProperty({ description: 'Unique identifier of the meal' })
  id: string;

  @ApiProperty({ description: 'Name of the meal' })
  name: string;

  @ApiProperty({ description: 'Description of the meal' })
  description: string;

  @ApiProperty({
    enum: MealTimeEnum,
    description: 'The time of day this meal is intended for',
  })
  mealTime: MealTimeEnum;

  @ApiProperty({ description: 'Whether this meal is featured or highlighted' })
  isFeatured: boolean;

  @ApiProperty({ description: 'ID of the trainer who created this meal' })
  createdById: string;

  @ApiProperty({ description: 'ID of the trainee this meal is assigned to' })
  traineeId: string;

  @ApiProperty({
    type: () => MacroRangeDto,
    description: 'Macronutrient range for the meal',
  })
  macroRange: MacroRangeDto;

  @ApiProperty({
    type: () => [CreateMealCategoryDto],
    description: 'Categories the meal belongs to',
  })
  categories: CreateMealCategoryDto[];

  @ApiProperty({ description: 'Date when the meal was created' })
  createdAt: Date;

  @ApiProperty({ description: 'Date when the meal was last updated' })
  updatedAt: Date;

  @ApiProperty({
    description: 'IDs of the food items associated with this meal',
  })
  foodItemIds: string[];
}
