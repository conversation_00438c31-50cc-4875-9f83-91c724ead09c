import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MealCategoryEntity } from '../../models/meals/meals-categories.entity';
import { CreateMealCategoryDto, UpdateMealCategoryDto } from './dto/meals-categories.dto';
import { MealOptionEntity } from '../../models/meals/meal-option.entity';

@Injectable()
export class MealCategoriesService {
  constructor(
    @InjectRepository(MealCategoryEntity)
    private readonly mealCategoryRepository: Repository<MealCategoryEntity>,
    @InjectRepository(MealOptionEntity)
    private readonly mealOptionRepository: Repository<MealOptionEntity>,
  ) {}

  async createMealCategory(
    createMealCategoryDto: CreateMealCategoryDto,
  ): Promise<MealCategoryEntity> {
    const { name, options } = createMealCategoryDto;

    const mealCategory = new MealCategoryEntity();
    mealCategory.name = name;
    mealCategory.options = await Promise.all(
      (options || []).map(async (option) => {
        const mealOption = new MealOptionEntity();
        mealOption.foodId = option.foodId;
        mealOption.amount = option.amount;
        return await this.mealOptionRepository.save(mealOption);
      }),
    );

    return await this.mealCategoryRepository.save(mealCategory);
  }

  async getAllMealCategories(): Promise<MealCategoryEntity[]> {
    return await this.mealCategoryRepository.find({ relations: ['options'] });
  }

  async getMealCategoryById(id: string): Promise<MealCategoryEntity> {
    const mealCategory = await this.mealCategoryRepository.findOne({
      where: { id },
      relations: ['options'],
    });

    if (!mealCategory) {
      throw new NotFoundException(`Meal category with ID ${id} not found`);
    }
    return mealCategory;
  }

  async updateMealCategory(
    id: string,
    updateMealCategoryDto: UpdateMealCategoryDto,
  ): Promise<MealCategoryEntity> {
    const mealCategory = await this.mealCategoryRepository.findOne({ where: { id } });

    if (!mealCategory) {
      throw new NotFoundException(`Meal category with ID ${id} not found`);
    }

    if (updateMealCategoryDto.name) {
      mealCategory.name = updateMealCategoryDto.name;
    }

    return await this.mealCategoryRepository.save(mealCategory);
  }

  async deleteMealCategory(id: string): Promise<void> {
    const result = await this.mealCategoryRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException(`Meal category with ID ${id} not found`);
    }
  }
}
