import { IsString, <PERSON>Array, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateMealOptionDto } from '../../meal-option/dto/meal-option.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMealCategoryDto {
  @ApiProperty({
    example: 'Breakfast',
    description: 'The name of the meal category',
  })
  @IsString()
  name: string;

  @ApiProperty({
    type: [CreateMealOptionDto],
    required: false,
    description: 'List of meal options for this category',
  })
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CreateMealOptionDto)
  options?: CreateMealOptionDto[];
}

export class UpdateMealCategoryDto {
  @ApiProperty({
    example: 'Updated Name',
    required: false,
    description: 'New name of the meal category',
  })
  @IsString()
  @IsOptional()
  name?: string;
}
