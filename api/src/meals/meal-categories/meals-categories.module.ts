import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MealsCategoriesController } from './meals-categories.controller';
import { MealCategoriesService } from './meal-categories.service';
import { MealCategoryEntity } from '../../models/meals/meals-categories.entity';
import { MealOptionEntity } from '../../models/meals/meal-option.entity';

@Module({
  imports: [TypeOrmModule.forFeature([MealCategoryEntity, MealOptionEntity])],
  controllers: [MealsCategoriesController],
  providers: [MealCategoriesService],
  exports: [MealCategoriesService, TypeOrmModule],
})
export class MealsCategoriesModule {}
