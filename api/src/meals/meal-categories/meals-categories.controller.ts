import { Controller, Post, Get, Body, Param, Delete, Patch } from '@nestjs/common';
import { MealCategoriesService } from './meal-categories.service';
import { CreateMealCategoryDto, UpdateMealCategoryDto } from './dto/meals-categories.dto';

@Controller('meals-categories')
export class MealsCategoriesController {
  constructor(private readonly mealCategoryService: MealCategoriesService) {}

  @Post()
  async createMealCategory(@Body() createMealCategoryDto: CreateMealCategoryDto) {
    return await this.mealCategoryService.createMealCategory(createMealCategoryDto);
  }

  @Get()
  async getAllMealCategories() {
    return await this.mealCategoryService.getAllMealCategories();
  }

  @Get(':id')
  async getMealCategoryById(@Param('id') id: string) {
    return await this.mealCategoryService.getMealCategoryById(id);
  }

  @Patch(':id')
  async updateMealCategory(
    @Param('id') id: string,
    @Body() updateMealCategoryDto: UpdateMealCategoryDto,
  ) {
    return await this.mealCategoryService.updateMealCategory(id, updateMealCategoryDto);
  }

  @Delete(':id')
  async deleteMealCategory(@Param('id') id: string) {
    return await this.mealCategoryService.deleteMealCategory(id);
  }
}
