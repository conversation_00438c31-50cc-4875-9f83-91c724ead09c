import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MealTemplateService } from './meal-template.service';
import { MealTemplateController } from './meal-template.controller';
import { MealTemplateEntity } from 'src/models/meals/meal-templates.entity';
import { UserEntity } from 'src/models/user-entity';
import { SecurityModule } from 'src/security/security.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MealTemplateEntity, UserEntity]),
    SecurityModule,
  ],
  controllers: [MealTemplateController],
  providers: [MealTemplateService],
  exports: [MealTemplateService],
})
export class MealTemplateModule {}
