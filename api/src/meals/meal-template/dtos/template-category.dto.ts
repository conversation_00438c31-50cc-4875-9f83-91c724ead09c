import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  IsOptional,
  ValidateNested,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';

export class TemplateOptionDto {
  @IsString()
  foodId: string;

  @IsNumber()
  @Min(0)
  amount: number;
}

export class TemplateCategoryDto {
  @IsString()
  name: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateOptionDto)
  options: TemplateOptionDto[];
}

export class MacroValuesDto {
  @IsNumber()
  @Min(0)
  protein: number;

  @IsNumber()
  @Min(0)
  carbs: number;

  @IsNumber()
  @Min(0)
  fats: number;

  @IsNumber()
  @Min(0)
  calories: number;
}

export class MacroRangeDto {
  @ValidateNested()
  @Type(() => MacroValuesDto)
  min: MacroValuesDto;

  @ValidateNested()
  @Type(() => MacroValuesDto)
  max: MacroValuesDto;
}

export class TemplateDataDto {
  @IsString()
  meal_name: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => MacroRangeDto)
  macroRange?: MacroRangeDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateCategoryDto)
  categories: TemplateCategoryDto[];
}
