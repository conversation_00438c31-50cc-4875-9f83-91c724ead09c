import {
  IsNotEmpty,
  <PERSON>UUID,
  IsOptional,
  IsArray,
  ValidateNested,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TemplateDataDto } from './template-category.dto';

class MealOptionDto {
  @IsUUID()
  foodId: string;

  @IsNotEmpty()
  amount: number;
}

class MealCategoryDto {
  @IsNotEmpty()
  name: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MealOptionDto)
  options: MealOptionDto[];
}

class MacroValue {
  protein: number;
  carbs: number;
  fats: number;
  calories: number;
}

class MacroRangeDto {
  min: MacroValue;
  max: MacroValue;
}

export class CreateMealTemplateDto {
  @IsString()
  @IsNotEmpty({ message: 'Please provide name of template' })
  name: string;

  @IsUUID()
  @IsNotEmpty({ message: 'Please provide trainerId' })
  trainerId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateDataDto)
  @IsNotEmpty({ message: 'Please provide template data' })
  templateData: TemplateDataDto[];
}

export class UpdateMealTemplateDto {
  @IsOptional()
  name?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TemplateDataDto)
  templateData?: TemplateDataDto[];
}
