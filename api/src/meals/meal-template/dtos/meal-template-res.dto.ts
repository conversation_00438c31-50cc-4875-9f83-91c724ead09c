import { MealTemplateEntity } from 'src/models/meals/meal-templates.entity';
import {
  IsString,
  IsUUID,
  IsDate,
  ValidateNested,
  IsOptional,
  IsArray,
} from 'class-validator';
import { Type } from 'class-transformer';
import { TemplateDataDto } from './template-category.dto';

export class MealTemplateResponseDto {
  @IsUUID()
  id: string;

  @IsString()
  name: string;

  @IsUUID()
  trainerId: string;

  @IsDate()
  createdAt: Date;

  @IsDate()
  updatedAt: Date;

  @ValidateNested({ each: true })
  @Type(() => TemplateDataDto)
  templateData: TemplateDataDto[];

  static transform(entity: MealTemplateEntity): MealTemplateResponseDto {
    const dto = new MealTemplateResponseDto();
    dto.id = entity.id;
    dto.name = entity.name;
    dto.trainerId = entity.trainerId;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;

    const raw = entity.templateData;

    dto.templateData = entity.templateData.map((item) => ({
      meal_name: item.meal_name,
      macroRange: item.macroRange,
      categories: item.categories?.map((category) => ({
        name: category.name,
        options: category.options.map((option) => ({
          foodId: option.foodId,
          amount: option.amount,
        })),
      })),
    }));

    return dto;
  }
}
