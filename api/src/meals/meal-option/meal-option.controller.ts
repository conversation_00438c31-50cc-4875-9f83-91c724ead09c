import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Patch, 
  Param, 
  Delete, 
  UseGuards 
} from '@nestjs/common';
import { MealOptionsService } from './meal-option.service';
import { MealOptionEntity } from '../../models/meals/meal-option.entity';
import { JwtAuthGuard } from '../../security/middleware/authGuard.middleware';
import { CreateMealOptionDto, UpdateMealOptionDto } from './dto/meal-option.dto';

@Controller('meal-options')
@UseGuards(JwtAuthGuard)
export class MealOptionsController {
  constructor(private readonly mealOptionsService: MealOptionsService) {}

  @Get('category/:categoryId')
  findByCategoryId(@Param('categoryId') categoryId: string): Promise<MealOptionEntity[]> {
    return this.mealOptionsService.findByCategoryId(categoryId);
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<MealOptionEntity> {
    return this.mealOptionsService.findOne(id);
  }

  @Post('category/:categoryId')
  create(
    @Param('categoryId') categoryId: string,
    @Body() dto: CreateMealOptionDto,
  ): Promise<MealOptionEntity> {
    return this.mealOptionsService.create(categoryId, dto);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() dto: UpdateMealOptionDto,
  ): Promise<MealOptionEntity> {
    return this.mealOptionsService.update(id, dto);
  }

  @Delete(':id')
  remove(@Param('id') id: string): Promise<void> {
    return this.mealOptionsService.remove(id);
  }
}
