import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MealOptionsController } from './meal-option.controller';
import { MealOptionsService } from './meal-option.service';
import { MealOptionEntity } from '../../models/meals/meal-option.entity';
import { JwtService } from '@nestjs/jwt';
import { AccessTokenEntity } from '../../models/user-entity/accessToken.entity';

@Module({
  imports: [TypeOrmModule.forFeature([MealOptionEntity, AccessTokenEntity])],
  controllers: [MealOptionsController],
  providers: [MealOptionsService, JwtService],
})
export class MealOptionModule {}
