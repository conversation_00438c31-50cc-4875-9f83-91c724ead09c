import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  NotFoundException,
  Put,
} from '@nestjs/common';
import { MealsService } from './meals.service';
import { JwtAuthGuard } from '../security/middleware/authGuard.middleware';
import { CreateMealDto, UpdateMealDto } from './dto/meal.dto';
import { CreateMealFromTemplateDTO } from './dto/CreateMealFromTemplate.dto';
import { Roles } from 'src/security/middleware/roles.decorator';
import { ROLE_VALUES } from 'src/models/user-entity';

@Controller('meals')
@UseGuards(JwtAuthGuard)
export class MealsController {
  constructor(private readonly mealsService: MealsService) {}

  @Get('trainee/:traineeId')
  findByTrainee(@Param('traineeId') traineeId: string) {
    if (!traineeId) {
      throw new NotFoundException('Trainee ID is required');
    }
    return this.mealsService.findByTrainee(traineeId);
  }

  @Get()
  findAll(@Request() req) {
    return this.mealsService.findAll(req.user.id);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.mealsService.findOne(id);
  }

  @Post()
  create(@Body() mealDto: CreateMealDto, @Request() req) {
    return this.mealsService.create(mealDto, req.user);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() mealDto: UpdateMealDto) {
    return this.mealsService.update(id, mealDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.mealsService.remove(id);
  }

  @Get(':id/macros')
  calculateMealMacros(@Param('id') id: string) {
    return this.mealsService.calculateMealMacros(id);
  }

  @Post(':templateId')
  async createMealsFromTemplate(
    @Request() req,
    @Param('templateId') templateId: string,
    @Body() CreateMealData: CreateMealFromTemplateDTO,
  ) {
    const trainerId = req.user.id;
    const { traineeId } = CreateMealData;

    return this.mealsService.createMealsFromTemplate(
      templateId,
      traineeId,
      trainerId,
    );
  }

  @Roles(ROLE_VALUES.TRAINER)
  @Put('/macros/:traineeId')
  updateTraineeMacrosVisibility(
    @Param('traineeId') traineeId: string,
    @Request() req,
  ) {
    return this.mealsService.updateTraineeMacrosVisibility(traineeId);
  }
}
