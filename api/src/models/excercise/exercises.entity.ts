import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

@Entity('exercises')
export class ExerciseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: false })
  category: string;

  @Column({ nullable: true })
  defaultSets: string;

  @Column({ nullable: true })
  defaultReps: string;

  @Column({ nullable: true })
  defaultRest: string;

  @Column({ nullable: true, type: 'text' })
  instructions: string;
}
