import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyT<PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { TrainingPlanEntity } from './training-plans.entity';
import { ExerciseEntity } from './exercises.entity';

@Entity('training_exercises')
export class TrainingExerciseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(
    () => TrainingPlanEntity,
    (trainingPlan) => trainingPlan.exercises,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'training_plan_id' })
  trainingPlan: TrainingPlanEntity;

  @ManyToOne(() => ExerciseEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'exercise_id' })
  exercise: ExerciseEntity;

  @Column({ nullable: true })
  sets: string;

  @Column({ nullable: true })
  videoUrl: string;

  @Column({ nullable: true })
  reps: string;

  @Column({ nullable: true })
  rest: string;

  @Column({ nullable: true, type: 'text' })
  instructions: string;

  @Column({ nullable: true })
  name: string;
}
