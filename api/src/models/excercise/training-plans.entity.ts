import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { UserEntity } from '../user-entity/user.entity';
import { TrainingExerciseEntity } from './training-exercises.entity';

@Entity('training_plans')
export class TrainingPlanEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  day: string;

  @Column({ nullable: false })
  focus: string;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity; // trainee

  @OneToMany(
    () => TrainingExerciseEntity,
    (trainingExercise) => trainingExercise.trainingPlan,
  )
  exercises: TrainingExerciseEntity[];
}
