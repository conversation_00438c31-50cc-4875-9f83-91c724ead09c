import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '../user-entity/user.entity';

export interface TrainingExerciseTemplateInterface {
  day: string;
  focus: string;
  exercises: {
    exerciseId: string;
    name: string;
    sets: string;
    reps: string;
    rest: string;
    instructions?: string; // optional field
  }[];
}

@Entity('training_plan_templates')
export class TrainingPlanTemplateEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column()
  trainerId: string;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'trainerId' })
  trainer: UserEntity;

  @Column({ type: 'jsonb', nullable: false })
  templateData: TrainingExerciseTemplateInterface[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
