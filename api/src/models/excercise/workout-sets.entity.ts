import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { WorkoutExerciseEntity } from './workout-exercise.entity';

@Entity('workout_sets')
export class WorkoutSetEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => WorkoutExerciseEntity, (exercise) => exercise.sets, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'workout_exercise_id' })
  workoutExercise: WorkoutExerciseEntity;

  @Column({ type: 'int', nullable: false })
  setNumber: number;

  @Column({ type: 'int', nullable: true })
  weight: number;

  @Column({ type: 'int', nullable: true })
  reps: number;

  @Column({ type: 'text', nullable: true })
  notes: string;
}
