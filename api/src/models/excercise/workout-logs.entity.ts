import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { UserEntity } from '../user-entity/user.entity';
import { TrainingPlanEntity } from './training-plans.entity';
import { WorkoutExerciseEntity } from './workout-exercise.entity';

@Entity('workout_logs')
export class WorkoutLogEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => TrainingPlanEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'training_plan_id' })
  trainingPlan: TrainingPlanEntity;

  @Column({ type: 'date', nullable: false })
  date: string;

  @Column({ nullable: true })
  duration: string;

  @Column({ nullable: true, type: 'text' })
  notes: string;

  @OneToMany(() => WorkoutExerciseEntity, (exercise) => exercise.workoutLog, {
    cascade: true,
  })
  exercises: WorkoutExerciseEntity[];
}
