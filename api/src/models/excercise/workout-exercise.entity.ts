import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { WorkoutLogEntity } from './workout-logs.entity';
import { WorkoutSetEntity } from './workout-sets.entity';
import { ExerciseEntity } from './exercises.entity';

@Entity('workout_exercises')
export class WorkoutExerciseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => WorkoutLogEntity, (workoutLog) => workoutLog.exercises, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'workout_log_id' })
  workoutLog: WorkoutLogEntity;

  @ManyToOne(() => ExerciseEntity, { eager: true })
  @JoinColumn({ name: 'exercise_id' })
  exercise: ExerciseEntity;

  @Column({ nullable: false })
  name: string;

  @OneToMany(() => WorkoutSetEntity, (set) => set.workoutExercise, {
    cascade: true,
  })
  sets: WorkoutSetEntity[];
}
