import {
    Column,
    <PERSON>tity,
    PrimaryGeneratedColumn,
    OneTo<PERSON><PERSON>,
    Join<PERSON><PERSON>umn
  } from 'typeorm';
  import { UserEntity } from '../user-entity/user.entity';
  
  export enum TrainerSpecialization {
    WEIGHT_LOSS = 'WEIGHT_LOSS',
    MUSCLE_GAIN = 'MUSCLE_GAIN',
    STRENGTH_TRAINING = 'STRENGTH_TRAINING',
    CARDIO = 'CARDIO',
    YOGA = 'YOGA'
  }
  
  @Entity('trainer_profiles')
  export class TrainerProfileEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;
  
    @OneToOne(() => UserEntity, (user) => user.trainerProfile, { onDelete: 'CASCADE' })
    @JoinColumn()
    user: UserEntity;
  
    @Column({
      type: 'simple-array',
      nullable: true
    })
    specializations: TrainerSpecialization[];
  
    @Column({ nullable: true })
    yearsOfExperience: number;
  
    @Column({ nullable: true })
    certifications: string;
  
    @Column('text', { nullable: true })
    bio: string;
  
    @Column({ nullable: true })
    hourlyRate: number;
  
    @Column({ nullable: true })
    profileImageUrl: string;

    @Column({ 
      nullable: true, 
      default: 'junior_trainer',
      enum: ['junior_trainer', 'nutrition_specialist', 'senior_trainer'] 
    })
    trainerRole: string;
  }
  