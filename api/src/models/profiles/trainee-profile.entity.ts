import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { UserEntity } from '../user-entity/user.entity';

export enum FitnessGoal {
  FAT_LOSS = 'fat_loss',
  MUSCLE_GAIN = 'muscle_gain',
  ENDURANCE = 'endurance',
  FLEXIBILITY = 'flexibilty',
}

export enum ActivityLevel {
  SEDENTARY = 'sendentary',
  LIGHTLY_ACTIVE = 'light_active',
  MODERATELY_ACTIVE = 'moderately_active',
  VERY_ACTIVE = 'very_active',
  EXTRA_ACTIVE = 'extra_active',
}

@Entity('trainee_profiles')
export class TraineeProfileEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @OneToOne(() => UserEntity, (user) => user.traineeProfile, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  user: UserEntity;

  @Column('int')
  age: number;

  @Column('enum', {
    enum: ['male', 'female', 'other'],
    default: 'male',
  })
  gender: string;

  @Column('float')
  height: number;

  @Column('float')
  weight: number;

  @Column('float', { nullable: true })
  bodyFatPercentage: number;

  @Column('enum', {
    enum: FitnessGoal,
    default: FitnessGoal.FAT_LOSS,
  })
  fitnessGoal: FitnessGoal;

  @Column('enum', {
    enum: ActivityLevel,
    default: ActivityLevel.MODERATELY_ACTIVE,
  })
  activityLevel: ActivityLevel;

  @Column({ nullable: true })
  profileImageUrl: string;
}
