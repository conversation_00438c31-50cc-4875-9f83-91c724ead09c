import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { MealEntity } from './meals.entity';
import { MealOptionEntity } from './meal-option.entity';

export type MealOption = {
  foodId: string;
  amount: number;
};

export type MealCategory = {
  name: string;
  options: MealOption[];
};

@Entity('meal_categories')
export class MealCategoryEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string; // 'Protein', 'Carb', etc.

  @ManyToOne(() => MealEntity, (meal) => meal.categories, {
    onDelete: 'CASCADE',
  })
  meal: MealEntity;

  @OneToMany(() => MealOptionEntity, (option) => option.category, {
    cascade: true,
    eager: true,
  })
  options: MealOptionEntity[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
