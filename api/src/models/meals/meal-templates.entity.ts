import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { UserEntity } from '../user-entity';
import { MacroRange } from 'src/utils/interfaces';

export interface TemplateMealDataInterface {
  meal_name: string;
  macroRange?: MacroRange;
  categories: {
    name: string;
    options: {
      foodId: string;
      amount: number;
    }[];
  }[];
}

@Entity('meal_templates')
export class MealTemplateEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  trainerId: string;

  @ManyToOne(() => UserEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'trainerId' })
  trainer: UserEntity;

  @Column('jsonb')
  templateData: TemplateMealDataInterface[]; // contains all meals data

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
