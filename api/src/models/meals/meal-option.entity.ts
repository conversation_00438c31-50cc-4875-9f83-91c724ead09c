import {
  Column,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { MealCategoryEntity } from './meals-categories.entity';
import { FoodItemEntity } from './food-items.entity';

@Entity('meal_options')
export class MealOptionEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid')
  foodId: string;

  @Column('int')
  amount: number;

  @ManyToOne(() => MealCategoryEntity, (category) => category.options, {
    onDelete: 'CASCADE',
  })
  category: MealCategoryEntity;

  @ManyToOne(() => FoodItemEntity, { eager: true })
  @JoinColumn({ name: 'foodId' })
  food: FoodItemEntity;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
