import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OtpController } from './otp.controller';
import { OtpService } from './otp.service';
import { OTPEntity } from 'src/models/user-entity/otp.entity';
import { SesService } from 'src/third-party/aws/SES/ses.service';
import { UserEntity } from 'src/models/user-entity';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';

@Module({
  imports: [TypeOrmModule.forFeature([OTPEntity, UserEntity])],
  controllers: [OtpController],
  providers: [OtpService, SesService, FirebaseService],
})
export class OtpModule {}
