import { Modu<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { FirebaseService } from '../third-party/firebase/firebase-authentication.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../models/user-entity/user.entity';
import { AccessTokenEntity } from '../models/user-entity/accessToken.entity';
import { RoleEntity } from '../models/user-entity/role.entity';
import { AccessTokenModule } from './access-token/access-token.module';
import { JwtStrategy } from '../security/middleware/jwt.strategy';
import { OtpModule } from './otp/otp.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserEntity, RoleEntity, AccessTokenEntity]),
    AccessTokenModule,
    OtpModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, FirebaseService, JwtStrategy],
  exports: [AuthService, JwtStrategy],
})
export class AuthModule {}
