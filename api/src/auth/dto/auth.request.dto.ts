import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty } from 'class-validator';
import { MatchRegex } from 'src/common/validators';
import { BaseResponse } from 'src/utils/responses';

export class RegisterDto extends BaseResponse {
  email: string;
  password: string;
  role: string;
  name: string;
  mobileNumber?: string;
}

export class LoginDto extends BaseResponse {
  @IsEmail({}, { message: 'Please provide a valid email' })
  @IsNotEmpty()
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email of the user Account',
  })
  email: string;

  @IsNotEmpty()
  @MatchRegex('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})')
  @ApiProperty({
    example: 'Test@123',
    description: 'Password of the user account',
  })
  password: string;
}

export class ForgotPasswordDto {
  @IsEmail()
  @IsNotEmpty()
  email: string;
}
