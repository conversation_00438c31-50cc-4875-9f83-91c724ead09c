export function generateRandomPassword(length = 10): string {
  const chars =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
  let password = '';

  // Ensure we have at least one uppercase, one lowercase, one number, and one special character
  password += chars[Math.floor(Math.random() * 26)];
  password += chars[Math.floor(Math.random() * 26) + 26];
  password += chars[Math.floor(Math.random() * 10) + 52];
  password += chars[Math.floor(Math.random() * 10) + 62];

  // Fill the rest of the password
  for (let i = 4; i < length; i++) {
    password += chars[Math.floor(Math.random() * chars.length)];
  }

  // Shuffle the password
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
}
