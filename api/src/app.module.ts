import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SecurityModule } from './security/security.module';
import config from 'src/configuration/properties';
import { BootstrapService } from './bootstrap.service';
import { CommonModule } from './common/common.module';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import {
  PermissionEntity,
  RoleEntity,
  UserEntity,
  AccessTokenEntity,
} from './models/user-entity';
import { ThirdPartyModule } from './third-party/third-party.module';
import { MulterModule } from '@nestjs/platform-express';
import { AuthModule } from './auth/auth.module';
import { MealsModule } from './meals/meals.module';
import { ExercisesModule } from './exercises/exercises.module';
import { TraineeModule } from './trainee/trainee.module';
import { AdminModule } from './admin/admin.module';
import { NetworkService } from './network.service';
import { TwilioModule } from './twilio/twilio.module';
import { FoodItemEntity } from './models/meals/food-items.entity';
import { ExerciseEntity } from './models/excercise/exercises.entity';
import { APIUrlLoggerMiddleware } from './security/middleware/ApiUrlLogger.middleware';

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRoot(config[process.env.NODE_ENV]()['ormConfig']),
    TypeOrmModule.forFeature([
      UserEntity,
      PermissionEntity,
      RoleEntity,
      RoleEntity,
      AccessTokenEntity,
      FoodItemEntity,
      ExerciseEntity,
    ]),
    MulterModule.register({}),

    CommonModule,
    ThirdPartyModule,
    SecurityModule,
    AuthModule,

    AdminModule,
    TraineeModule,
    MealsModule,
    ExercisesModule,

    TwilioModule,
  ],
  controllers: [],
  providers: [BootstrapService, NetworkService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(APIUrlLoggerMiddleware).forRoutes('*');
  }
}
