### PUBLIC ENDPOINTS (No Authentication Required)

### Test the health endpoint
GET http://localhost:5000/public-agents/health

### Test the public chat endpoint
POST http://localhost:5000/public-agents/test-chat
Content-Type: application/json

{
  "message": "I missed my second meal today"
}

### Test the public test-message endpoint
POST http://localhost:5000/public-agents/test-message
Content-Type: application/json

{
  "message": "Hello, I'm testing the API"
}

### Test the public classify endpoint
POST http://localhost:5000/public-agents/classify
Content-Type: application/json

{
  "message": "I'm going to a restaurant tomorrow night"
}

### Test the public nutrition agent endpoint
POST http://localhost:5000/public-agents/nutrition
Content-Type: application/json

{
  "message": "What should I eat before my workout?"
}

### Test the public social agent endpoint
POST http://localhost:5000/public-agents/social
Content-Type: application/json

{
  "message": "I'm going to a birthday party this weekend"
}

### Test the public general agent endpoint
POST http://localhost:5000/public-agents/general
Content-Type: application/json

{
  "message": "How are you doing today?"
}

### AUTHENTICATED ENDPOINTS (JWT Token Required)

### Test the chat endpoint
POST http://localhost:5000/agents/chat
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "message": "I missed my second meal today"
}

### Test the conversation history endpoint
GET http://localhost:5000/agents/conversation
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test the progress endpoint
GET http://localhost:5000/agents/progress
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test the weekly summary endpoint
POST http://localhost:5000/agents/weekly-summary
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{}

### Test the health endpoint
GET http://localhost:5000/agents/health
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test the test-message endpoint
POST http://localhost:5000/agents/test-message
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "message": "Hello, I'm testing the API"
}

### Test the classify endpoint
POST http://localhost:5000/agents/classify
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "message": "I'm going to a restaurant tomorrow night"
}

### Test the nutrition agent endpoint
POST http://localhost:5000/agents/agents/nutrition
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "message": "What should I eat before my workout?"
}

### Test the social agent endpoint
POST http://localhost:5000/agents/agents/social
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "message": "I'm going to a birthday party this weekend"
}

### Test the general agent endpoint
POST http://localhost:5000/agents/agents/general
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "message": "How are you doing today?"
}

### Test the summarize endpoint
POST http://localhost:5000/agents/memory/summarize
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{}

### Test the schedule checkup endpoint
POST http://localhost:5000/agents/checkups/schedule
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "minutes_from_now": 60,
  "message": "זוכר לאכול את הארוחה הבאה שלך?"
}

### Test the get checkups endpoint
GET http://localhost:5000/agents/checkups
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test the get user state endpoint
GET http://localhost:5000/agents/user/state
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test the get user profile endpoint
GET http://localhost:5000/agents/user/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test the get audit log endpoint
GET http://localhost:5000/agents/audit-log
Authorization: Bearer YOUR_JWT_TOKEN_HERE

### Test the quick test endpoint
GET http://localhost:5000/agents/test
Authorization: Bearer YOUR_JWT_TOKEN_HERE
