import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AccessTokenEntity } from '../models/user-entity/accessToken.entity';
import { TraineeProfileEntity } from '../models/profiles/trainee-profile.entity';
import { SecurityModule } from '../security/security.module';
import { AgentsController } from './agents.controller';
import { AgentsPublicController } from './agents.public.controller';
import { AgentsService } from './agents.service';

@Module({
  imports: [
    HttpModule,
    ConfigModule,
    JwtModule,
    TypeOrmModule.forFeature([AccessTokenEntity, TraineeProfileEntity]),
    SecurityModule
  ],
  controllers: [Agents<PERSON><PERSON>roll<PERSON>, AgentsPublicController],
  providers: [AgentsService],
  exports: [AgentsService]
})
export class AgentsModule {}
