import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class ChatMessageDto {
  @ApiProperty({
    description: 'The message to send to the AI assistant',
    example: 'I missed my second meal today',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'The channel (web or whatsapp)',
    example: 'web',
    required: false,
  })
  @IsString()
  @IsOptional()
  channel?: string;
}

export class ChatResponseDto {
  @ApiProperty({
    description: 'The response from the AI assistant',
    example: 'I understand you missed your second meal today. It\'s important to try to stay consistent with your meal plan, but I know things happen sometimes.',
  })
  response: string;

  @ApiProperty({
    description: 'The domain of the response (nutrition, social_event, general)',
    example: 'nutrition',
  })
  domain: string;
}

export class ConversationMessageDto {
  @ApiProperty({
    description: 'The role of the message sender (user or assistant)',
    example: 'user',
  })
  role: string;

  @ApiProperty({
    description: 'The content of the message',
    example: 'I missed my second meal today',
  })
  content: string;

  @ApiProperty({
    description: 'The timestamp of the message',
    example: 1684567890.123,
  })
  timestamp: number;

  @ApiProperty({
    description: 'The domain of the message (nutrition, social_event, general)',
    example: 'nutrition',
  })
  @IsOptional()
  domain?: string;
}

export class ConversationHistoryDto {
  @ApiProperty({
    description: 'The conversation messages',
    type: [ConversationMessageDto],
  })
  messages: ConversationMessageDto[];
}

export class ProgressDataDto {
  @ApiProperty({
    description: 'The number of missed meals',
    example: 1,
  })
  missed_meals_count: number;

  @ApiProperty({
    description: 'The number of unplanned foods',
    example: 0,
  })
  unplanned_food_count: number;

  @ApiProperty({
    description: 'The number of social events',
    example: 0,
  })
  social_events_count: number;

  @ApiProperty({
    description: 'Personalized tips for the user',
    example: ['Consider setting an alarm for Meal4'],
    type: [String],
  })
  personalized_tips: string[];
}

export class WeeklySummaryDto {
  @ApiProperty({
    description: 'The weekly summary text',
    example: 'דוח התקדמות שבועי שלך אח יקר:\n• פספסת 1 ארוחות השבוע\n• היו 0 אכילות לא מתוכננות השבוע\n• התקיימו 0 אירועים חברתיים השבוע',
  })
  summary: string;
}

export class CheckupRequestDto {
  @ApiProperty({
    description: 'Minutes from now to schedule the checkup',
    example: 60,
  })
  @IsNotEmpty()
  minutes_from_now: number;

  @ApiProperty({
    description: 'The checkup message',
    example: 'זוכר לאכול את הארוחה הבאה שלך?',
  })
  @IsString()
  @IsNotEmpty()
  message: string;
}

export class ClassifyMessageDto {
  @ApiProperty({
    description: 'The message to classify',
    example: 'I missed my second meal today',
  })
  @IsString()
  @IsNotEmpty()
  message: string;
}

export class HealthCheckResponseDto {
  @ApiProperty({
    description: 'The health status of the API',
    example: 'healthy',
  })
  status: string;

  @ApiProperty({
    description: 'Whether the orchestrator is initialized',
    example: true,
  })
  orchestrator_initialized: boolean;

  @ApiProperty({
    description: 'The API version',
    example: '1.0.0',
  })
  version: string;
}

export class TestMessageResponseDto {
  @ApiProperty({
    description: 'The status of the test',
    example: 'success',
  })
  status: string;

  @ApiProperty({
    description: 'The message that was sent',
    example: 'Hello, I\'m testing the API',
  })
  message: string;

  @ApiProperty({
    description: 'The response from the AI assistant',
    example: 'Hello! I\'m here to help with your fitness and nutrition goals. How can I assist you today?',
  })
  response: string;
}
