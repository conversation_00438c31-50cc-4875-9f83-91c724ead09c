import { Controller, Post, Get, Body, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import {
  ChatMessageDto,
  HealthCheckResponseDto,
  TestMessageResponseDto,
  ClassifyMessageDto
} from './dto/agents.dto';

@ApiTags('public-agents')
@Controller('public-agents')
export class AgentsPublicController {
  constructor(private readonly agentsService: AgentsService) {}

  @Get('health')
  @ApiOperation({ summary: 'Check if the agents service is healthy' })
  @ApiResponse({ status: 200, description: 'Service is healthy', type: HealthCheckResponseDto })
  async healthCheck() {
    try {
      // Call the Flask API health check
      const flaskHealth = await this.agentsService.checkHealth();
      return {
        status: flaskHealth.status,
        message: 'Agents service is healthy',
        flask_api: flaskHealth
      };
    } catch (error) {
      return {
        status: 'error',
        message: 'Error connecting to Flask API',
        error: error.message
      };
    }
  }

  @Post('test-chat')
  @ApiOperation({ summary: 'Test the chat functionality without authentication' })
  @ApiResponse({ status: 200, description: 'Test message processed successfully' })
  async testChat(@Body() body: ChatMessageDto) {
    // Use a default user ID for testing
    const testUserId = 'test-user-123';
    return this.agentsService.sendChatMessage(testUserId, body.message, body.channel);
  }

  @Post('test-message')
  @ApiOperation({ summary: 'Test the AI service with a simple message' })
  @ApiResponse({ status: 200, description: 'Test successful', type: TestMessageResponseDto })
  async testMessage(@Body('message') message: string) {
    return this.agentsService.testMessage(message);
  }

  @Post('classify')
  @ApiOperation({ summary: 'Classify a message without authentication' })
  @ApiResponse({ status: 200, description: 'Classification successful' })
  async classifyMessage(@Body() dto: ClassifyMessageDto) {
    // Use a default user ID for testing
    const testUserId = 'test-user-123';
    return this.agentsService.classifyMessage(testUserId, dto.message);
  }

  @Post('nutrition')
  @ApiOperation({ summary: 'Send a message to the nutrition agent without authentication' })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  async sendToNutritionAgent(@Body() dto: ChatMessageDto) {
    // Use a default user ID for testing
    const testUserId = 'test-user-123';
    return this.agentsService.sendToNutritionAgent(testUserId, dto.message, dto.channel);
  }

  @Post('social')
  @ApiOperation({ summary: 'Send a message to the social event agent without authentication' })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  async sendToSocialAgent(@Body() dto: ChatMessageDto) {
    // Use a default user ID for testing
    const testUserId = 'test-user-123';
    return this.agentsService.sendToSocialAgent(testUserId, dto.message, dto.channel);
  }

  @Post('general')
  @ApiOperation({ summary: 'Send a message to the general agent without authentication' })
  @ApiResponse({ status: 200, description: 'Message sent successfully' })
  async sendToGeneralAgent(@Body() dto: ChatMessageDto) {
    // Use a default user ID for testing
    const testUserId = 'test-user-123';
    return this.agentsService.sendToGeneralAgent(testUserId, dto.message, dto.channel);
  }
}
