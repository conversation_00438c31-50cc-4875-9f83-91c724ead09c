# Testing the Agents Integration

This document explains how to test the integration between the Nest.js backend and the Flask API.

## Prerequisites

1. Make sure both servers are running:
   - Flask API: `cd agents && python src/main.py`
   - Nest.js backend: `cd api && npm run start:dev`

2. Get a valid JWT token by logging in to the application.

## Public Endpoints (No Authentication Required)

For initial testing, you can use these endpoints that don't require authentication:

```
GET http://localhost:5000/public-agents/health
```

Expected response:
```json
{
  "status": "ok",
  "message": "Agents service is healthy"
}
```

```
POST http://localhost:5000/public-agents/test-chat
Content-Type: application/json

{
  "message": "I missed my second meal today"
}
```

Expected response:
```json
{
  "response": "I understand you missed your second meal today...",
  "domain": "nutrition"
}
```

## Testing with Postman

### 1. Send a message to the AI assistant

```
POST http://localhost:5000/agents/chat
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{
  "message": "I missed my second meal today"
}
```

Expected response:
```json
{
  "response": "I understand you missed your second meal today. It's important to try to stay consistent with your meal plan, but I know things happen sometimes. Since you missed the meal, try to make up for some of those nutrients in your next meal. Would you like me to suggest an alternative quick meal you could have now?",
  "domain": "nutrition"
}
```

### 2. Get conversation history

```
GET http://localhost:5000/agents/conversation
Authorization: Bearer YOUR_JWT_TOKEN_HERE
```

Expected response:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "I missed my second meal today",
      "timestamp": 1684567890.123,
      "domain": "unknown"
    },
    {
      "role": "assistant",
      "content": "I understand you missed your second meal today...",
      "timestamp": 1684567895.456,
      "domain": "nutrition"
    }
  ]
}
```

### 3. Get progress data

```
GET http://localhost:5000/agents/progress
Authorization: Bearer YOUR_JWT_TOKEN_HERE
```

Expected response:
```json
{
  "missed_meals_count": 1,
  "unplanned_food_count": 0,
  "social_events_count": 0,
  "personalized_tips": []
}
```

### 4. Generate a weekly summary

```
POST http://localhost:5000/agents/weekly-summary
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN_HERE

{}
```

Expected response:
```json
{
  "summary": "דוח התקדמות שבועי שלך אח יקר:\n• פספסת 1 ארוחות השבוע\n• היו 0 אכילות לא מתוכננות השבוע\n• התקיימו 0 אירועים חברתיים השבוע\n\nיאללה, תמשיך ככה גבר!"
}
```

## Troubleshooting

### 1. 401 Unauthorized

If you get a 401 Unauthorized error, make sure:
- You're including a valid JWT token in the Authorization header
- The token hasn't expired
- The token is in the correct format: `Bearer YOUR_JWT_TOKEN_HERE`

### 2. 422 Unprocessable Entity

If you get a 422 Unprocessable Entity error, make sure:
- You're sending the correct request body format
- For the chat endpoint, include a `message` field in the request body

### 3. 500 Internal Server Error

If you get a 500 Internal Server Error, check:
- Both servers are running
- The Flask API URL is correctly configured in the `.env` file
- The logs in both the Nest.js and Flask servers for more details

### 4. Connection Refused

If you get a Connection Refused error, make sure:
- The Flask API is running on the expected port (default: 8000)
- The FLASK_API_URL environment variable is set correctly in the `.env` file
