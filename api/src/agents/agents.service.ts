import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { catchError, firstValueFrom, of } from 'rxjs';
import { AxiosError } from 'axios';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TraineeProfileEntity } from '../models/profiles/trainee-profile.entity';

// Define interfaces for request/response types
export interface ClassificationResponse {
  domain: string;
}

export interface AgentResponse {
  response: string;
  domain: string;
}

export interface SummaryResponse {
  summary: string;
}

export interface CheckupResponse {
  checkup_id: string;
  scheduled_time: number;
}

export interface CheckupsListResponse {
  checkups: any[];
}

export interface ProgressData {
  missed_meals_count: number;
  unplanned_food_count: number;
  social_events_count: number;
  personalized_tips: string[];
}

@Injectable()
export class AgentsService {
  private readonly flaskApiUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(TraineeProfileEntity)
    private readonly traineeProfileRepository: Repository<TraineeProfileEntity>,
  ) {
    // Get the Flask API URL from environment variables or use a default
    this.flaskApiUrl = this.configService.get<string>('FLASK_API_URL') || 'http://localhost:8000';
  }

  /**
   * Get a user's trainee profile from the database
   * @param userId The user ID
   * @returns The trainee profile or null if not found
   */
  async getUserTraineeProfile(userId: string): Promise<any> {
    try {
      console.log(`Getting trainee profile for user ID: ${userId}`);

      // Find trainee profile by user ID
      const traineeProfile = await this.traineeProfileRepository.findOne({
        where: { user: { id: userId } }
      });

      if (traineeProfile) {
        console.log(`Found trainee profile for user ID: ${userId}`);

        // Return trainee profile information
        return {
          id: traineeProfile.id,
          age: traineeProfile.age,
          gender: traineeProfile.gender,
          height: traineeProfile.height,
          weight: traineeProfile.weight,
          bodyFatPercentage: traineeProfile.bodyFatPercentage,
          fitnessGoal: traineeProfile.fitnessGoal,
          activityLevel: traineeProfile.activityLevel
        };
      } else {
        console.log(`No trainee profile found for user ID: ${userId}`);
        return null;
      }
    } catch (error) {
      console.error(`Error getting trainee profile: ${error.message}`);
      // Return null instead of throwing an error to allow the process to continue
      return null;
    }
  }

  /**
   * Get user data from various sources to provide to the AI service
   * @param userId The user ID
   * @returns User data including profile, meal plan, etc.
   */
  async getUserData(userId: string): Promise<any> {
    try {
      // Get trainee profile
      const traineeProfile = await this.getUserTraineeProfile(userId);

      // Get user data
      const userData = {
        user: { id: userId },
        traineeProfile: traineeProfile || {}
      };

      return userData;
    } catch (error) {
      console.error(`Error getting user data: ${error.message}`);
      return {
        user: { id: userId }
      };
    }
  }

  /**
   * Send a message to the chat API
   * @param userId The user ID
   * @param message The message to send
   * @param channel The channel (web or whatsapp). If not provided, defaults to web.
   * @returns The response from the chat API
   */
  async sendChatMessage(userId: string, message: string, channel: string = 'web') {
    try {
      // First check if the Flask API is healthy
      try {
        const healthCheck = await firstValueFrom(
          this.httpService.get(`${this.flaskApiUrl}/health`).pipe(
            catchError((error: AxiosError) => {
              console.error('Health check failed:', error.message);
              throw new HttpException(
                'AI service is not available. Health check failed.',
                HttpStatus.SERVICE_UNAVAILABLE,
              );
            }),
          ),
        );

        console.log('Health check response:', healthCheck.data);

        if (healthCheck.data.status !== 'healthy') {
          throw new HttpException(
            'AI service is not healthy: ' + JSON.stringify(healthCheck.data),
            HttpStatus.SERVICE_UNAVAILABLE,
          );
        }
      } catch (healthError) {
        console.error('Health check error:', healthError);
        throw healthError;
      }

      // Try to get user data from the database
      try {
        // Get user data from the database
        const userData = await this.getUserData(userId);

        // If we have user data, update Redis with it
        if (userData && (userData.traineeProfile || userData.user)) {
          try {
            // Update Redis with user data
            await firstValueFrom(
              this.httpService
                .post(`${this.flaskApiUrl}/api/user/update-from-api?user_id=${userId}&channel=${channel}`, userData)
                .pipe(
                  catchError((error: AxiosError) => {
                    console.error('Error updating Redis with user data:', error.response?.data || error.message);
                    // Continue even if this fails
                    return of(null);
                  }),
                ),
            );
            console.log(`Updated Redis with user data for ${userId}`);
          } catch (updateError) {
            console.error('Error updating Redis with user data:', updateError);
            // Continue with the message processing even if the update fails
          }
        }
      } catch (userDataError) {
        console.error('Error getting user data:', userDataError);
        // Continue with the message processing even if getting user data fails
      }

      // If health check passes, proceed with the actual request
      console.log(`Sending message to ${this.flaskApiUrl}/api/chat:`, {
        input_data: message,
        user_id: userId,
        channel: channel
      });

      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/chat`, {
            input_data: message,
            user_id: userId,
            channel: channel
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling Flask API:', error.response?.data || error.message);

              // Provide more detailed error information
              const errorMessage = error.response?.data
                ? `AI service error: ${JSON.stringify(error.response.data)}`
                : `Failed to communicate with AI service: ${error.message}`;

              throw new HttpException(
                errorMessage,
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );

      console.log('Successfully received response from Flask API:', data);
      return data;
    } catch (error) {
      console.error('Error in sendChatMessage:', error);
      throw error;
    }
  }

  /**
   * Get the user's conversation history
   * @param userId The user ID
   * @param channel The channel (web or whatsapp). If not provided, defaults to web.
   * @returns The user's conversation history
   */
  async getConversationHistory(userId: string, channel: string = 'web') {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/api/memory/short-term?user_id=${userId}&channel=${channel}`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling Flask API:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to retrieve conversation history',
                error.response?.status || 500,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in getConversationHistory:', error);
      throw error;
    }
  }

  /**
   * Get the user's progress data
   * @param userId The user ID
   * @returns The user's progress data
   */
  async getUserProgress(userId: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/api/progress?user_id=${userId}`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling Flask API:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to retrieve user progress',
                error.response?.status || 500,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in getUserProgress:', error);
      throw error;
    }
  }

  /**
   * Generate a weekly summary for the user
   * @param userId The user ID
   * @returns The weekly summary
   */
  async generateWeeklySummary(userId: string): Promise<SummaryResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/progress/weekly-summary`, {
            user_id: userId,
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling Flask API:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to generate weekly summary',
                error.response?.status || 500,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in generateWeeklySummary:', error);
      throw error;
    }
  }

  /**
   * Check the health of the Flask API
   * @returns The health status
   */
  async checkHealth() {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/health`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Health check failed:', error.message);
              throw new HttpException(
                'AI service is not available. Health check failed.',
                HttpStatus.SERVICE_UNAVAILABLE,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in checkHealth:', error);
      throw error;
    }
  }

  /**
   * Test the Flask API with a simple message
   * @param message The message to send
   * @returns The response from the Flask API
   */
  async testMessage(message: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/test-message`, null, {
            params: { message },
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling test message endpoint:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to test message',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in testMessage:', error);
      throw error;
    }
  }

  /**
   * Classify a message
   * @param userId The user ID
   * @param message The message to classify
   * @returns The classification result
   */
  async classifyMessage(userId: string, message: string): Promise<ClassificationResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/classify`, {
            input_data: message,
            user_id: userId,
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling classify endpoint:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to classify message',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in classifyMessage:', error);
      throw error;
    }
  }

  /**
   * Send a message to the nutrition agent
   * @param userId The user ID
   * @param message The message to send
   * @param channel The channel (web or whatsapp). If not provided, defaults to web.
   * @returns The response from the nutrition agent
   */
  async sendToNutritionAgent(userId: string, message: string, channel: string = 'web'): Promise<AgentResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/agents/nutrition`, {
            input_data: message,
            user_id: userId,
            channel: channel
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling nutrition agent:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to communicate with nutrition agent',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in sendToNutritionAgent:', error);
      throw error;
    }
  }

  /**
   * Send a message to the social event agent
   * @param userId The user ID
   * @param message The message to send
   * @param channel The channel (web or whatsapp). If not provided, defaults to web.
   * @returns The response from the social event agent
   */
  async sendToSocialAgent(userId: string, message: string, channel: string = 'web'): Promise<AgentResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/agents/social`, {
            input_data: message,
            user_id: userId,
            channel: channel
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling social agent:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to communicate with social agent',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in sendToSocialAgent:', error);
      throw error;
    }
  }

  /**
   * Send a message to the general agent
   * @param userId The user ID
   * @param message The message to send
   * @param channel The channel (web or whatsapp). If not provided, defaults to web.
   * @returns The response from the general agent
   */
  async sendToGeneralAgent(userId: string, message: string, channel: string = 'web'): Promise<AgentResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/agents/general`, {
            input_data: message,
            user_id: userId,
            channel: channel
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling general agent:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to communicate with general agent',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in sendToGeneralAgent:', error);
      throw error;
    }
  }

  /**
   * Summarize the conversation
   * @param userId The user ID
   * @param channel The channel (web or whatsapp). If not provided, defaults to web.
   * @returns The summary
   */
  async summarizeConversation(userId: string, channel: string = 'web'): Promise<SummaryResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/memory/summarize?user_id=${userId}&channel=${channel}`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error calling summarize endpoint:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to summarize conversation',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in summarizeConversation:', error);
      throw error;
    }
  }

  /**
   * Schedule a checkup
   * @param userId The user ID
   * @param minutesFromNow Minutes from now to schedule the checkup
   * @param message The checkup message
   * @returns The checkup response
   */
  async scheduleCheckup(userId: string, minutesFromNow: number, message: string): Promise<CheckupResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .post(`${this.flaskApiUrl}/api/checkups/schedule`, {
            user_id: userId,
            minutes_from_now: minutesFromNow,
            message: message,
          })
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error scheduling checkup:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to schedule checkup',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in scheduleCheckup:', error);
      throw error;
    }
  }

  /**
   * Get scheduled checkups
   * @param userId The user ID
   * @param includeCompleted Whether to include completed checkups
   * @returns The checkups
   */
  async getCheckups(userId: string, includeCompleted: boolean = false): Promise<CheckupsListResponse> {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/api/checkups?user_id=${userId}&include_completed=${includeCompleted}`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error getting checkups:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to get checkups',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in getCheckups:', error);
      throw error;
    }
  }

  /**
   * Get the user state
   * @param userId The user ID
   * @returns The user state
   */
  async getUserState(userId: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/api/user/state?user_id=${userId}`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error getting user state:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to get user state',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in getUserState:', error);
      throw error;
    }
  }

  /**
   * Get the user profile
   * @param userId The user ID
   * @returns The user profile
   */
  async getUserProfile(userId: string) {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/api/user/profile?user_id=${userId}`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error getting user profile:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to get user profile',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in getUserProfile:', error);
      throw error;
    }
  }

  /**
   * Get the audit log
   * @param userId The user ID
   * @param limit The maximum number of entries to return
   * @returns The audit log
   */
  async getAuditLog(userId: string, limit: number = 50) {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/api/audit-log?user_id=${userId}&limit=${limit}`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error getting audit log:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to get audit log',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in getAuditLog:', error);
      throw error;
    }
  }

  /**
   * Run a quick test
   * @returns The test result
   */
  async runQuickTest() {
    try {
      const { data } = await firstValueFrom(
        this.httpService
          .get(`${this.flaskApiUrl}/api/test`)
          .pipe(
            catchError((error: AxiosError) => {
              console.error('Error running quick test:', error.response?.data || error.message);
              throw new HttpException(
                'Failed to run quick test',
                error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
              );
            }),
          ),
      );
      return data;
    } catch (error) {
      console.error('Error in runQuickTest:', error);
      throw error;
    }
  }
}