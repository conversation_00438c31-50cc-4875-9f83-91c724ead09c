import { Controller, Post, Get, Body, Query, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { AgentsService } from './agents.service';
import { JwtAuthGuard } from '../security/middleware/authGuard.middleware';
import {
  ChatMessageDto,
  ChatResponseDto,
  ConversationHistoryDto,
  ProgressDataDto,
  WeeklySummaryDto,
  CheckupRequestDto,
  ClassifyMessageDto,
  HealthCheckResponseDto,
  TestMessageResponseDto
} from './dto/agents.dto';

// Define the request type
interface RequestWithUser {
  user: {
    id: string;
    [key: string]: any;
  };
}

@ApiTags('agents')
@Controller('agents')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AgentsController {
  constructor(private readonly agentsService: AgentsService) {}

  @Post('chat')
  @ApiOperation({ summary: 'Send a message to the AI assistant' })
  @ApiResponse({ status: 200, description: 'Message processed successfully', type: ChatResponseDto })
  async sendChatMessage(@Body() chatMessageDto: ChatMessageDto, @Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.sendChatMessage(userId, chatMessageDto.message, chatMessageDto.channel);
  }

  @Get('conversation')
  @ApiOperation({ summary: 'Get the user\'s conversation history' })
  @ApiResponse({ status: 200, description: 'Conversation history retrieved successfully', type: ConversationHistoryDto })
  @ApiQuery({ name: 'channel', required: false, type: String, description: 'The channel (web or whatsapp)' })
  async getConversationHistory(@Request() req: RequestWithUser, @Query('channel') channel?: string) {
    const userId = req.user.id;
    return this.agentsService.getConversationHistory(userId, channel);
  }

  @Get('progress')
  @ApiOperation({ summary: 'Get the user\'s progress data' })
  @ApiResponse({ status: 200, description: 'Progress data retrieved successfully', type: ProgressDataDto })
  async getUserProgress(@Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.getUserProgress(userId);
  }

  @Post('weekly-summary')
  @ApiOperation({ summary: 'Generate a weekly summary for the user' })
  @ApiResponse({ status: 200, description: 'Weekly summary generated successfully', type: WeeklySummaryDto })
  async generateWeeklySummary(@Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.generateWeeklySummary(userId);
  }

  @Get('health')
  @ApiOperation({ summary: 'Check the health of the AI service' })
  @ApiResponse({ status: 200, description: 'Health check successful', type: HealthCheckResponseDto })
  async checkHealth() {
    return this.agentsService.checkHealth();
  }

  @Post('test-message')
  @ApiOperation({ summary: 'Test the AI service with a simple message' })
  @ApiResponse({ status: 200, description: 'Test successful', type: TestMessageResponseDto })
  async testMessage(@Body('message') message: string) {
    return this.agentsService.testMessage(message);
  }

  @Post('classify')
  @ApiOperation({ summary: 'Classify a message' })
  @ApiResponse({ status: 200, description: 'Classification successful' })
  async classifyMessage(@Body() dto: ClassifyMessageDto, @Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.classifyMessage(userId, dto.message);
  }

  @Post('agents/nutrition')
  @ApiOperation({ summary: 'Send a message to the nutrition agent' })
  @ApiResponse({ status: 200, description: 'Message sent successfully', type: ChatResponseDto })
  async sendToNutritionAgent(@Body() dto: ChatMessageDto, @Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.sendToNutritionAgent(userId, dto.message, dto.channel);
  }

  @Post('agents/social')
  @ApiOperation({ summary: 'Send a message to the social event agent' })
  @ApiResponse({ status: 200, description: 'Message sent successfully', type: ChatResponseDto })
  async sendToSocialAgent(@Body() dto: ChatMessageDto, @Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.sendToSocialAgent(userId, dto.message, dto.channel);
  }

  @Post('agents/general')
  @ApiOperation({ summary: 'Send a message to the general agent' })
  @ApiResponse({ status: 200, description: 'Message sent successfully', type: ChatResponseDto })
  async sendToGeneralAgent(@Body() dto: ChatMessageDto, @Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.sendToGeneralAgent(userId, dto.message, dto.channel);
  }

  @Post('memory/summarize')
  @ApiOperation({ summary: 'Summarize the conversation' })
  @ApiResponse({ status: 200, description: 'Summarization successful', type: WeeklySummaryDto })
  @ApiQuery({ name: 'channel', required: false, type: String, description: 'The channel (web or whatsapp)' })
  async summarizeConversation(@Request() req: RequestWithUser, @Query('channel') channel?: string) {
    const userId = req.user.id;
    return this.agentsService.summarizeConversation(userId, channel);
  }

  @Post('checkups/schedule')
  @ApiOperation({ summary: 'Schedule a checkup' })
  @ApiResponse({ status: 200, description: 'Checkup scheduled successfully' })
  async scheduleCheckup(@Body() dto: CheckupRequestDto, @Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.scheduleCheckup(userId, dto.minutes_from_now, dto.message);
  }

  @Get('checkups')
  @ApiOperation({ summary: 'Get scheduled checkups' })
  @ApiResponse({ status: 200, description: 'Checkups retrieved successfully' })
  @ApiQuery({ name: 'include_completed', required: false, type: Boolean })
  async getCheckups(@Request() req: RequestWithUser, @Query('include_completed') includeCompleted?: boolean) {
    const userId = req.user.id;
    return this.agentsService.getCheckups(userId, includeCompleted);
  }

  @Get('user/state')
  @ApiOperation({ summary: 'Get the user state' })
  @ApiResponse({ status: 200, description: 'User state retrieved successfully' })
  async getUserState(@Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.getUserState(userId);
  }

  @Get('user/profile')
  @ApiOperation({ summary: 'Get the user profile' })
  @ApiResponse({ status: 200, description: 'User profile retrieved successfully' })
  async getUserProfile(@Request() req: RequestWithUser) {
    const userId = req.user.id;
    return this.agentsService.getUserProfile(userId);
  }

  @Get('audit-log')
  @ApiOperation({ summary: 'Get the audit log' })
  @ApiResponse({ status: 200, description: 'Audit log retrieved successfully' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getAuditLog(@Request() req: RequestWithUser, @Query('limit') limit?: number) {
    const userId = req.user.id;
    return this.agentsService.getAuditLog(userId, limit);
  }

  @Get('test')
  @ApiOperation({ summary: 'Run a quick test' })
  @ApiResponse({ status: 200, description: 'Test successful' })
  async runQuickTest() {
    return this.agentsService.runQuickTest();
  }
}