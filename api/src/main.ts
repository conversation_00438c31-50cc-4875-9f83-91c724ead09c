import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { RuntimeExceptionFilter } from 'src/common/filters/runtimeException.filter';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { BootstrapService } from './bootstrap.service';
import * as passport from 'passport';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { ValidationPipe } from '@nestjs/common';

declare const module: any;

import { DataSource, DataSourceOptions } from 'typeorm';
import * as path from 'path';
import { NetworkService } from './network.service';

async function createDatabaseAndTables() {
  const requiredEnvVars = [
    'DB_HOST',
    'DB_PORT',
    'DB_USERNAME',
    'DB_PASSWORD',
    'DB_DATABASE',
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.error(`Missing environment variable: ${envVar}`);
      throw new Error(`Missing environment variable: ${envVar}`);
    }
  }

  const dbName = process.env.DB_DATABASE;

  const baseConnectionOptions: DataSourceOptions = {
    type: 'postgres',
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT, 10) || 5432,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: 'postgres', // Connect to default 'postgres' database initially
    synchronize: false, // Disable sync initially
  };

  const baseDataSource = new DataSource(baseConnectionOptions);

  try {
    await baseDataSource.initialize();
    console.log('Connection initialized (without database)');

    // Check if the database exists
    const dbExists = await baseDataSource.query(
      `SELECT 1 FROM pg_catalog.pg_database WHERE datname = $1`,
      [dbName],
    );

    if (dbExists.length === 0) {
      await baseDataSource.query(`CREATE DATABASE ${dbName}`);
      console.log(`Database ${dbName} created.`);
    } else {
      console.log(`Database ${dbName} already exists.`);
    }

    await baseDataSource.destroy();
    console.log('Base database connection closed.');

    // Connect to the newly created database
    const dbConnectionOptions: DataSourceOptions = {
      ...baseConnectionOptions,
      database: dbName,
      entities: [path.join(__dirname, './models/**/*.entity.{ts,js}')], // Adjust path as needed
      synchronize: true,
    };

    const dbDataSource = new DataSource(dbConnectionOptions);

    await dbDataSource.initialize();
    console.log('Database connection initialized (with tables).');

    console.log('All necessary tables created.');
  } catch (err) {
    console.error('Error during database and table creation:', err);
  }
}

async function bootstrap() {
  await createDatabaseAndTables();

  const app = await NestFactory.create(AppModule, {
    logger: ['log', 'error', 'debug', 'warn'],
  });

  const corsOptions: CorsOptions = {
    origin: [
      process.env.FRONTEND_URL,
      process.env.ADMIN_FRONTEND_URL,
      process.env.MOBILE_FRONTEND_URL,
    ],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
    credentials: true,
  };
  app.enableCors(corsOptions);

  app.useLogger(app.get(CustomLogger));

  app.useGlobalFilters(new RuntimeExceptionFilter(app.get(CustomLogger)));

  // Configure Express to parse URL-encoded form data BEFORE validation pipe
  // This is critical for Twilio webhooks which send form data
  app.use(require('body-parser').urlencoded({ extended: true }));

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      // Allow non-DTO objects to be processed (important for Twilio webhooks)
      whitelist: false,
      // Don't throw errors for unknown properties (important for Twilio webhooks)
      forbidNonWhitelisted: false,
      // Skip validation if empty (important for Twilio webhooks)
      skipMissingProperties: true,
      // Provide detailed validation errors
      validationError: { target: false, value: true },
    }),
  );

  app.use(passport.initialize());

  const options = new DocumentBuilder()
    .setTitle('App API')
    .setDescription('This API is for App.')
    .setVersion('1.0')
    .addBearerAuth() // Add this line to enable Bearer token support
    .build();

  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('api', app, document);

  const bootstrapService = app.get(BootstrapService);
  await bootstrapService.InitializeBootstrap();

  const PORT = parseInt(process.env.SERVER_PORT) || 4000;
  const networkService = app.get(NetworkService);
  const IP = networkService.getServerIp();
  if (IP) {
    await app.listen(PORT, IP);
    console.log(`Application is running on http://${IP}:${PORT}`);
  } else {
    await app.listen(PORT);
    console.log(`Application is running on http://localhost:${PORT}`);
  }
}

bootstrap();
