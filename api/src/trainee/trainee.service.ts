import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TrainerAssignmentEntity } from '../models/profiles/trainer-trainee-assignment.entity';
import {
  CreateTrainerAssignmentDto,
  UpdateTrainerAssignmentDto,
} from './dto/trainee.dto';
import { UserEntity } from '../models/user-entity/user.entity';
import { RoleEntity } from '../models/user-entity/role.entity';
import { FirebaseService } from '../third-party/firebase/firebase-authentication.service';
import * as bcrypt from 'bcrypt';
import { generateRandomPassword } from '../utils/password-generator';
import { OTPEntity, OTPEnum } from '../models/user-entity/otp.entity';
import { TraineeProfileEntity } from '../models/profiles/trainee-profile.entity';
import { VerificationEmailService } from 'src/common/email/send-verification-email.service';

@Injectable()
export class TrainerAssignmentsService {
  constructor(
    @InjectRepository(TrainerAssignmentEntity)
    private readonly trainerAssignmentsRepository: Repository<TrainerAssignmentEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<UserEntity>,

    @InjectRepository(TraineeProfileEntity)
    private readonly traineeProfileRepository: Repository<TraineeProfileEntity>,

    @InjectRepository(OTPEntity)
    private readonly otpRepo: Repository<OTPEntity>,

    private readonly firebaseService: FirebaseService,
    private readonly verificationEmailService: VerificationEmailService,
  ) {}

  async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }

  // Create OTP for password reset
  async createOtp(email: string): Promise<string> {
    try {
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const expiry = new Date(Date.now() + 15 * 60 * 1000);

      let existingOtp = await this.otpRepo.findOne({
        where: { email, otp_type: OTPEnum.FORGOTPASS, isDeleted: false },
      });

      if (!existingOtp) {
        existingOtp = this.otpRepo.create({
          email,
          otp_type: OTPEnum.FORGOTPASS,
          isDeleted: false,
        });
      }

      existingOtp.OTP = otp;
      existingOtp.expiry = expiry;

      await this.otpRepo.save(existingOtp);
      return otp;
    } catch (error) {
      throw error;
    }
  }

  async createAssignment(
    createDto: CreateTrainerAssignmentDto,
  ): Promise<{ trainer: any; trainee: any; startDate: Date | null }> {
    try {
      // Find the trainer by ID
      const trainer = await this.userRepository.findOne({
        where: { id: createDto.trainerId },
        relations: ['role'],
      });

      if (!trainer) {
        throw new NotFoundException('Trainer not found');
      }

      // Validate trainer role
      if (!trainer.role || trainer.role.value !== 'ROLE_TRAINER') {
        throw new BadRequestException(
          'Selected trainer is not a valid trainer',
        );
      }

      // Find the trainee by email
      let trainee = await this.userRepository.findOne({
        where: { email: createDto.trainee_email },
        relations: ['role'],
      });

      // If trainee doesn't exist, create a new user with a random password
      if (!trainee) {
        const randomPassword = generateRandomPassword(12);
        const firebaseUser = await this.firebaseService.createUser(
          createDto.trainee_email,
          randomPassword,
        );

        const defaultRole = await this.roleRepo.findOne({
          where: { name: 'Trainee' },
        });

        if (!defaultRole) {
          throw new BadRequestException(`Default role not found.`);
        }

        const passwordHash = await this.hashPassword(randomPassword);

        trainee = this.userRepository.create({
          id: firebaseUser.uid,
          email: firebaseUser.email,
          passwordHash,
          name: createDto.trainee_name || '',
          mobileNumber: createDto.trainee_mobileNumber || null,
          roleId: 2,
          isVerified: false,
          isActive: true,
        });

        trainee = await this.userRepository.save(trainee);

        // Create OTP for password reset
        const otp = await this.createOtp(createDto.trainee_email);

        // Send verification email with temporary password
        await this.verificationEmailService.sendVerificationEmail(
          createDto.trainee_email,
          randomPassword,
          otp,
        );
      }

      // Prevent self-assignment
      if (trainer.id === trainee.id) {
        throw new BadRequestException(
          'Trainer and trainee cannot be the same person',
        );
      }

      // Check if the assignment already exists
      const existingAssignment =
        await this.trainerAssignmentsRepository.findOne({
          where: { trainer: { id: trainer.id }, trainee: { id: trainee.id } },
        });

      if (existingAssignment) {
        throw new ConflictException(
          'This trainer is already assigned to this trainee',
        );
      }

      let traineeProfile = await this.traineeProfileRepository.findOne({
        where: { user: { id: trainee.id } },
      });

      if (!traineeProfile) {
        traineeProfile = new TraineeProfileEntity();

        traineeProfile.user = trainee;

        traineeProfile.age = createDto.trainee_age;
        traineeProfile.gender = createDto.trainee_gender;
        traineeProfile.height = createDto.trainee_height;
        traineeProfile.weight = createDto.trainee_weight;
        traineeProfile.bodyFatPercentage = createDto.trainee_bodyFatPercentage;
        traineeProfile.fitnessGoal = createDto.trainee_fitnessGoal;
        traineeProfile.activityLevel = createDto.trainee_activityLevel;
        traineeProfile.profileImageUrl =
          createDto.trainee_profileImageUrl || null;

        await this.traineeProfileRepository.save(traineeProfile);
      }

      // Create the assignment
      const assignment = this.trainerAssignmentsRepository.create({
        trainer,
        trainee,
        isActive: createDto.isActive ?? true,
        startDate: createDto.startDate ? new Date(createDto.startDate) : null,
        endDate: createDto.endDate ? new Date(createDto.endDate) : null,
        approved: true,
      });

      await this.trainerAssignmentsRepository.save(assignment);

      return {
        trainer,
        trainee,
        startDate: assignment.startDate,
      };
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Something went wrong while creating the assignment.',
      );
    }
  }

  async getAllAssignments(): Promise<TrainerAssignmentEntity[]> {
    return await this.trainerAssignmentsRepository.find();
  }

  async getAssignmentById(id: string): Promise<TrainerAssignmentEntity> {
    const assignment = await this.trainerAssignmentsRepository.findOne({
      where: { id },
    });
    if (!assignment) {
      throw new NotFoundException('Trainer assignment not found');
    }
    return assignment;
  }

  async getAssignmentsByTrainerId(
    trainerId: string,
  ): Promise<TrainerAssignmentEntity[]> {
    return await this.trainerAssignmentsRepository.find({
      where: { trainer: { id: trainerId } },
    });
  }

  async getAssignmentsByTraineeId(
    traineeId: string,
  ): Promise<TrainerAssignmentEntity[]> {
    return await this.trainerAssignmentsRepository.find({
      where: { trainee: { id: traineeId } },
    });
  }

  async updateAssignment(
    id: string,
    updateDto: UpdateTrainerAssignmentDto,
  ): Promise<{ message: string }> {
    const assignment = await this.trainerAssignmentsRepository.findOne({
      where: { id },
    });
    if (!assignment) {
      throw new NotFoundException('Trainer assignment not found');
    }

    await this.trainerAssignmentsRepository.update(id, updateDto);
    return { message: 'Trainer assignment updated successfully' };
  }

  async deleteAssignment(id: string): Promise<{ message: string }> {
    const result = await this.trainerAssignmentsRepository.delete(id);
    if (result.affected === 0) {
      throw new NotFoundException('Trainer assignment not found');
    }
    return { message: 'Trainer assignment deleted successfully' };
  }
}
