import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TrainerAssignmentsController } from './trainee.controller';
import { TrainerAssignmentsService } from './trainee.service';
import { TrainerAssignmentEntity } from '../models/profiles/trainer-trainee-assignment.entity';
import { UserEntity } from '../models/user-entity/user.entity';
import { JwtService } from '@nestjs/jwt';
import { RoleEntity } from '../models/user-entity/role.entity';
import { AccessTokenEntity } from '../models/user-entity/accessToken.entity';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { OTPEntity } from 'src/models/user-entity/otp.entity';
import { SesService } from 'src/third-party/aws/SES/ses.service';
import { TraineeProfileEntity } from 'src/models/profiles/trainee-profile.entity';
import { VerificationEmailService } from 'src/common/email/send-verification-email.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TrainerAssignmentEntity,
      UserEntity,
      AccessTokenEntity,
      RoleEntity,
      OTPEntity,
      TraineeProfileEntity,
    ]),
  ],
  controllers: [TrainerAssignmentsController],
  providers: [
    TrainerAssignmentsService,
    JwtService,
    FirebaseService,
    VerificationEmailService,
    SesService,
  ],
})
export class TraineeModule {}
