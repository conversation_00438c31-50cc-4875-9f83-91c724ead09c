import { IsBoolean, IsDateString, IsOptional, IsUUID, IsString, IsInt } from 'class-validator';
import { Type } from 'class-transformer';
import { FitnessGoal, ActivityLevel } from '../../models/profiles/trainee-profile.entity';

export class CreateTrainerAssignmentDto {
  @IsString()
  trainerId: string;

  @IsString()
  trainee_email: string;

  @IsString()
  trainee_name?: string;

  @IsOptional()
  @IsString()
  trainee_mobileNumber?: string;

  @IsOptional()
  @IsInt()
  @Type(() => Number)
  trainee_age?: number;

  @IsInt()
  @Type(() => Number)
  trainee_height?: number;

  @IsString()
  trainee_gender?: string;

  @IsString()
  trainee_level?: string;

  @IsInt()
  @Type(() => Number)
  trainee_weight?: number;

  @IsInt()
  trainee_bodyFatPercentage?: number;

  @IsString()
  trainee_fitnessGoal?: FitnessGoal;

  @IsString()
  trainee_activityLevel?: ActivityLevel;

  @IsString()
  trainee_profileImageUrl?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsBoolean()
  approved?: boolean;

  @IsOptional()
  @IsUUID()
  userId?: string;
}


export class UpdateTrainerAssignmentDto {
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsBoolean()
  approved?: boolean;
}