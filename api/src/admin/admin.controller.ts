import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpStatus,
  HttpCode,
  Query,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import {
  CreateTrainerDto,
  UpdateTrainerDto,
  SearchTrainersDto,
} from './dto/admin.dto';
import { JwtAuthGuard } from '../security/middleware/authGuard.middleware';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('admin')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/admin')
export class AdminController {
  constructor(private readonly trainerService: AdminService) {}

  // ──────────────── Trainers ────────────────

  @Get('trainers')
  @ApiTags('admin/trainers')
  @ApiOperation({ summary: 'Get all trainers' })
  @ApiResponse({
    status: 200,
    description: 'Returns all trainers with their profile information',
  })
  async findAllTrainers() {
    return {
      success: true,
      data: await this.trainerService.findAllTrainers(),
    };
  }

  @Post('trainer')
  @ApiTags('admin/trainers')
  @ApiOperation({ summary: 'Create a new trainer' })
  @ApiResponse({
    status: 201,
    description: 'The trainer has been successfully created',
  })
  @HttpCode(HttpStatus.CREATED)
  async createTrainer(@Body() createTrainerDto: CreateTrainerDto) {
    return {
      success: true,
      data: await this.trainerService.createTrainer(createTrainerDto),
    };
  }

  @Put('trainer/:id')
  @ApiTags('admin/trainers')
  @ApiOperation({ summary: 'Update a trainer' })
  @ApiResponse({
    status: 200,
    description: 'The trainer has been successfully updated',
  })
  async updateTrainer(
    @Param('id') id: string,
    @Body() updateTrainerDto: UpdateTrainerDto,
  ) {
    return {
      success: true,
      data: await this.trainerService.updateTrainer(id, updateTrainerDto),
    };
  }

  @Delete('trainer/:id')
  @ApiTags('admin/trainers')
  @ApiOperation({ summary: 'Delete a trainer' })
  @ApiResponse({
    status: 204,
    description: 'The trainer has been successfully deleted',
  })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteTrainer(@Param('id') id: string) {
    await this.trainerService.removeTrainer(id);
    return;
  }

  @Get('trainer/:id')
  @ApiTags('admin/trainers')
  @ApiOperation({ summary: 'Get a trainer by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns trainer with their profile information',
  })
  @ApiResponse({
    status: 404,
    description: 'Trainer not found',
  })
  async getTrainerById(@Param('id') id: string) {
    return {
      success: true,
      data: await this.trainerService.getTrainerById(id),
    };
  }

  @Get('trainers/search')
  @ApiTags('admin/trainers')
  @ApiOperation({ summary: 'Search trainers' })
  @ApiResponse({
    status: 200,
    description: 'Returns a list of trainers that match the search criteria',
  })
  async searchTrainers(@Query() searchParams: SearchTrainersDto) {
    return {
      success: true,
      data: await this.trainerService.searchTrainers(searchParams),
    };
  }
}
