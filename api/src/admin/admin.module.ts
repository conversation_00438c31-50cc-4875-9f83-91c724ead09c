import { Modu<PERSON> } from '@nestjs/common';
import { AdminController } from './admin.controller';
import { AdminService } from './admin.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AccessTokenEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import { TrainerProfileEntity } from 'src/models/profiles/trainer-profile.entity';
import { JwtService } from '@nestjs/jwt';
import { OTPEntity } from 'src/models/user-entity/otp.entity';
import { FirebaseService } from 'src/third-party/firebase/firebase-authentication.service';
import { SesService } from 'src/third-party/aws/SES/ses.service';
import { VerificationEmailService } from 'src/common/email/send-verification-email.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      RoleEntity,
      TrainerProfileEntity,
      AccessTokenEntity,
      OTPEntity,
    ]),
  ],
  controllers: [AdminController],
  providers: [
    AdminService,
    JwtService,
    FirebaseService,
    VerificationEmailService,
    SesService,
  ],
})
export class AdminModule {}
