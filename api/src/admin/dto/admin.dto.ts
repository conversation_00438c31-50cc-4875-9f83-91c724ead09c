import { IsEmail, IsNotEmpty, IsOptional, IsNumber, IsString, IsBoolean, IsEnum, IsArray, IsUrl } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TrainerSpecialization } from '../../models/profiles/trainer-profile.entity';
import { Type } from 'class-transformer';

export class CreateTrainerDto {
  @ApiProperty({ description: 'Unique ID for the user (Firebase UID)' })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({ description: 'Full name of the trainer' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Email address of the trainer' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiPropertyOptional({ description: 'Mobile number for WhatsApp communication' })
  @IsOptional()
  @IsString()
  mobileNumber?: string;

  @ApiProperty({ description: 'Trainer specializations', enum: TrainerSpecialization, isArray: true })
  @IsArray()
  @IsEnum(TrainerSpecialization, { each: true })
  @IsOptional()
  specializations?: TrainerSpecialization[];

  @ApiProperty({ description: 'Years of experience as a trainer' })
  @IsNumber()
  @IsOptional()
  yearsOfExperience?: number;

  @ApiProperty({ description: 'Trainer certifications (comma-separated)' })
  @IsString()
  @IsOptional()
  certifications?: string;

  @ApiProperty({ description: 'Trainer bio' })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiProperty({ description: 'Hourly rate for training sessions' })
  @IsNumber()
  @IsOptional()
  hourlyRate?: number;

  @ApiPropertyOptional({ description: 'URL to profile image' })
  @IsOptional()
  @IsUrl()
  @IsOptional()
  profileImageUrl?: string;

  @ApiProperty({ description: 'Trainer role', enum: ['junior_trainer', 'senior_trainer', 'nutrition_specialist'] })
  @IsOptional()
  @IsEnum(['junior_trainer', 'senior_trainer', 'nutrition_specialist'])
  trainerRole?: string;

}

export class UpdateTrainerDto {
  @ApiPropertyOptional({ description: 'Full name of the trainer' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Email address of the trainer' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'Mobile number for WhatsApp communication' })
  @IsOptional()
  @IsString()
  mobileNumber?: string;

  @ApiPropertyOptional({ description: 'Active status of the trainer' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'Trainer specializations', enum: TrainerSpecialization, isArray: true })
  @IsOptional()
  @IsArray()
  @IsEnum(TrainerSpecialization, { each: true })
  specializations?: TrainerSpecialization[];

  @ApiPropertyOptional({ description: 'Years of experience as a trainer' })
  @IsOptional()
  @IsNumber()
  yearsOfExperience?: number;

  @ApiPropertyOptional({ description: 'Trainer certifications (comma-separated)' })
  @IsOptional()
  @IsString()
  certifications?: string;

  @ApiPropertyOptional({ description: 'Trainer bio' })
  @IsOptional()
  @IsString()
  bio?: string;

  @ApiPropertyOptional({ description: 'Hourly rate for training sessions' })
  @IsOptional()
  @IsNumber()
  hourlyRate?: number;

  @ApiPropertyOptional({ description: 'URL to profile image' })
  @IsOptional()
  @IsUrl()
  profileImageUrl?: string;

  @ApiPropertyOptional({ description: 'Trainer role', enum: ['junior_trainer', 'senior_trainer', 'nutrition_specialist'] })
  @IsOptional()
  @IsEnum(['junior_trainer', 'senior_trainer', 'nutrition_specialist'])
  trainerRole?: string;
}


export class SearchTrainersDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  @IsOptional()
  @IsString()
  status?: 'active' | 'inactive' | 'pending';

  @IsOptional()
  @IsString()
  trainerRole?: 'senior_trainer' | 'junior_trainer' | 'nutrition_specialist';
}