import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, ILike } from 'typeorm';
import { UserEntity } from '../models/user-entity/user.entity';
import { TrainerProfileEntity } from '../models/profiles/trainer-profile.entity';
import { RoleEntity, ROLE_VALUES } from '../models/user-entity/role.entity';
import {
  CreateTrainerDto,
  UpdateTrainerDto,
  SearchTrainersDto,
} from './dto/admin.dto';
import { FirebaseService } from '../third-party/firebase/firebase-authentication.service';
import * as bcrypt from 'bcrypt';
import { generateRandomPassword } from '../utils/password-generator';
import { OTPEntity, OTPEnum } from '../models/user-entity/otp.entity';
import { VerificationEmailService } from 'src/common/email/send-verification-email.service';

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(UserEntity)
    private readonly userRepository: Repository<UserEntity>,
    @InjectRepository(TrainerProfileEntity)
    private readonly trainerProfileRepository: Repository<TrainerProfileEntity>,
    @InjectRepository(RoleEntity)
    private readonly roleRepository: Repository<RoleEntity>,
    @InjectRepository(OTPEntity)
    private readonly otpRepo: Repository<OTPEntity>,
    private readonly firebaseService: FirebaseService,
    private readonly verificationEmailService: VerificationEmailService,
  ) {}

  async findAllTrainers(): Promise<any[]> {
    // First, get the trainer role ID
    const trainerRole = await this.roleRepository.findOne({
      where: { value: ROLE_VALUES.TRAINER },
    });

    if (!trainerRole) {
      throw new NotFoundException('Trainer role not found');
    }

    // Find all users with trainer role
    const trainers = await this.userRepository.find({
      where: { roleId: trainerRole.id },
      relations: ['trainerProfile'],
    });

    return trainers.map((trainer) => ({
      id: trainer.id,
      name: trainer.name,
      email: trainer.email,
      mobileNumber: trainer.mobileNumber,
      avatar: trainer.trainerProfile?.profileImageUrl || null,
      trainerRole: trainer.trainerProfile?.trainerRole,
      status: trainer.isActive ? 'active' : 'inactive',
      specializations: trainer.trainerProfile?.specializations || [],
      yearsOfExperience: trainer.trainerProfile?.yearsOfExperience || 0,
      certifications: trainer.trainerProfile?.certifications || [],
      bio: trainer.trainerProfile?.bio || '',
      hourlyRate: trainer.trainerProfile?.hourlyRate || 0,
    }));
  }

  async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt(10);
    return bcrypt.hash(password, salt);
  }

  async createOtp(email: string): Promise<string> {
    try {
      const otp = Math.floor(100000 + Math.random() * 900000).toString();
      const expiry = new Date(Date.now() + 15 * 60 * 1000);

      let existingOtp = await this.otpRepo.findOne({
        where: { email, otp_type: OTPEnum.FORGOTPASS, isDeleted: false },
      });

      if (!existingOtp) {
        existingOtp = this.otpRepo.create({
          email,
          otp_type: OTPEnum.FORGOTPASS,
          isDeleted: false,
        });
      }

      existingOtp.OTP = otp;
      existingOtp.expiry = expiry;

      await this.otpRepo.save(existingOtp);
      return otp;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  async createTrainer(createTrainerDto: CreateTrainerDto): Promise<any> {
    try {
      // Find the trainer role
      const trainerRole = await this.roleRepository.findOne({
        where: { name: 'Trainer' },
      });

      if (!trainerRole) {
        throw new NotFoundException('Trainer role not found');
      }

      // Generate random password and create Firebase user
      const randomPassword = generateRandomPassword(12);
      const firebaseUser = await this.firebaseService.createUser(
        createTrainerDto.email,
        randomPassword,
      );

      // Hash the password
      const passwordHash = await this.hashPassword(randomPassword);

      // Create user entity
      const user = new UserEntity();
      user.id = firebaseUser.uid;
      user.email = firebaseUser.email;
      user.passwordHash = passwordHash;
      user.name = createTrainerDto.name;
      user.mobileNumber = createTrainerDto.mobileNumber;
      user.isActive = true;
      user.roleId = trainerRole.id;
      user.isVerified = false;

      // Create trainer profile
      const trainerProfile = new TrainerProfileEntity();
      trainerProfile.specializations = createTrainerDto.specializations;
      trainerProfile.yearsOfExperience = createTrainerDto.yearsOfExperience;
      trainerProfile.certifications = createTrainerDto.certifications;
      trainerProfile.bio = createTrainerDto.bio;
      trainerProfile.hourlyRate = createTrainerDto.hourlyRate;
      trainerProfile.profileImageUrl = createTrainerDto.profileImageUrl;
      trainerProfile.trainerRole =
        createTrainerDto.trainerRole || 'junior_trainer';

      // Link trainer profile to user
      user.trainerProfile = trainerProfile;

      // Save user with trainer profile
      const savedUser = await this.userRepository.save(user);

      // Create OTP for password reset
      const otp = await this.createOtp(createTrainerDto.email);

      // Send verification email with temporary password
      await this.verificationEmailService.sendVerificationEmail(
        createTrainerDto.email,
        randomPassword,
        otp,
      );

      return {
        id: savedUser.id,
        name: savedUser.name,
        email: savedUser.email,
        mobileNumber: savedUser.mobileNumber,
        avatar: savedUser.trainerProfile.profileImageUrl,
        role: savedUser.trainerProfile.trainerRole,
        status: savedUser.isActive ? 'active' : 'inactive',
        specializations: savedUser.trainerProfile.specializations,
        yearsOfExperience: savedUser.trainerProfile.yearsOfExperience,
        certifications: savedUser.trainerProfile.certifications,
        bio: savedUser.trainerProfile.bio,
        hourlyRate: savedUser.trainerProfile.hourlyRate,
      };
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Something went wrong while creating the trainer.',
      );
    }
  }

  async updateTrainer(
    id: string,
    updateTrainerDto: UpdateTrainerDto,
  ): Promise<any> {
    // Find the trainer user with profile
    const trainer = await this.userRepository.findOne({
      where: { id },
      relations: ['trainerProfile'],
    });

    if (!trainer || !trainer.trainerProfile) {
      throw new NotFoundException(`Trainer with ID ${id} not found`);
    }

    // Update user entity fields
    if (updateTrainerDto.name) trainer.name = updateTrainerDto.name;
    if (updateTrainerDto.email) trainer.email = updateTrainerDto.email;
    if (updateTrainerDto.mobileNumber !== undefined)
      trainer.mobileNumber = updateTrainerDto.mobileNumber;
    if (updateTrainerDto.isActive !== undefined)
      trainer.isActive = updateTrainerDto.isActive;

    // Update trainer profile fields
    if (updateTrainerDto.specializations)
      trainer.trainerProfile.specializations = updateTrainerDto.specializations;
    if (updateTrainerDto.yearsOfExperience)
      trainer.trainerProfile.yearsOfExperience =
        updateTrainerDto.yearsOfExperience;
    if (updateTrainerDto.certifications)
      trainer.trainerProfile.certifications = updateTrainerDto.certifications;
    if (updateTrainerDto.bio) trainer.trainerProfile.bio = updateTrainerDto.bio;
    if (updateTrainerDto.hourlyRate)
      trainer.trainerProfile.hourlyRate = updateTrainerDto.hourlyRate;
    if (updateTrainerDto.profileImageUrl)
      trainer.trainerProfile.profileImageUrl = updateTrainerDto.profileImageUrl;
    if (updateTrainerDto.trainerRole)
      trainer.trainerProfile.trainerRole = updateTrainerDto.trainerRole;

    // Save updated trainer
    const updatedTrainer = await this.userRepository.save(trainer);

    return {
      id: updatedTrainer.id,
      name: updatedTrainer.name,
      email: updatedTrainer.email,
      mobileNumber: updatedTrainer.mobileNumber,
      avatar: updatedTrainer.trainerProfile.profileImageUrl,
      trainerRole: updatedTrainer.trainerProfile.trainerRole,
      status: updatedTrainer.isActive ? 'active' : 'inactive',
      specializations: updatedTrainer.trainerProfile.specializations,
      yearsOfExperience: updatedTrainer.trainerProfile.yearsOfExperience,
      certifications: updatedTrainer.trainerProfile.certifications,
      bio: updatedTrainer.trainerProfile.bio,
      hourlyRate: updatedTrainer.trainerProfile.hourlyRate,
    };
  }

  async removeTrainer(id: string): Promise<void> {
    const trainer = await this.userRepository.findOne({
      where: { id },
      relations: ['trainerProfile'],
    });

    if (!trainer) {
      throw new NotFoundException(`Trainer with ID ${id} not found`);
    }

    // Delete the trainer profile first
    if (trainer.trainerProfile) {
      await this.firebaseService.deleteUser(id);
      await this.trainerProfileRepository.remove(trainer.trainerProfile);
    }

    // Delete the user entity
    await this.userRepository.remove(trainer);
  }

  async getTrainerById(id: string): Promise<any> {
    // Find the trainer user with profile
    const trainer = await this.userRepository.findOne({
      where: { id },
      relations: ['trainerProfile'],
    });

    if (!trainer || !trainer.trainerProfile) {
      throw new NotFoundException(`Trainer with ID ${id} not found`);
    }

    return {
      id: trainer.id,
      name: trainer.name,
      email: trainer.email,
      avatar: trainer.trainerProfile.profileImageUrl || null,
      trainerRole: trainer.trainerProfile.trainerRole,
      status: trainer.isActive ? 'active' : 'inactive',
      specializations: trainer.trainerProfile.specializations || [],
      yearsOfExperience: trainer.trainerProfile.yearsOfExperience || 0,
      certifications: trainer.trainerProfile.certifications || '',
      bio: trainer.trainerProfile.bio || '',
      hourlyRate: trainer.trainerProfile.hourlyRate || 0,
    };
  }

  async searchTrainers(
    searchParams: SearchTrainersDto,
  ): Promise<{ data: any[]; total: number }> {
    const { search, page = 1, limit = 10, status, trainerRole } = searchParams;
    const skip = (page - 1) * limit;

    // First, get the trainer role ID
    const trainerRoleEntity = await this.roleRepository.findOne({
      where: { value: ROLE_VALUES.TRAINER },
    });

    if (!trainerRoleEntity) {
      throw new NotFoundException('Trainer role not found');
    }

    // Build query conditions
    const whereConditions: any = { roleId: trainerRoleEntity.id };

    // Add search filter if provided
    if (search) {
      whereConditions.name = ILike(`%${search}%`);
    }

    // Add status filter if provided
    if (status) {
      whereConditions.isActive = status === 'active' ? true : false;
    }

    // Find trainers with pagination
    const [trainers, total] = await this.userRepository.findAndCount({
      where: whereConditions,
      relations: ['trainerProfile'],
      skip,
      take: limit,
      order: { name: 'ASC' },
    });

    // Filter by trainerRole if provided
    let result = trainers;
    if (trainerRole) {
      result = trainers.filter(
        (trainer) => trainer.trainerProfile?.trainerRole === trainerRole,
      );
    }

    // Format response
    const formattedTrainers = result.map((trainer) => ({
      id: trainer.id,
      name: trainer.name,
      email: trainer.email,
      mobileNumber: trainer.mobileNumber,
      avatar: trainer.trainerProfile?.profileImageUrl || null,
      trainerRole: trainer.trainerProfile?.trainerRole,
      status: trainer.isActive ? 'active' : 'inactive',
      specializations: trainer.trainerProfile?.specializations || [],
      yearsOfExperience: trainer.trainerProfile?.yearsOfExperience || 0,
      certifications: trainer.trainerProfile?.certifications || [],
      bio: trainer.trainerProfile?.bio || '',
      hourlyRate: trainer.trainerProfile?.hourlyRate || 0,
    }));

    return {
      data: formattedTrainers,
      total: trainerRole ? result.length : total,
    };
  }
}
