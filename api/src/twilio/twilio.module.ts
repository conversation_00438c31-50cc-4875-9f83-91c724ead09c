import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { TwilioController } from './twilio.controller';
import { TwilioService } from './twilio.service';
import { MessageObserverService } from './message-observer.service';
import { AgentsModule } from '../agents/agents.module';
import { AuthModule } from '../auth/auth.module';
import { TraineeProfileEntity } from '../models/profiles/trainee-profile.entity';
import { TrainingPlanEntity } from '../models/excercise/training-plans.entity';
import { MealEntity } from '../models/meals/meals.entity';
import { FoodItemsModule } from '../meals/food-items/food-items.module';
import { FoodItemEntity } from '../models/meals/food-items.entity';
import { ThirdPartyModule } from '../third-party/third-party.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TraineeProfileEntity,
      TrainingPlanEntity,
      MealEntity,
      FoodItemEntity,
    ]),
    HttpModule,
    ConfigModule,
    AgentsModule,
    AuthModule,
    FoodItemsModule,
    ThirdPartyModule,
  ],
  controllers: [TwilioController],
  providers: [TwilioService, MessageObserverService],
  exports: [TwilioService, MessageObserverService],
})
export class TwilioModule {}
