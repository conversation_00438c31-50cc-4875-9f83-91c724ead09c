import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class TwilioWebhookDto {
  // Standard fields
  @ApiProperty({
    description: 'The WhatsApp number that sent the message',
    example: 'whatsapp:+**********',
  })
  @IsString()
  @IsOptional()
  From?: string;

  @ApiProperty({
    description: 'The message content',
    example: 'Hello, I missed my second meal today',
  })
  @IsString()
  @IsOptional()
  Body?: string;

  @ApiProperty({
    description:
      'The WhatsApp number that received the message (your Twilio number)',
    example: 'whatsapp:+**********',
  })
  @IsString()
  @IsOptional()
  To?: string;

  @ApiProperty({
    description: 'The unique ID of the message',
    example: 'SM123456789',
  })
  @IsString()
  @IsOptional()
  MessageSid?: string;

  @ApiProperty({
    description: 'The account SID',
    example: 'AC123456789',
  })
  @IsString()
  @IsOptional()
  AccountSid?: string;

  // WhatsApp specific fields
  @ApiProperty({
    description: 'The WhatsApp ID of the sender',
    example: '**********',
  })
  @IsString()
  @IsOptional()
  WaId?: string;

  @ApiProperty({
    description: 'The profile name of the sender',
    example: 'John Doe',
  })
  @IsString()
  @IsOptional()
  ProfileName?: string;

  // Additional Twilio fields
  @ApiProperty({
    description: 'The SMS SID',
    example: 'SM123456789',
  })
  @IsString()
  @IsOptional()
  SmsSid?: string;

  @ApiProperty({
    description: 'The SMS message SID',
    example: 'SM123456789',
  })
  @IsString()
  @IsOptional()
  SmsMessageSid?: string;

  @ApiProperty({
    description: 'The number of media attachments',
    example: '0',
  })
  @IsString()
  @IsOptional()
  NumMedia?: string;

  @ApiProperty({
    description: 'The message type',
    example: 'text',
  })
  @IsString()
  @IsOptional()
  MessageType?: string;

  @ApiProperty({
    description: 'The SMS status',
    example: 'received',
  })
  @IsString()
  @IsOptional()
  SmsStatus?: string;

  @ApiProperty({
    description: 'The number of segments',
    example: '1',
  })
  @IsString()
  @IsOptional()
  NumSegments?: string;

  @ApiProperty({
    description: 'The number of referral media',
    example: '0',
  })
  @IsString()
  @IsOptional()
  ReferralNumMedia?: string;

  @ApiProperty({
    description: 'The API version',
    example: '2010-04-01',
  })
  @IsString()
  @IsOptional()
  ApiVersion?: string;

  // Allow any additional properties
  [key: string]: any;
}

export class SendWhatsAppMessageDto {
  @ApiProperty({
    description:
      'The WhatsApp number to send the message to (with or without whatsapp: prefix)',
    example: '+**********',
    required: true,
  })
  @IsString({ message: 'The "to" field must be a string' })
  @IsNotEmpty({ message: 'The "to" field is required and should not be empty' })
  to: string;

  @ApiProperty({
    description: 'The message to send',
    example: 'Your response from the AI assistant',
    required: true,
  })
  @IsString({ message: 'The "message" field must be a string' })
  @IsNotEmpty({
    message: 'The "message" field is required and should not be empty',
  })
  message: string;
}

export class TwilioResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Optional message about the operation',
    example: 'Message sent successfully',
  })
  @IsOptional()
  message?: string;
}
