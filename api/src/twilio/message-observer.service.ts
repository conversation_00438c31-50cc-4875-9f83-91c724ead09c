import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twilio } from 'twilio';

@Injectable()
export class MessageObserverService {
  private readonly twilio: Twilio;
  private readonly logger = new Logger(MessageObserverService.name);
  private activeProcessing: Map<
    string,
    {
      intervalId: NodeJS.Timeout;
      startTime: number;
      lastTypingTime: number;
    }
  > = new Map();

  constructor(private readonly configService: ConfigService) {
    // Initialize Twilio client
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');

    if (!accountSid || !authToken) {
      this.logger.warn('Twilio credentials not found in environment variables');
    }

    this.twilio = new Twilio(accountSid, authToken);
  }

  startObserving(phoneNumber: string): () => void {
    // Format the phone number
    const formattedNumber = this.formatPhoneNumber(phoneNumber);

    // Check if we're already observing this phone number
    if (this.activeProcessing.has(formattedNumber)) {
      this.logger.log(
        `Already observing message processing for ${formattedNumber}`,
      );
      return () => this.stopObserving(formattedNumber);
    }

    this.logger.log(
      `Starting to observe message processing for ${formattedNumber}`,
    );

    // Send the initial typing indicator
    this.sendTypingIndicator(formattedNumber);

    // Set up an interval to send typing indicators every 15 seconds
    const intervalId = setInterval(() => {
      const processingInfo = this.activeProcessing.get(formattedNumber);
      if (!processingInfo) return;

      // Calculate time since last typing indicator
      const now = Date.now();
      const timeSinceLastTyping = now - processingInfo.lastTypingTime;

      // Send a new typing indicator every 15 seconds
      if (timeSinceLastTyping >= 15000) {
        this.sendTypingIndicator(formattedNumber);

        // Update the last typing time
        this.activeProcessing.set(formattedNumber, {
          ...processingInfo,
          lastTypingTime: now,
        });
      }
    }, 5000); // Check every 5 seconds, but only send every 15 seconds

    // Store the interval ID and start time
    this.activeProcessing.set(formattedNumber, {
      intervalId,
      startTime: Date.now(),
      lastTypingTime: Date.now(),
    });

    // Return a cleanup function
    return () => this.stopObserving(formattedNumber);
  }

  stopObserving(phoneNumber: string): void {
    // Format the phone number
    const formattedNumber = this.formatPhoneNumber(phoneNumber);

    // Check if we're observing this phone number
    const processingInfo = this.activeProcessing.get(formattedNumber);
    if (!processingInfo) {
      this.logger.log(
        `Not observing message processing for ${formattedNumber}`,
      );
      return;
    }

    this.logger.log(`Stopping observation for ${formattedNumber}`);

    // Clear the interval
    clearInterval(processingInfo.intervalId);

    // Remove from the map
    this.activeProcessing.delete(formattedNumber);

    // Log the total processing time
    const totalTime = (Date.now() - processingInfo.startTime) / 1000;
    this.logger.log(
      `Total processing time for ${formattedNumber}: ${totalTime.toFixed(2)} seconds`,
    );
  }

  private async sendTypingIndicator(phoneNumber: string): Promise<void> {
    try {
      // Get the WhatsApp number from environment variables
      let whatsappNumber = this.configService.get<string>(
        'TWILIO_WHATSAPP_NUMBER',
      );

      if (!whatsappNumber) {
        this.logger.error(
          'TWILIO_WHATSAPP_NUMBER is not configured in environment variables',
        );
        return;
      }

      // Remove 'whatsapp:' prefix if it exists in the environment variable
      whatsappNumber = whatsappNumber.replace('whatsapp:', '');

      // Ensure the from number has a + if it doesn't already
      if (!whatsappNumber.startsWith('+')) {
        whatsappNumber = '+' + whatsappNumber;
      }

      this.logger.log(`Sending typing indicator to ${phoneNumber}`);

      // Create the message with the typing indicator
      const result = await this.twilio.messages.create({
        from: `whatsapp:${whatsappNumber}`,
        to: phoneNumber,
        body: 'מקליד...',
      });

      this.logger.log(
        `Typing indicator sent successfully to ${phoneNumber}, SID: ${result.sid}`,
      );
    } catch (error) {
      this.logger.error(`Error sending typing indicator: ${error.message}`);

      // Log detailed Twilio error information if available
      if (error.code) {
        this.logger.error(`Twilio error code: ${error.code}`);
      }
      if (error.moreInfo) {
        this.logger.error(`More info: ${error.moreInfo}`);
      }
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    let formattedNumber = phoneNumber;

    // First, ensure the number has a + if it doesn't already and isn't prefixed with whatsapp:
    if (
      !formattedNumber.startsWith('whatsapp:') &&
      !formattedNumber.startsWith('+')
    ) {
      formattedNumber = '+' + formattedNumber;
    }

    // Then add the whatsapp: prefix if it doesn't have it
    if (!formattedNumber.startsWith('whatsapp:')) {
      formattedNumber = `whatsapp:${formattedNumber}`;
    }

    return formattedNumber;
  }

  getActiveProcessing(): Map<
    string,
    { startTime: number; lastTypingTime: number }
  > {
    // Create a new map without the intervalId (for logging/debugging purposes)
    const result = new Map<
      string,
      { startTime: number; lastTypingTime: number }
    >();

    for (const [phoneNumber, info] of this.activeProcessing.entries()) {
      result.set(phoneNumber, {
        startTime: info.startTime,
        lastTypingTime: info.lastTypingTime,
      });
    }

    return result;
  }
}
