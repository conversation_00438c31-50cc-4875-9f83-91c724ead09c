import { BadRequestException, Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import * as fs from 'fs';

@Injectable()
export class FirebaseService {
  private firebaseApp: admin.app.App;

  constructor() {
    try {
      if (!admin.apps.length) {
        const serviceAccount = JSON.parse(
          fs.readFileSync(process.env.PERSONAL_FIREBASE_CREDENTIALS, 'utf8'),
        );

        this.firebaseApp = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });
      } else {
        this.firebaseApp = admin.app();
      }
    } catch (error) {
      console.error('Firebase initialization error:', error);
    }
  }

  async getUserUidByEmail(email: string): Promise<string | null> {
    try {
      const userRecord = await admin.auth().getUserByEmail(email);
      return userRecord.uid;
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        return null;
      }
      throw error;
    }
  }

  async signInWithEmailAndPassword(
    email: string,
    password: string,
  ): Promise<any> {
    try {
      const url = `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${process.env.PERSONAL_FIREBASE_API_KEY}`;

      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password,
          returnSecureToken: true,
        }),
      });

      if (!response.ok) {
        // Try to get more detailed error information
        const errorResponse = await response.json();
        console.error('Firebase Auth Error:', errorResponse);

        throw new Error('Invalid email or password.');
      }

      return await response.json();
    } catch (error) {
      console.error('Sign-in error:', error);
      throw error;
    }
  }

  async createCustomToken(uid: string): Promise<string> {
    return await admin.auth().createCustomToken(uid);
  }

  async verifyToken(token: string): Promise<admin.auth.DecodedIdToken> {
    return this.firebaseApp.auth().verifyIdToken(token);
  }

  async createUser(
    email: string,
    password: string,
  ): Promise<admin.auth.UserRecord> {
    try {
      return await admin.auth().createUser({
        email,
        password,
      });
    } catch (error) {
      console.error('User creation error:', error);
      throw error;
    }
  }

  async verifyGoogleToken(idToken: string) {
    try {
      // Verify the token using Firebase Admin SDK
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      return decodedToken;
    } catch (error) {
      throw new Error('Invalid Google token');
    }
  }

  async updateUserPassword(uid: string, newPassword: string): Promise<void> {
    try {
      await admin.auth().updateUser(uid, {
        password: newPassword,
      });
    } catch (error) {
      console.error('Error updating user password:', error);
      throw new Error('Failed to update password in Firebase');
    }
  }

  async deleteUser(uid: string): Promise<void> {
    try {
      await admin.auth().deleteUser(uid);
      console.log(`Successfully deleted user with UID: ${uid} from Firebase`);
    } catch (error) {
      console.error('Error deleting user from Firebase:', error);
      throw new Error(`Failed to delete user from Firebase: ${error.message}`);
    }
  }
}
