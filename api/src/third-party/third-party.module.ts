import { Module } from '@nestjs/common';
import { FirebaseService } from './firebase/firebase-authentication.service';
import { TwilioClientService } from './twilio/twilio-client.service';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [ConfigModule],
  providers: [FirebaseService, TwilioClientService],
  exports: [FirebaseService, TwilioClientService],
})
export class ThirdPartyModule {}
