import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twilio } from 'twilio';

@Injectable()
export class TwilioClientService {
  private readonly twilio: Twilio;
  private readonly logger = new Logger(TwilioClientService.name);

  constructor(private readonly configService: ConfigService) {
    // Initialize Twilio client
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');

    if (!accountSid || !authToken) {
      this.logger.warn('Twilio credentials not found in environment variables');
      // We'll initialize anyway but log a warning
    }

    this.twilio = new Twilio(accountSid, authToken);
  }

  /**
   * Get the Twilio client instance
   * @returns The Twilio client instance
   */
  getTwilioClient(): <PERSON>wi<PERSON> {
    return this.twilio;
  }

  /**
   * Send a WhatsApp message using Twilio
   * @param to The recipient's phone number
   * @param message The message content
   * @returns Promise resolving to the message SID
   */
  async sendWhatsAppMessage(to: string, message: string): Promise<string> {
    try {
      // Validate inputs with detailed error messages
      if (!to) {
        throw new Error('The "to" parameter is required and cannot be empty');
      }

      if (!message) {
        throw new Error(
          'The "message" parameter is required and cannot be empty',
        );
      }

      // Format the 'to' number as WhatsApp expects
      // Make sure it has the whatsapp: prefix and proper formatting
      let formattedTo = to;

      // First, ensure the number has a + if it doesn't already and isn't prefixed with whatsapp:
      if (
        !formattedTo.startsWith('whatsapp:') &&
        !formattedTo.startsWith('+')
      ) {
        formattedTo = '+' + formattedTo;
      }

      // Then add the whatsapp: prefix if it doesn't have it
      if (!formattedTo.startsWith('whatsapp:')) {
        formattedTo = `whatsapp:${formattedTo}`;
      }

      // Get the WhatsApp number from environment variables
      let whatsappNumber = this.configService.get<string>(
        'TWILIO_WHATSAPP_NUMBER',
      );

      if (!whatsappNumber) {
        this.logger.error(
          'TWILIO_WHATSAPP_NUMBER is not configured in environment variables',
        );
        throw new Error(
          'TWILIO_WHATSAPP_NUMBER is not configured in environment variables',
        );
      }

      // Remove 'whatsapp:' prefix if it exists in the environment variable
      whatsappNumber = whatsappNumber.replace('whatsapp:', '');

      // Ensure the from number has a + if it doesn't already
      if (!whatsappNumber.startsWith('+')) {
        whatsappNumber = '+' + whatsappNumber;
      }

      this.logger.log(
        `Sending message to ${formattedTo} from whatsapp:${whatsappNumber}`,
      );
      this.logger.log(
        `Message content: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`,
      );

      // Create the message
      const result = await this.twilio.messages.create({
        from: `whatsapp:${whatsappNumber}`,
        to: formattedTo,
        body: message,
      });

      this.logger.log(`Message sent successfully to ${to}, SID: ${result.sid}`);
      return result.sid;
    } catch (error) {
      this.logger.error(`Error sending WhatsApp message: ${error.message}`);

      // Log detailed Twilio error information if available
      if (error.code) {
        this.logger.error(`Twilio error code: ${error.code}`);
      }
      if (error.moreInfo) {
        this.logger.error(`More info: ${error.moreInfo}`);
      }

      // Log Twilio account information (without sensitive details)
      const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
      if (accountSid) {
        this.logger.log(
          `Using Twilio account: ${accountSid.substring(0, 8)}...`,
        );
      } else {
        this.logger.error('TWILIO_ACCOUNT_SID is not configured');
      }

      // Check if auth token is configured (don't log the actual token)
      if (!this.configService.get<string>('TWILIO_AUTH_TOKEN')) {
        this.logger.error('TWILIO_AUTH_TOKEN is not configured');
      }

      throw error;
    }
  }

  /**
   * Verify Twilio request signature
   * @param signature The X-Twilio-Signature header value
   * @param url The full URL of the request
   * @param params The request parameters
   * @returns Boolean indicating if the signature is valid
   */
  verifyTwilioSignature(signature: string, url: string, params: any): boolean {
    try {
      const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
      if (!authToken) {
        this.logger.warn(
          'Cannot verify Twilio signature: Auth token not configured',
        );
        return false;
      }

      const twilio = require('twilio');
      return twilio.validateRequest(authToken, signature, url, params);
    } catch (error) {
      this.logger.error(`Error verifying Twilio signature: ${error.message}`);
      return false;
    }
  }

  /**
   * Get account information from Twilio
   * @returns Promise resolving to the account information
   */
  async getAccountInfo() {
    try {
      const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
      if (!accountSid) {
        throw new Error('TWILIO_ACCOUNT_SID is not configured');
      }

      return await this.twilio.api.accounts(accountSid).fetch();
    } catch (error) {
      this.logger.error(`Error getting account info: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get messaging services from Twilio
   * @param limit The maximum number of services to return
   * @returns Promise resolving to the messaging services
   */
  async getMessagingServices(limit = 20) {
    try {
      return await this.twilio.messaging.v1.services.list({
        limit,
      });
    } catch (error) {
      this.logger.error(`Error getting messaging services: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get phone numbers from Twilio
   * @param limit The maximum number of phone numbers to return
   * @returns Promise resolving to the phone numbers
   */
  async getPhoneNumbers(limit = 20) {
    try {
      return await this.twilio.incomingPhoneNumbers.list({
        limit,
      });
    } catch (error) {
      this.logger.error(`Error getting phone numbers: ${error.message}`);
      throw error;
    }
  }
}
