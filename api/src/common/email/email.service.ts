import { Injectable } from '@nestjs/common';
import { CustomLogger } from '../logger/custom-logger.service';
import { SesService } from '../../third-party/aws/SES/ses.service';
import { sendTextMailInterface } from '../../utils/interfaces';

@Injectable()
export class EmailService {
  constructor(
    private readonly sesService: SesService,
    private readonly logger: CustomLogger,
  ) {}

  async sendTextMail({
    toEmail,
    fromEmail,
    subject,
    textBody,
    html,
  }: sendTextMailInterface) {
    try {
      await this.sesService.sendEmail(
        toEmail,
        fromEmail,
        subject,
        textBody,
        html,
      );
    } catch (error) {
      throw error;
    }
  }
}
