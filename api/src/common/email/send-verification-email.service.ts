import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SesService } from '../../third-party/aws/SES/ses.service';
import { SEND_VERIFICATION_EMAIL_TEMPLATE } from './email-templates';
import { SendVerificationEmailTextBody } from './email-textbodies';

@Injectable()
export class VerificationEmailService {
  constructor(
    private readonly sesService: SesService,
    private readonly configService: ConfigService,
  ) {}

  async sendVerificationEmail(
    email: string,
    tempPassword: string,
    otp: string,
  ): Promise<void> {
    try {
      if (!otp) {
        throw new BadRequestException('OTP is required for email verification');
      }

      const from = this.configService.get<string>('AWS_SES_EMAIL');
      const frontendUrl = this.configService.get<string>('FRONTEND_URL');
      const subject = 'Important: Secure Your Account Now';

      const verificationLink = `${frontendUrl}/reset-password?email=${encodeURIComponent(email)}`;

      const htmlBody = SEND_VERIFICATION_EMAIL_TEMPLATE.replace(
        '$$tempPassword',
        tempPassword,
      )
        .replace('$$otp', otp)
        .replace('$$verificationLink', verificationLink);

      const textBody = SendVerificationEmailTextBody.replace(
        '$$tempPassword',
        tempPassword,
      )
        .replace('$$otp', otp)
        .replace('$$verificationLink', verificationLink);

      await this.sesService.sendEmail(email, from, subject, textBody, htmlBody);
    } catch (error) {
      console.error(`Failed to send verification email: ${error.message}`);
      throw new BadRequestException('Failed to send verification email');
    }
  }
}
