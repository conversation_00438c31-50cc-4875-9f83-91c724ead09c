import { Modu<PERSON> } from '@nestjs/common';
import { LoggerModule } from './logger/logger.module';
import { FilterModule } from './filters/filter.module';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { EmailModule } from './email/email.module';

@Module({
  imports: [
    LoggerModule,
    FilterModule,
    ConfigModule,
    JwtModule.register({}),
    EmailModule,
  ],
  exports: [
    LoggerModule,
    FilterModule,
    ConfigModule,
    JwtModule.register({}),
    EmailModule,
  ],
})
export class CommonModule {}
