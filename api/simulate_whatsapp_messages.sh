#!/bin/bash

# Script to simulate 20 WhatsApp messages from a specific number
# This will call the Twilio webhook endpoint with simulated Twilio payloads

# Configuration
WEBHOOK_URL="http://localhost:5000/twilio/webhook"
WHATSAPP_NUMBER="919810728991"
MESSAGES=(
  "Hello, I'm testing the nutrition agent"
  "What should I eat for breakfast?"
  "I want to lose weight"
  "Can you suggest a healthy lunch?"
  "I'm going to a restaurant tonight"
  "What's a good pre-workout meal?"
  "I ate pizza yesterday"
  "How many calories should I consume daily?"
  "I need a meal plan for the week"
  "What's a good protein source for vegetarians?"
  "I'm feeling tired after my meals"
  "Can you suggest some healthy snacks?"
  "What should I eat after a workout?"
  "I'm having trouble sleeping"
  "What's your opinion on intermittent fasting?"
  "I need to gain muscle mass"
  "What's a good breakfast before a morning run?"
  "I'm allergic to nuts, what can I eat?"
  "Can you suggest a meal plan for muscle gain?"
  "What's my lunch meal?"
)

# Function to send a message
send_message() {
  local message="$1"
  local message_sid="SM$(openssl rand -hex 12)"
  
  echo "Sending message: $message"
  
  # Create a Twilio-like payload
  payload='{
    "SmsMessageSid":"'$message_sid'",
    "NumMedia":"0",
    "ProfileName":"Abhishek",
    "MessageType":"text",
    "SmsSid":"'$message_sid'",
    "WaId":"'$WHATSAPP_NUMBER'",
    "SmsStatus":"received",
    "Body":"'"$message"'",
    "To":"whatsapp:+***********",
    "NumSegments":"1",
    "ReferralNumMedia":"0",
    "MessageSid":"'$message_sid'",
    "AccountSid":"ACdf7c651c6ccf25fada28c0e4f86706c1",
    "From":"whatsapp:+'$WHATSAPP_NUMBER'",
    "ApiVersion":"2010-04-01"
  }'
  
  # Send the request to the webhook
  curl -X POST "$WEBHOOK_URL" \
    -H "Content-Type: application/json" \
    -d "$payload"
    
  echo -e "\n-----------------------------------\n"
  
  # Wait a bit between messages to avoid overwhelming the system
  sleep 2
}

# Send all messages
echo "Starting to send 20 messages from $WHATSAPP_NUMBER..."
for message in "${MESSAGES[@]}"; do
  send_message "$message"
done

echo "All messages sent. Now checking for summaries..."

# Wait a bit for processing to complete
sleep 5

# Check if a summary was created
echo "Checking for summaries for $WHATSAPP_NUMBER..."
curl -X GET "http://localhost:8000/api/memory/summaries?user_id=$WHATSAPP_NUMBER&channel=whatsapp"

echo -e "\n\nDone!"
